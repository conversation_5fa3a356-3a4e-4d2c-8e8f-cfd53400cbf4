package meteordevelopment.meteorclient.renderer.text;

public final class SystemFontFace$ConstantPool {
   public static double const_uDoGI9x1VWPMr8G = Double.longBitsToDouble(
      -5998482359403584878L ^ -8788250114463273714L ^ 6106793835936366935L ^ 4480643634540334795L
   );
   public static String const_2aedrW9aYtK7vMY = s4l81DES15(a3P7g6ZNry("ūųŪŲůīŶŪŧťţ"));
   public static String const_eO2i2kWN811Ulip = 0yqIreWxXH(a3P7g6ZNry("Ìðý¸ëñüý¸û÷ô÷ê¸þ÷ê¸ëùþý¸úô÷ûóë¶"));
   public static int const_FtYQj1V4tBQL70d = (int)(1086449965218440463L ^ -1086449965931578705L) ^ (int)(455621247 ^ -1974132103);
   public static String const_JVA8nXjVY9ai0cQ = Uy2ZGh9KFe(a3P7g6ZNry("̖̞͖̔̍́"));
   public static double const_PwtabdydeCyQ5Ya = Double.longBitsToDouble(
      6268253038408087639L ^ 6655698975319083084L ^ -391922671695114231L ^ -3472529910018334702L
   );
}
