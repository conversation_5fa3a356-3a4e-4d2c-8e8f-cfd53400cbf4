package meteordevelopment.meteorclient.mixin;

import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.events.entity.DropItemsEvent;
import meteordevelopment.meteorclient.events.entity.player.AfterAttackEntityEvent;
import meteordevelopment.meteorclient.events.entity.player.AttackEntityEvent;
import meteordevelopment.meteorclient.events.entity.player.BlockBreakingCooldownEvent;
import meteordevelopment.meteorclient.events.entity.player.BreakBlockEvent;
import meteordevelopment.meteorclient.events.entity.player.InteractBlockEvent;
import meteordevelopment.meteorclient.events.entity.player.InteractEntityEvent;
import meteordevelopment.meteorclient.events.entity.player.InteractItemEvent;
import meteordevelopment.meteorclient.events.entity.player.StartBreakingBlockEvent;
import meteordevelopment.meteorclient.mixininterface.IClientPlayerInteractionManager;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.misc.InventoryTweaks;
import meteordevelopment.meteorclient.systems.modules.player.BreakDelay;
import meteordevelopment.meteorclient.systems.modules.player.SpeedMine;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import net.minecraft.block.BlockState;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.network.ClientPlayerInteractionManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerActionC2SPacket.Action;
import net.minecraft.screen.PlayerScreenHandler;
import net.minecraft.screen.ScreenHandler;
import net.minecraft.screen.slot.Slot;
import net.minecraft.screen.slot.SlotActionType;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.world.BlockView;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.Redirect;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin({ClientPlayerInteractionManager.class})
public abstract class ClientPlayerInteractionManagerMixin implements IClientPlayerInteractionManager {
   @Shadow
   private int field_3716;
   @Shadow
   @Final
   private ClientPlayNetworkHandler field_3720;

   @Shadow
   protected abstract void method_2911();

   @Shadow
   public abstract void method_2906(int var1, int var2, int var3, SlotActionType var4, PlayerEntity var5);

   @Shadow
   public abstract boolean method_2899(BlockPos var1);

   @Inject(
      method = {"clickSlot"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void onClickSlot(int syncId, int slotId, int button, SlotActionType actionType, PlayerEntity player, CallbackInfo info) {
      if (actionType == SlotActionType.field_7795 && slotId >= 0 && slotId < player.field_7512.field_7761.size()) {
         if (MeteorClient.EVENT_BUS.post(DropItemsEvent.get(((Slot)player.field_7512.field_7761.get(slotId)).method_7677())).isCancelled()) {
            info.cancel();
         }
      } else if (slotId == -999 && MeteorClient.EVENT_BUS.post(DropItemsEvent.get(player.field_7512.method_34255())).isCancelled()) {
         info.cancel();
      }
   }

   @Inject(
      method = {"clickSlot"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void onClickArmorSlot(int syncId, int slotId, int button, SlotActionType actionType, PlayerEntity player, CallbackInfo ci) {
      if (Modules.get().get(InventoryTweaks.class).armorStorage()) {
         ScreenHandler screenHandler = player.field_7512;
         if (screenHandler instanceof PlayerScreenHandler && slotId >= 5 && slotId <= 8) {
            int armorSlot = 8 - slotId + 36;
            if (actionType == SlotActionType.field_7790 && !screenHandler.method_34255().method_7960()) {
               this.method_2906(syncId, 17, armorSlot, SlotActionType.field_7791, player);
               this.method_2906(syncId, 17, button, SlotActionType.field_7790, player);
               this.method_2906(syncId, 17, armorSlot, SlotActionType.field_7791, player);
               ci.cancel();
            } else if (actionType == SlotActionType.field_7791) {
               if (button >= 10) {
                  this.method_2906(syncId, 45, armorSlot, SlotActionType.field_7791, player);
                  ci.cancel();
               } else {
                  this.method_2906(syncId, 36 + button, armorSlot, SlotActionType.field_7791, player);
                  ci.cancel();
               }
            }
         }
      }
   }

   @Inject(
      method = {"attackBlock"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void onAttackBlock(BlockPos blockPos, Direction direction, CallbackInfoReturnable<Boolean> info) {
      if (MeteorClient.EVENT_BUS.post(StartBreakingBlockEvent.get(blockPos, direction)).isCancelled()) {
         info.cancel();
      } else {
         SpeedMine sm = Modules.get().get(SpeedMine.class);
         BlockState state = MeteorClient.mc.field_1687.method_8320(blockPos);
         if (!sm.instamine() || !sm.filter(state.method_26204())) {
            return;
         }

         if (state.method_26165(MeteorClient.mc.field_1724, MeteorClient.mc.field_1687, blockPos) > 0.5F) {
            this.method_2899(blockPos);
            this.field_3720.method_52787(new PlayerActionC2SPacket(Action.field_12968, blockPos, direction));
            this.field_3720.method_52787(new PlayerActionC2SPacket(Action.field_12973, blockPos, direction));
            info.setReturnValue(true);
         }
      }
   }

   @Inject(
      method = {"interactBlock"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void interactBlock(ClientPlayerEntity player, Hand hand, BlockHitResult hitResult, CallbackInfoReturnable<ActionResult> cir) {
      if (MeteorClient.EVENT_BUS.post(InteractBlockEvent.get(player.method_6047().method_7960() ? Hand.field_5810 : hand, hitResult)).isCancelled()) {
         cir.setReturnValue(ActionResult.field_5814);
      }
   }

   @Inject(
      method = {"attackEntity"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void onAttackEntity(PlayerEntity player, Entity target, CallbackInfo info) {
      if (MeteorClient.EVENT_BUS.post(AttackEntityEvent.get(target)).isCancelled()) {
         info.cancel();
      }
   }

   @Inject(
      method = {"attackEntity"},
      at = {@At("RETURN")}
   )
   private void afterAttackEntity(PlayerEntity player, Entity target, CallbackInfo info) {
      MeteorClient.EVENT_BUS.post(new AfterAttackEntityEvent());
   }

   @Inject(
      method = {"interactEntity"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void onInteractEntity(PlayerEntity player, Entity entity, Hand hand, CallbackInfoReturnable<ActionResult> info) {
      if (MeteorClient.EVENT_BUS.post(InteractEntityEvent.get(entity, hand)).isCancelled()) {
         info.setReturnValue(ActionResult.field_5814);
      }
   }

   @Inject(
      method = {"dropCreativeStack"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void onDropCreativeStack(ItemStack stack, CallbackInfo info) {
      if (MeteorClient.EVENT_BUS.post(DropItemsEvent.get(stack)).isCancelled()) {
         info.cancel();
      }
   }

   @Redirect(
      method = {"updateBlockBreakingProgress"},
      at = @At(
         value = "FIELD",
         target = "Lnet/minecraft/client/network/ClientPlayerInteractionManager;blockBreakingCooldown:I",
         opcode = 181,
         ordinal = 1
      )
   )
   private void creativeBreakDelayChange(ClientPlayerInteractionManager interactionManager, int value) {
      BlockBreakingCooldownEvent event = MeteorClient.EVENT_BUS.post(BlockBreakingCooldownEvent.get(value));
      this.field_3716 = event.cooldown;
   }

   @Redirect(
      method = {"updateBlockBreakingProgress"},
      at = @At(
         value = "FIELD",
         target = "Lnet/minecraft/client/network/ClientPlayerInteractionManager;blockBreakingCooldown:I",
         opcode = 181,
         ordinal = 2
      )
   )
   private void survivalBreakDelayChange(ClientPlayerInteractionManager interactionManager, int value) {
      BlockBreakingCooldownEvent event = MeteorClient.EVENT_BUS.post(BlockBreakingCooldownEvent.get(value));
      this.field_3716 = event.cooldown;
   }

   @Redirect(
      method = {"attackBlock"},
      at = @At(
         value = "FIELD",
         target = "Lnet/minecraft/client/network/ClientPlayerInteractionManager;blockBreakingCooldown:I",
         opcode = 181
      )
   )
   private void creativeBreakDelayChange2(ClientPlayerInteractionManager interactionManager, int value) {
      BlockBreakingCooldownEvent event = MeteorClient.EVENT_BUS.post(BlockBreakingCooldownEvent.get(value));
      this.field_3716 = event.cooldown;
   }

   @Redirect(
      method = {"method_41930"},
      at = @At(
         value = "INVOKE",
         target = "Lnet/minecraft/block/BlockState;calcBlockBreakingDelta(Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/world/BlockView;Lnet/minecraft/util/math/BlockPos;)F"
      )
   )
   private float deltaChange(BlockState blockState, PlayerEntity player, BlockView world, BlockPos pos) {
      float delta = blockState.method_26165(player, world, pos);
      if (Modules.get().get(BreakDelay.class).preventInstaBreak() && delta >= 1.0F) {
         BlockBreakingCooldownEvent event = MeteorClient.EVENT_BUS.post(BlockBreakingCooldownEvent.get(this.field_3716));
         this.field_3716 = event.cooldown;
         return 0.0F;
      } else {
         return delta;
      }
   }

   @Inject(
      method = {"breakBlock"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void onBreakBlock(BlockPos blockPos, CallbackInfoReturnable<Boolean> info) {
      if (MeteorClient.EVENT_BUS.post(BreakBlockEvent.get(blockPos)).isCancelled()) {
         info.setReturnValue(false);
      }
   }

   @Inject(
      method = {"interactItem"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void onInteractItem(PlayerEntity player, Hand hand, CallbackInfoReturnable<ActionResult> info) {
      InteractItemEvent event = MeteorClient.EVENT_BUS.post(InteractItemEvent.get(hand));
      if (event.toReturn != null) {
         info.setReturnValue(event.toReturn);
      }
   }

   @Inject(
      method = {"cancelBlockBreaking"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void onCancelBlockBreaking(CallbackInfo info) {
      if (BlockUtils.breaking) {
         info.cancel();
      }
   }

   @Override
   public void meteor$syncSelected() {
      this.method_2911();
   }
}
