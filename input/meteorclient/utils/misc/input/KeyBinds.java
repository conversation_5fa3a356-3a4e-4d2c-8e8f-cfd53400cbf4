package meteordevelopment.meteorclient.utils.misc.input;

import io.netty.handler.codec.socks.SocksCommonUtils$ConstantPool;
import java.util.Map;
import javassist.bytecode.analysis.Util$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.accounts.AccountInfoScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.AlignmentX$ConstantPool;
import meteordevelopment.meteorclient.mixin.KeyBindingAccessor;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil.Type;
import org.reflections.vfs.Vfs$DefaultUrlTypes$2$ConstantPool;

public class KeyBinds {
   private static final String CATEGORY = "Meteor Client";
   public static KeyBinding OPEN_GUI = new KeyBinding(
      AoPg463Ibj(g1Is6zlGF6(oa4L2NIeVA(Util$ConstantPool.const_ByZVeKQU0UqZ1gR))),
      Type.field_1668,
      344,
      AoPg463Ibj(g1Is6zlGF6(oa4L2NIeVA(Vfs$DefaultUrlTypes$2$ConstantPool.const_DG9hYFWnTLCrJ7g)))
   );
   public static KeyBinding OPEN_COMMANDS = new KeyBinding(
      AoPg463Ibj(g1Is6zlGF6(oa4L2NIeVA(AlignmentX$ConstantPool.const_Ww0JVrDAxH4La1n))),
      Type.field_1668,
      46,
      AoPg463Ibj(g1Is6zlGF6(oa4L2NIeVA(SocksCommonUtils$ConstantPool.const_ND36gJ1qbDoNYki)))
   );

   private KeyBinds() {
   }

   public static KeyBinding[] apply(KeyBinding[] binds) {
      Map<String, Integer> categories = KeyBindingAccessor.getCategoryOrderMap();
      int highest = 0;

      for (int i : categories.values()) {
         if (i > highest) {
            highest = i;
         }
      }

      categories.put(AoPg463Ibj(g1Is6zlGF6(oa4L2NIeVA(AccountInfoScreen$ConstantPool.const_vwWeFJFdrzoWSGI))), highest + 1);
      KeyBinding[] newBinds = new KeyBinding[binds.length + 2];
      System.arraycopy(binds, 0, newBinds, 0, binds.length);
      newBinds[binds.length] = OPEN_GUI;
      newBinds[binds.length + 1] = OPEN_COMMANDS;
      return newBinds;
   }
}
