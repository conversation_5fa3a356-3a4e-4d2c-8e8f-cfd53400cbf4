package meteordevelopment.meteorclient.utils.misc;

import baritone.api.BaritoneAPI;
import baritone.api.pathing.goals.Goal;
import baritone.api.process.IBaritoneProcess;
import de.florianmichael.waybackauthlib.WaybackAuthLib$Agent$ConstantPool;
import io.netty.handler.codec.socks.SocksCmdResponse$1$ConstantPool;
import io.netty.handler.codec.socks.SocksInitResponseDecoder$1$ConstantPool;
import io.netty.handler.codec.socks.SocksInitResponseDecoder$ConstantPool;
import io.netty.handler.codec.socks.SocksInitResponseDecoder$State$ConstantPool;
import io.netty.handler.codec.socksx.v4.Socks4CommandResponse$ConstantPool;
import io.netty.handler.codec.socksx.v4.Socks4ServerDecoder$1$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5InitialRequest$ConstantPool;
import io.netty.handler.proxy.ProxyHandler$LazyChannelPromise$ConstantPool;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javassist.CannotCompileException$ConstantPool;
import javassist.ClassPath$ConstantPool;
import javassist.CtClass$DelayedFileOutputStream$ConstantPool;
import javassist.CtClassType$ConstantPool;
import javassist.CtMethod$LongConstParameter$ConstantPool;
import javassist.CtNewConstructor$ConstantPool;
import javassist.JarDirClassPath$1$ConstantPool;
import javassist.bytecode.AnnotationsAttribute$Copier$ConstantPool;
import javassist.bytecode.AnnotationsAttribute$Parser$ConstantPool;
import javassist.bytecode.ClassFileWriter$ConstPoolWriter$ConstantPool;
import javassist.bytecode.CodeAnalyzer$ConstantPool;
import javassist.bytecode.CodeIterator$AlignmentException$ConstantPool;
import javassist.bytecode.LineNumberAttribute$ConstantPool;
import javassist.bytecode.MethodParametersAttribute$ConstantPool;
import javassist.bytecode.SignatureAttribute$ArrayType$ConstantPool;
import javassist.bytecode.SignatureAttribute$ObjectType$ConstantPool;
import javassist.bytecode.StackMap$SwitchShifter$ConstantPool;
import javassist.bytecode.StackMapTable$Copier$ConstantPool;
import javassist.bytecode.TypeAnnotationsAttribute$SubCopier$ConstantPool;
import javassist.bytecode.annotation.Annotation$ConstantPool;
import javassist.bytecode.annotation.CharMemberValue$ConstantPool;
import javassist.bytecode.annotation.MemberValueVisitor$ConstantPool;
import javassist.bytecode.annotation.NoSuchClassError$ConstantPool;
import javassist.bytecode.stackmap.TypedBlock$ConstantPool;
import javassist.compiler.CompileError$ConstantPool;
import javassist.compiler.Javac$CtFieldWithInit$ConstantPool;
import javassist.compiler.JvstCodeGen$ConstantPool;
import javassist.compiler.Lex$ConstantPool;
import javassist.compiler.TokenId$ConstantPool;
import javassist.compiler.ast.MethodDecl$ConstantPool;
import javassist.convert.TransformNewClass$ConstantPool;
import javassist.expr.ExprEditor$ConstantPool;
import javassist.expr.Handler$ConstantPool;
import javassist.scopedpool.ScopedClassPoolFactory$ConstantPool;
import javassist.scopedpool.ScopedClassPoolRepositoryImpl$ConstantPool;
import javassist.tools.Callback$ConstantPool;
import javassist.tools.reflect.Compiler$ConstantPool;
import javassist.tools.reflect.Loader$ConstantPool;
import javassist.tools.rmi.AppletServer$ConstantPool;
import javassist.tools.rmi.RemoteRef$ConstantPool;
import javassist.util.HotSwapAgent$ConstantPool;
import javassist.util.proxy.SecurityActions$6$ConstantPool;
import javax.annotation.RegEx$ConstantPool;
import javax.annotation.WillCloseWhenClosed$ConstantPool;
import meteordevelopment.discordipc.IPCUser$ConstantPool;
import meteordevelopment.meteorclient.Main$OperatingSystem$1$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.MeteorClient$ConstantPool;
import meteordevelopment.meteorclient.asm.transformers.GameRendererTransformer$ConstantPool;
import meteordevelopment.meteorclient.commands.arguments.PlayerListEntryArgumentType$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.DropCommand$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.RotationCommand$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.SpectateCommand$ConstantPool;
import meteordevelopment.meteorclient.events.Cancellable$ConstantPool;
import meteordevelopment.meteorclient.events.entity.DropItemsEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.AttackEntityEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.InteractBlockEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerTickMovementEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.ArmRenderEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.RenderBossBarEvent$BossSpacing$ConstantPool;
import meteordevelopment.meteorclient.events.render.RenderBossBarEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.TooltipDataEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.GuiTheme$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiDebugRenderer$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderOperation$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.packer.GuiTexture$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.ItemSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.PacketBoolSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.StorageBlockListSettingScreen$BlockEntityTypeInfo$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.Tabs$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.MacrosTab$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.PathManagerTab$PathManagerScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.input.WMeteorDropdown$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorMinus$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.CharFilter$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WMultiLabel$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WRoot$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WContainer$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WSection$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.input.WDropdown$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.input.WIntEdit$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.input.WTextBox$Renderer$ConstantPool;
import meteordevelopment.meteorclient.mixin.ClientPlayerInteractionManagerAccessor;
import meteordevelopment.meteorclient.mixin.MinecraftClientAccessor;
import meteordevelopment.meteorclient.mixininterface.ICamera$ConstantPool;
import meteordevelopment.meteorclient.pathing.BaritoneSettings$ConstantPool;
import meteordevelopment.meteorclient.pathing.BaritoneUtils;
import meteordevelopment.meteorclient.pathing.IPathManager$ISettings$ConstantPool;
import meteordevelopment.meteorclient.pathing.NopPathManager$NopSettings$ConstantPool;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.renderer.Renderer2D$ConstantPool;
import meteordevelopment.meteorclient.renderer.Shaders$ConstantPool;
import meteordevelopment.meteorclient.renderer.Texture$ConstantPool;
import meteordevelopment.meteorclient.renderer.Texture$Format$ConstantPool;
import meteordevelopment.meteorclient.settings.EnumSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.GenericSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.IntSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.StorageBlockListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.StringListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.StringSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.StringSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.AccountType$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.AllMod$1$1$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.UuidToProfileResponse$Property$ConstantPool;
import meteordevelopment.meteorclient.systems.config.Config;
import meteordevelopment.meteorclient.systems.friends.Friend$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.Hud$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.XAnchor$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.HoleHud$Facing$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.MeteorTextHud$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.ModuleInfosHud$ConstantPool;
import meteordevelopment.meteorclient.systems.macros.Macros$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.AutoAnvil$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoCity$SwitchMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.CrystalAura$SwingMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.CrystalAura$YawStepMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AotuPlaceStep$BlockType$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoPotion$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.GgboyAutoLog$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.GrimPacketMine$SwapMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.PalletBuilder$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.TridentExp$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.TridentExp$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.AutoReconnect$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.AutoRespawn$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.swarm.SwarmHost$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AutoJump$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AutoWalk$Direction$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AutoWalk$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.Scaffold$ListMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$StaticGroundListener$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoFish$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoMend$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.FastUse$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Breadcrumbs$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.HoleESP$Hole$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.LightOverlay$Spawn$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$Position$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.NoRender$BannerRenderMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.PopChams$GhostPlayer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Tracers$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Trajectories$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.TunnelESP$Context$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Zoom$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.Sphere2dMarker$Block$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.EndermanLook$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$Floor$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$MBPIterator$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$8$ConstantPool;
import meteordevelopment.meteorclient.utils.PreInit;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.entity.SortPriority$ConstantPool;
import meteordevelopment.meteorclient.utils.files.StreamUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.files.YZModules$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.input.KeyAction$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.text.TextUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.network.PacketUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.network.PacketUtils$PacketRegistry$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$NotebotMode$ConstantPool;
import meteordevelopment.meteorclient.utils.other.MemoryUtil$ConstantPool;
import meteordevelopment.meteorclient.utils.other.Snapper$ConstantPool;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.meteorclient.utils.player.ChatUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.TitleScreenCredits$Credit$ConstantPool;
import meteordevelopment.meteorclient.utils.render.RenderUtils$RenderBlock$ConstantPool;
import meteordevelopment.meteorclient.utils.render.postprocess.PostProcessShaders$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockIterator$Callback$ConstantPool;
import meteordevelopment.meteorclient.utils.world.Dimension;
import meteordevelopment.meteorclient.utils.world.Dir$1$ConstantPool;
import meteordevelopment.meteorclient.utils.world.TickRate;
import meteordevelopment.orbit.listeners.IListener$ConstantPool;
import meteordevelopment.starscript.Script;
import meteordevelopment.starscript.Section;
import meteordevelopment.starscript.StandardLib;
import meteordevelopment.starscript.Starscript;
import meteordevelopment.starscript.compiler.Compiler;
import meteordevelopment.starscript.compiler.Parser;
import meteordevelopment.starscript.utils.Error;
import meteordevelopment.starscript.utils.StarscriptError;
import meteordevelopment.starscript.value.Value;
import meteordevelopment.starscript.value.ValueMap;
import net.minecraft.SharedConstants;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.client.network.PlayerListEntry;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.effect.StatusEffect;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.ClientStatusC2SPacket;
import net.minecraft.network.packet.c2s.play.ClientStatusC2SPacket.Mode;
import net.minecraft.registry.Registries;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.registry.entry.RegistryEntry.Reference;
import net.minecraft.stat.Stat;
import net.minecraft.stat.Stats;
import net.minecraft.util.Identifier;
import net.minecraft.util.InvalidIdentifierException;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult.Type;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos.Mutable;
import net.minecraft.world.biome.Biome;
import org.apache.commons.lang3.StringUtils;
import org.reflections.ReflectionUtils$1$ConstantPool;
import org.reflections.scanners.Scanners$2$ConstantPool;
import org.reflections.scanners.TypeAnnotationsScanner$ConstantPool;
import org.reflections.vfs.Vfs$DefaultUrlTypes$1$ConstantPool;
import org.reflections.vfs.Vfs$DefaultUrlTypes$7$ConstantPool;
import org.reflections.vfs.Vfs$File$ConstantPool;
import org.reflections.vfs.Vfs$UrlType$ConstantPool;
import org.reflections.vfs.ZipDir$ConstantPool;
import org.reflections.vfs.ZipFile$ConstantPool;

public class MeteorStarscript {
   public static Starscript ss = new Starscript();
   private static final Mutable BP = new Mutable();
   private static final StringBuilder SB = new StringBuilder();
   private static long lastRequestedStatsTime = 0L;

   @PreInit(
      dependencies = {PathManagers.class}
   )
   public static void init() {
      StandardLib.init(ss);
      ss.set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ModuleInfosHud$ConstantPool.const_7Sq8S51vBWAIr7t))), SharedConstants.method_16673().method_48019());
      ss.set(
         gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Main$OperatingSystem$1$ConstantPool.const_vJrm1XNrSwoUoiw))), () -> Value.number(MinecraftClientAccessor.getFps())
      );
      ss.set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(BlockIterator$Callback$ConstantPool.const_8qvxK2qOegBFPBq))), MeteorStarscript::ping);
      ss.set(
         gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Texture$Format$ConstantPool.const_QjfqkvtcJD6r85I))),
         () -> Value.string(LocalTime.now().format(DateTimeFormatter.ofLocalizedTime(FormatStyle.SHORT)))
      );
      ss.set(
         gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SocksInitResponseDecoder$State$ConstantPool.const_f0jvGrPOaNGBQYR))), () -> Value.number(CPSUtils.getCpsAverage())
      );
      ss.set(
         gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoPotion$ConstantPool.const_FGjdfwi1AJhaX9L))),
         new ValueMap()
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoFish$ConstantPool.const_VXqnLjoLyqnz2Fj))), MeteorClient.NAME)
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TridentExp$Mode$ConstantPool.const_VS8RlSOlHkcJiLi))),
               MeteorClient.VERSION != null
                  ? (MeteorClient.DEV_BUILD.isEmpty() ? MeteorClient.VERSION.toString() : MeteorClient.VERSION + " " + MeteorClient.DEV_BUILD)
                  : gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ItemSettingScreen$ConstantPool.const_BXz1lynTVBGM2cO)))
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoReconnect$ConstantPool.const_iByYewioAwHtdrx))), () -> Value.number(Modules.get().getAll().size()))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Vfs$UrlType$ConstantPool.const_4Sk9bFTvJUHwW6Y))), () -> Value.number(Modules.get().getActive().size()))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PostProcessShaders$ConstantPool.const_FIW29jZCIAzvnVq))), MeteorStarscript::isModuleActive)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PacketUtils$PacketRegistry$ConstantPool.const_D87MlKDqJmW90BF))), MeteorStarscript::getModuleInfo)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Vfs$DefaultUrlTypes$1$ConstantPool.const_KAttjOiiGZgffwr))), MeteorStarscript::getModuleSetting)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoWalk$Mode$ConstantPool.const_3oKzFOfbn2luYoH))), MeteorStarscript::getMeteorPrefix)
      );
      if (BaritoneUtils.IS_AVAILABLE) {
         ss.set(
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(HighwayBuilder$MBPIterator$ConstantPool.const_zlQMuyQzsjtBUat))),
            new ValueMap()
               .set(
                  gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoWalk$Mode$ConstantPool.const_TgyVQQyXCqrCimd))),
                  () -> Value.bool(BaritoneAPI.getProvider().getPrimaryBaritone().getPathingBehavior().isPathing())
               )
               .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Javac$CtFieldWithInit$ConstantPool.const_OZl2nnrjV8bfgfl))), MeteorStarscript::baritoneDistanceToGoal)
               .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoMend$ConstantPool.const_084rSmlYm0RE0AQ))), MeteorStarscript::baritoneProcess)
               .set(
                  gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(UuidToProfileResponse$Property$ConstantPool.const_8IFwxkavFZUi4HB))), MeteorStarscript::baritoneProcessName
               )
               .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Dir$1$ConstantPool.const_EymgDNe4YLWgYbG))), MeteorStarscript::baritoneETA)
         );
      }

      ss.set(
         gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CtClass$DelayedFileOutputStream$ConstantPool.const_AHLnVzHSNGYgBeQ))),
         new ValueMap()
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(EndermanLook$Mode$ConstantPool.const_i4QwWleACttLyhj))),
               new ValueMap()
                  .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GameRendererTransformer$ConstantPool.const_aWiMIkITLsDJLIG))), () -> posString(false, true))
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CtMethod$LongConstParameter$ConstantPool.const_oemBDFdA4eyxuD9))),
                     () -> Value.number(MeteorClient.mc.field_1773.method_19418().method_19326().field_1352)
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Shaders$ConstantPool.const_Sn2CQPvgPcQTSyZ))),
                     () -> Value.number(MeteorClient.mc.field_1773.method_19418().method_19326().field_1351)
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GuiDebugRenderer$ConstantPool.const_Gs54AaOzYgyiU79))),
                     () -> Value.number(MeteorClient.mc.field_1773.method_19418().method_19326().field_1350)
                  )
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WaybackAuthLib$Agent$ConstantPool.const_vjTBGyFKQeOz4tS))),
               new ValueMap()
                  .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Compiler$ConstantPool.const_2ZOJZxSj0iKgKj8))), () -> posString(true, true))
                  .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TridentExp$ConstantPool.const_BFD6BfTbGuGlh7S))), () -> oppositeX(true))
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PacketUtils$ConstantPool.const_6AboIS9bqAmawa0))),
                     () -> Value.number(MeteorClient.mc.field_1773.method_19418().method_19326().field_1351)
                  )
                  .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Callback$ConstantPool.const_ecIjW2NWn29yyj7))), () -> oppositeZ(true))
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(MeteorTextHud$ConstantPool.const_twmwybLDYRQlTcN))), () -> yaw(true))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WSection$ConstantPool.const_qSrA6Ay1tdIG11Q))), () -> pitch(true))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ProxyHandler$LazyChannelPromise$ConstantPool.const_StdiUVBDYpHrTN3))), () -> direction(true))
      );
      ss.set(
         gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TitleScreenCredits$Credit$ConstantPool.const_bQ7rllw5BwY6ipF))),
         new ValueMap()
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(StackMap$SwitchShifter$ConstantPool.const_l5a9blZyIR0vhGA))),
               () -> Value.string(MeteorClient.mc.method_1548().method_1676())
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TokenId$ConstantPool.const_XrTANd7l4XTeShY))),
               () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_6032() : 0.0)
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CompileError$ConstantPool.const_bEwqaZSq1yvFagi))),
               () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_6067() : 0.0)
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Nametags$1$ConstantPool.const_334KtNYzND45oAY))),
               () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_7344().method_7586() : 0.0)
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ElytraFly$ConstantPool.const_Gn2i9sw3LQaIRjD))), () -> Value.number(Utils.getPlayerSpeed().method_37267()))
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AnnotationsAttribute$Parser$ConstantPool.const_FykyhrjWgVAdtb0))),
               new ValueMap()
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Socks4ServerDecoder$1$ConstantPool.const_icBYDH2crdf6VMr))),
                     () -> Value.string(
                        MeteorClient.mc.field_1724 != null
                           ? Utils.getPlayerSpeed().toString()
                           : gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(JvstCodeGen$ConstantPool.const_w93hdQaP2Nlp08n)))
                     )
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PlayerListEntryArgumentType$ConstantPool.const_4JnWbJGGoIoJwjt))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? Utils.getPlayerSpeed().field_1352 : 0.0)
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SignatureAttribute$ObjectType$ConstantPool.const_cBA934mqdtVYWkQ))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? Utils.getPlayerSpeed().field_1351 : 0.0)
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoWalk$Direction$ConstantPool.const_gWwE6qFYpKZRLoX))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? Utils.getPlayerSpeed().field_1350 : 0.0)
                  )
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(HighwayBuilder$Floor$ConstantPool.const_NYa3CEAaWEX5qOr))),
               () -> Value.number(
                  MeteorClient.mc.field_1761 != null ? ((ClientPlayerInteractionManagerAccessor)MeteorClient.mc.field_1761).getBreakingProgress() : 0.0
               )
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WContainer$ConstantPool.const_RjBFySme7iyUku5))), MeteorStarscript::biome)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(EnumSetting$ConstantPool.const_yVwWOlcaYG80nGj))), () -> Value.string(PlayerUtils.getDimension().name()))
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WIntEdit$ConstantPool.const_abO7FC12j6anlNb))),
               () -> Value.string(PlayerUtils.getDimension().opposite().name())
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(IPathManager$ISettings$ConstantPool.const_bQLvoQtfJjJeFYW))),
               () -> PlayerUtils.getGameMode() != null ? Value.string(StringUtils.capitalize(PlayerUtils.getGameMode().method_8381())) : Value.null_()
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CtNewConstructor$ConstantPool.const_ioDV1d9bIV1aG69))),
               new ValueMap()
                  .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(RemoteRef$ConstantPool.const_FmiI6dq837oN9VU))), () -> posString(false, false))
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(StringSetting$ConstantPool.const_nRpZeMuGU3za14V))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_23317() : 0.0)
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GuiTexture$ConstantPool.const_gw66fBbVVW1WHlJ))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_23318() : 0.0)
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AnnotationsAttribute$Copier$ConstantPool.const_vgiKBWnb9VTtJ8O))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_23321() : 0.0)
                  )
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WaybackAuthLib$Agent$ConstantPool.const_bsWJo0J31YgQBkZ))),
               new ValueMap()
                  .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CannotCompileException$ConstantPool.const_lfQvrENSAsBFnGT))), () -> posString(true, false))
                  .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WMeteorMinus$ConstantPool.const_dg7Bi0rgF5IoJuY))), () -> oppositeX(false))
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(NopPathManager$NopSettings$ConstantPool.const_Y4pVJJwOY5l6DNp))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_23318() : 0.0)
                  )
                  .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TypeAnnotationsAttribute$SubCopier$ConstantPool.const_gt8Vu9ofGThtlC4))), () -> oppositeZ(false))
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ZipFile$ConstantPool.const_gjdrmIJSRUvCKqr))), () -> yaw(false))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GrimPacketMine$SwapMode$ConstantPool.const_2C122TWJ1vzB1SI))), () -> pitch(false))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Handler$ConstantPool.const_NWwNlSa2OL7vY6I))), () -> direction(false))
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CharFilter$ConstantPool.const_74hVJ9q9Soi52yb))),
               () -> MeteorClient.mc.field_1724 != null ? wrap(MeteorClient.mc.field_1724.method_6047()) : Value.null_()
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoCity$SwitchMode$ConstantPool.const_mRVDWF1Do3FU3bQ))),
               () -> MeteorClient.mc.field_1724 != null ? wrap(MeteorClient.mc.field_1724.method_6079()) : Value.null_()
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ExprEditor$ConstantPool.const_94cIooxkKaeSrYR))), MeteorStarscript::handOrOffhand)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(MacrosTab$ConstantPool.const_plKQsVxZUafdeqv))), MeteorStarscript::getItem)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(RotationCommand$ConstantPool.const_SkTEBSa8LTRNSkx))), MeteorStarscript::countItems)
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SocksInitResponseDecoder$1$ConstantPool.const_W6rmLPZjDFHLahJ))),
               new ValueMap()
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Texture$ConstantPool.const_I4LxdmRA0zGtfSj))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.field_7520 : 0.0)
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WillCloseWhenClosed$ConstantPool.const_ng7eA1p9orLSDbf))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.field_7510 : 0.0)
                  )
                  .set(
                     gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(RegEx$ConstantPool.const_7au00e7FNyd2fTG))),
                     () -> Value.number(MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.field_7495 : 0.0)
                  )
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Socks4CommandResponse$ConstantPool.const_553diChnD0mbYMV))), MeteorStarscript::hasPotionEffect)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SocksInitResponseDecoder$ConstantPool.const_6Fl2jOOadIOJnzL))), MeteorStarscript::getPotionEffect)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoAnvil$ConstantPool.const_zdpScmJYOnRDsqv))), MeteorStarscript::getStat)
      );
      ss.set(
         gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(StorageBlockListSettingScreen$BlockEntityTypeInfo$ConstantPool.const_ZqFbbjAArSGmKg0))),
         new ValueMap()
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(MethodParametersAttribute$ConstantPool.const_opp7DfEJWhxOygg))), MeteorStarscript::crosshairType)
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SortPriority$ConstantPool.const_Dymr59LaCjkjv8a))), MeteorStarscript::crosshairValue)
      );
      ss.set(
         gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Vfs$DefaultUrlTypes$1$ConstantPool.const_nS6bHF6Ioplbl8n))),
         new ValueMap()
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WillCloseWhenClosed$ConstantPool.const_6kPwHp2JI8NzVWo))), () -> Value.string(Utils.getWorldName()))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Loader$ConstantPool.const_Mn4OEBs58lW1Om1))), () -> Value.number(TickRate.INSTANCE.getTickRate()))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Lex$ConstantPool.const_AacmiSljTlvl41M))), () -> Value.string(Utils.getWorldTime()))
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PopChams$GhostPlayer$ConstantPool.const_MLSvbdUlfSNOJar))),
               () -> Value.number(MeteorClient.mc.method_1562() != null ? MeteorClient.mc.method_1562().method_2880().size() : 0.0)
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Snapper$ConstantPool.const_QHTIo7D5oSioqFa))),
               () -> Value.string(
                  MeteorClient.mc.field_1687 != null
                     ? MeteorClient.mc.field_1687.method_8407().method_5460()
                     : gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(StorageBlockListSetting$ConstantPool.const_ZLVWhyzo5YtoNjp)))
               )
            )
      );
   }

   public static Script compile(String source) {
      Parser.Result result = Parser.parse(source);
      if (!result.hasErrors()) {
         return Compiler.compile(result);
      } else {
         for (Error error : result.errors) {
            printChatError(error);
         }

         return null;
      }
   }

   public static Section runSection(Script script, StringBuilder sb) {
      try {
         return ss.run(script, sb);
      } catch (StarscriptError var3) {
         printChatError(var3);
         return null;
      }
   }

   public static String run(Script script, StringBuilder sb) {
      Section section = runSection(script, sb);
      return section != null ? section.toString() : null;
   }

   public static Section runSection(Script script) {
      return runSection(script, new StringBuilder());
   }

   public static String run(Script script) {
      return run(script, new StringBuilder());
   }

   public static void printChatError(int i, Error error) {
      String caller = getCallerName();
      if (caller != null) {
         if (i != -1) {
            ChatUtils.errorPrefix(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AppletServer$ConstantPool.const_b6iOaM4JxrnHseE))),
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GgboyAutoLog$ConstantPool.const_76IdD6wJyGpkh6W))),
               i,
               error.character,
               error.ch,
               error.message,
               caller
            );
         } else {
            ChatUtils.errorPrefix(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SocksInitResponseDecoder$ConstantPool.const_pwbWijYooBaFuzl))),
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(IntSetting$ConstantPool.const_A2II4uw9MDIWqFj))),
               error.character,
               error.ch,
               error.message,
               caller
            );
         }
      } else if (i != -1) {
         ChatUtils.errorPrefix(
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(HoleHud$Facing$ConstantPool.const_jjtDzbFFUNvvvoJ))),
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SignatureAttribute$ArrayType$ConstantPool.const_OFDI6Z9L999owbq))),
            i,
            error.character,
            error.ch,
            error.message
         );
      } else {
         ChatUtils.errorPrefix(
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(KeyAction$ConstantPool.const_W2QN4LuuDVAARJw))),
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Renderer2D$ConstantPool.const_BJQr5FclL7XbVtV))),
            error.character,
            error.ch,
            error.message
         );
      }
   }

   public static void printChatError(Error error) {
      printChatError(-1, error);
   }

   public static void printChatError(StarscriptError e) {
      String caller = getCallerName();
      if (caller != null) {
         ChatUtils.errorPrefix(
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Vfs$File$ConstantPool.const_qRy1IjA2eDFdKSb))),
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AttackEntityEvent$ConstantPool.const_FWQQiIlgZZ9ZQCJ))),
            e.getMessage(),
            caller
         );
      } else {
         ChatUtils.errorPrefix(
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(RenderBossBarEvent$ConstantPool.const_nIVVWvLaXnNyaLb))),
            gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TunnelESP$Context$ConstantPool.const_zgKoX4lormtNSwS))),
            e.getMessage()
         );
      }
   }

   private static String getCallerName() {
      StackTraceElement[] elements = Thread.currentThread().getStackTrace();
      if (elements.length == 0) {
         return null;
      } else {
         for (int i = 1; i < elements.length; i++) {
            String name = elements[i].getClassName();
            if (!name.startsWith(Starscript.class.getPackageName()) && !name.equals(MeteorStarscript.class.getName())) {
               return name.substring(name.lastIndexOf(46) + 1);
            }
         }

         return null;
      }
   }

   private static Value hasPotionEffect(Starscript ss, int argCount) {
      if (argCount < 1) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SpectateCommand$ConstantPool.const_GS67SyFWO7Zbq67))), argCount);
      }

      if (MeteorClient.mc.field_1724 == null) {
         return Value.bool(false);
      } else {
         Identifier name = popIdentifier(ss, gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Scanners$2$ConstantPool.const_Vz2VOyBfZyRr4Li))));
         Optional<Reference<StatusEffect>> effect = Registries.field_41174.method_55841(name);
         if (effect.isEmpty()) {
            return Value.bool(false);
         } else {
            StatusEffectInstance effectInstance = MeteorClient.mc.field_1724.method_6112((RegistryEntry)effect.get());
            return Value.bool(effectInstance != null);
         }
      }
   }

   private static Value getPotionEffect(Starscript ss, int argCount) {
      if (argCount < 1) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AllMod$1$1$ConstantPool.const_eI8C8YTyYeOitLS))), argCount);
      }

      if (MeteorClient.mc.field_1724 == null) {
         return Value.null_();
      } else {
         Identifier name = popIdentifier(ss, gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(StringListSetting$Builder$ConstantPool.const_79ny7vh5rPeX2VA))));
         Optional<Reference<StatusEffect>> effect = Registries.field_41174.method_55841(name);
         if (effect.isEmpty()) {
            return Value.null_();
         } else {
            StatusEffectInstance effectInstance = MeteorClient.mc.field_1724.method_6112((RegistryEntry)effect.get());
            return effectInstance == null ? Value.null_() : wrap(effectInstance);
         }
      }
   }

   private static Value getStat(Starscript ss, int argCount) {
      if (argCount < 1) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Nametags$Position$ConstantPool.const_K2ogDhz15aWs62h))), argCount);
      }

      if (MeteorClient.mc.field_1724 == null) {
         return Value.number(0.0);
      } else {
         long time = System.currentTimeMillis();
         if ((time - lastRequestedStatsTime) / MemberValueVisitor$ConstantPool.const_qHtwknO84EDjojt >= 1.0 && MeteorClient.mc.method_1562() != null) {
            MeteorClient.mc.method_1562().method_52787(new ClientStatusC2SPacket(Mode.field_12775));
            lastRequestedStatsTime = time;
         }

         String type = argCount > 1
            ? ss.popString(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WMeteorDropdown$ConstantPool.const_2oOSrMB4WS2lx12))))
            : gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(RenderBossBarEvent$BossSpacing$ConstantPool.const_19v5MDwfvYy9an9)));
         Identifier name = popIdentifier(
            ss,
            (
                  argCount > 1
                     ? gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CharMemberValue$ConstantPool.const_dgebKtt1bhwwqBD)))
                     : gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PacketBoolSettingScreen$ConstantPool.const_q8sH79DV3Y1BkhI)))
               )
               + " argument to player.get_stat() needs to be a string."
         );

         Stat<?> stat = switch (type) {
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ScopedClassPoolRepositoryImpl$ConstantPool.const_yqVoaUMSDwZ11Ll))) -> Stats.field_15427
               .method_14956((Block)Registries.field_41175.method_10223(name));
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GuiTheme$ConstantPool.const_E4kC1owllF0DeYq))) -> Stats.field_15370
               .method_14956((Item)Registries.field_41178.method_10223(name));
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WTextBox$Renderer$ConstantPool.const_9eGRzrgwaWlQX7h))) -> Stats.field_15372
               .method_14956((Item)Registries.field_41178.method_10223(name));
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(MeteorClient$ConstantPool.const_4417E2Cqdj922h1))) -> Stats.field_15383
               .method_14956((Item)Registries.field_41178.method_10223(name));
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(NoSuchClassError$ConstantPool.const_fng9uv1En7y4IBc))) -> Stats.field_15392
               .method_14956((Item)Registries.field_41178.method_10223(name));
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(BaritoneSettings$ConstantPool.const_Gvri27w6o1La6BV))) -> Stats.field_15405
               .method_14956((Item)Registries.field_41178.method_10223(name));
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(JarDirClassPath$1$ConstantPool.const_siEqIID2N16jP7R))) -> Stats.field_15403
               .method_14956((EntityType)Registries.field_41177.method_10223(name));
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(DropCommand$ConstantPool.const_dZwoTJPT1hjiuIN))) -> Stats.field_15411
               .method_14956((EntityType)Registries.field_41177.method_10223(name));
            case gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(NotebotUtils$NotebotMode$ConstantPool.const_F3L0nWlVKh6kodF))) -> {
               name = (Identifier)Registries.field_41183.method_10223(name);
               yield name != null ? Stats.field_15419.method_14956(name) : null;
            }
            default -> null;
         };
         return Value.number(stat != null ? MeteorClient.mc.field_1724.method_3143().method_15025(stat) : 0.0);
      }
   }

   private static Value getModuleInfo(Starscript ss, int argCount) {
      if (argCount != 1) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(RenderUtils$RenderBlock$ConstantPool.const_iSZZeDvfSH11vZa))), argCount);
      }

      Module module = Modules.get().get(ss.popString(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PlayerTickMovementEvent$ConstantPool.const_ot9Qy9UHd8GvKDQ)))));
      if (module != null && module.isActive()) {
         String info = module.getInfoString();
         return Value.string(info == null ? gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ChatUtils$ConstantPool.const_O545tYbwnbkFzCI))) : info);
      } else {
         return Value.string(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(StringSetting$Builder$ConstantPool.const_1w1evT1LiYnCoL0))));
      }
   }

   private static Value getModuleSetting(Starscript ss, int argCount) {
      if (argCount != 2) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ZipDir$ConstantPool.const_st2JwnOKU9WCHsO))), argCount);
      }

      String settingName = ss.popString(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Scanners$2$ConstantPool.const_2oQDsgkL6LwIU4y))));
      String moduleName = ss.popString(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Zoom$ConstantPool.const_5Q2DdM1IGBJna63))));
      Module module = Modules.get().get(moduleName);
      if (module == null) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(MemoryUtil$ConstantPool.const_sfwsCxhrJj6v85m))), moduleName);
      }

      Setting<?> setting = module.settings.get(settingName);
      if (setting == null) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Annotation$ConstantPool.const_av1I91NebQjkmZN))), settingName, moduleName);
      }

      Object value = setting.get();

      return switch (value) {
         case Double d -> Value.number(d);
         case Integer i -> Value.number(i.intValue());
         case Boolean b -> Value.bool(b);
         case List<?> list -> Value.number(list.size());
         case null, default -> Value.string(value.toString());
      };
   }

   private static Value isModuleActive(Starscript ss, int argCount) {
      if (argCount != 1) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(InteractBlockEvent$ConstantPool.const_bGOLdeIrqDgQYQS))), argCount);
      }

      Module module = Modules.get().get(ss.popString(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Sphere2dMarker$Block$ConstantPool.const_o2shKoYbgdV1Sqi)))));
      return Value.bool(module != null && module.isActive());
   }

   private static Value getItem(Starscript ss, int argCount) {
      if (argCount != 1) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SecurityActions$6$ConstantPool.const_HjiplWhrFB2Fa9G))), argCount);
      }

      int i = (int)ss.popNumber(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TextUtils$ConstantPool.const_DlfMQFvAY4bYwBL))));
      if (i < 0) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(StringListSetting$Builder$ConstantPool.const_febwSILyGav21nb))), i);
      }

      return MeteorClient.mc.field_1724 != null ? wrap(MeteorClient.mc.field_1724.method_31548().method_5438(i)) : Value.null_();
   }

   private static Value countItems(Starscript ss, int argCount) {
      if (argCount != 1) {
         ss.error(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ICamera$ConstantPool.const_xwjYwAwFkGADzdf))), argCount);
      }

      String idRaw = ss.popString(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Cancellable$ConstantPool.const_9HHGNCl5eIsj207))));
      Identifier id = Identifier.method_12829(idRaw);
      if (id == null) {
         return Value.number(0.0);
      } else {
         Item item = (Item)Registries.field_41178.method_10223(id);
         if (item != Items.field_8162 && MeteorClient.mc.field_1724 != null) {
            int count = 0;

            for (int i = 0; i < MeteorClient.mc.field_1724.method_31548().method_5439(); i++) {
               ItemStack itemStack = MeteorClient.mc.field_1724.method_31548().method_5438(i);
               if (itemStack.method_7909() == item) {
                  count += itemStack.method_7947();
               }
            }

            return Value.number(count);
         } else {
            return Value.number(0.0);
         }
      }
   }

   private static Value getMeteorPrefix() {
      return Config.get() == null ? Value.null_() : Value.string(Config.get().prefix.get());
   }

   private static Value baritoneProcess() {
      Optional<IBaritoneProcess> process = BaritoneAPI.getProvider().getPrimaryBaritone().getPathingControlManager().mostRecentInControl();
      return Value.string(
         process.isEmpty() ? gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(BlockIterator$Callback$ConstantPool.const_tVS3iS0BbBPKRq9))) : process.get().displayName0()
      );
   }

   private static Value baritoneProcessName() {
      Optional<IBaritoneProcess> process = BaritoneAPI.getProvider().getPrimaryBaritone().getPathingControlManager().mostRecentInControl();
      if (process.isEmpty()) {
         return Value.string(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GuiRenderOperation$ConstantPool.const_8IGh9N1AjOwwD4r))));
      } else {
         String className = process.get().getClass().getSimpleName();
         if (className.endsWith(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TooltipDataEvent$ConstantPool.const_9nIZjnlLhNTi0WH))))) {
            className = className.substring(0, className.length() - 7);
         }

         SB.append(className);
         int i = 0;

         for (int j = 0; j < className.length(); j++) {
            if (j > 0 && Character.isUpperCase(className.charAt(j))) {
               SB.insert(i, ' ');
               i++;
            }

            i++;
         }

         String name = SB.toString();
         SB.setLength(0);
         return Value.string(name);
      }
   }

   private static Value baritoneETA() {
      if (MeteorClient.mc.field_1724 == null) {
         return Value.number(0.0);
      } else {
         Optional<Double> ticksTillGoal = BaritoneAPI.getProvider().getPrimaryBaritone().getPathingBehavior().estimatedTicksToGoal();
         return ticksTillGoal.<Value>map(aDouble -> Value.number(aDouble / SocksCmdResponse$1$ConstantPool.const_rO9DQQLM7oT6oI4))
            .orElseGet(() -> Value.number(0.0));
      }
   }

   private static Value oppositeX(boolean camera) {
      double x = camera
         ? MeteorClient.mc.field_1773.method_19418().method_19326().field_1352
         : (MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_23317() : 0.0);
      Dimension dimension = PlayerUtils.getDimension();
      if (dimension == Dimension.Overworld) {
         x /= LineNumberAttribute$ConstantPool.const_hUlHXLIYgvsF4YJ;
      } else if (dimension == Dimension.Nether) {
         x *= ReflectionUtils$1$ConstantPool.const_qwLI8rQVtLnwIak;
      }

      return Value.number(x);
   }

   private static Value oppositeZ(boolean camera) {
      double z = camera
         ? MeteorClient.mc.field_1773.method_19418().method_19326().field_1350
         : (MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_23321() : 0.0);
      Dimension dimension = PlayerUtils.getDimension();
      if (dimension == Dimension.Overworld) {
         z /= StackMapTable$Copier$ConstantPool.const_y4A1glWtjD8QSwa;
      } else if (dimension == Dimension.Nether) {
         z *= WDropdown$ConstantPool.const_JB0F4SKFerrIRQE;
      }

      return Value.number(z);
   }

   private static Value yaw(boolean camera) {
      float yaw;
      if (camera) {
         yaw = MeteorClient.mc.field_1773.method_19418().method_19330();
      } else {
         yaw = MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_36454() : 0.0F;
      }

      yaw %= AccountType$ConstantPool.const_ws6dVefNuIzB9dK;
      if (yaw < 0.0F) {
         yaw += Trajectories$ConstantPool.const_Vo1b7UBaZLOWjBr;
      }

      if (yaw > PalletBuilder$1$ConstantPool.const_ysLg9MNOVLnHvBt) {
         yaw -= StreamUtils$ConstantPool.const_6FWfpItTygvsf7T;
      }

      return Value.number(yaw);
   }

   private static Value pitch(boolean camera) {
      float pitch;
      if (camera) {
         pitch = MeteorClient.mc.field_1773.method_19418().method_19329();
      } else {
         pitch = MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_36455() : 0.0F;
      }

      pitch %= FastUse$Mode$ConstantPool.const_QX2nhL1OKFyCirT;
      if (pitch < 0.0F) {
         pitch += HotSwapAgent$ConstantPool.const_ntGBpcv2ewbeaoD;
      }

      if (pitch > Hud$ConstantPool.const_EJUx1xgmb7ITdLG) {
         pitch -= TransformNewClass$ConstantPool.const_AJDSVJ4wTtLD0bL;
      }

      return Value.number(pitch);
   }

   private static Value direction(boolean camera) {
      float yaw;
      if (camera) {
         yaw = MeteorClient.mc.field_1773.method_19418().method_19330();
      } else {
         yaw = MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_36454() : 0.0F;
      }

      return wrap(HorizontalDirection.get(yaw));
   }

   private static Value biome() {
      if (MeteorClient.mc.field_1724 != null && MeteorClient.mc.field_1687 != null) {
         BP.method_10102(MeteorClient.mc.field_1724.method_23317(), MeteorClient.mc.field_1724.method_23318(), MeteorClient.mc.field_1724.method_23321());
         Identifier id = MeteorClient.mc
            .field_1687
            .method_30349()
            .method_30530(RegistryKeys.field_41236)
            .method_10221((Biome)MeteorClient.mc.field_1687.method_23753(BP).comp_349());
         return id == null
            ? Value.string(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(IListener$ConstantPool.const_wo7v90GrBuGI1di))))
            : Value.string(
               Arrays.stream(id.method_12832().split(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CodeIterator$AlignmentException$ConstantPool.const_bjN5Khd4yVlABzO)))))
                  .<CharSequence>map(StringUtils::capitalize)
                  .collect(Collectors.joining(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Socks5InitialRequest$ConstantPool.const_OEHLmhbkWAYI1GI)))))
            );
      } else {
         return Value.string(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoRespawn$ConstantPool.const_q4RPgfGy1g0X6FE))));
      }
   }

   private static Value handOrOffhand() {
      if (MeteorClient.mc.field_1724 == null) {
         return Value.null_();
      } else {
         ItemStack itemStack = MeteorClient.mc.field_1724.method_6047();
         if (itemStack.method_7960()) {
            itemStack = MeteorClient.mc.field_1724.method_6079();
         }

         return itemStack != null ? wrap(itemStack) : Value.null_();
      }
   }

   private static Value ping() {
      if (MeteorClient.mc.method_1562() != null && MeteorClient.mc.field_1724 != null) {
         PlayerListEntry playerListEntry = MeteorClient.mc.method_1562().method_2871(MeteorClient.mc.field_1724.method_5667());
         return Value.number(playerListEntry != null ? playerListEntry.method_2959() : 0.0);
      } else {
         return Value.number(0.0);
      }
   }

   private static Value baritoneDistanceToGoal() {
      Goal goal = BaritoneAPI.getProvider().getPrimaryBaritone().getPathingBehavior().getGoal();
      return Value.number(goal != null && MeteorClient.mc.field_1724 != null ? goal.heuristic(MeteorClient.mc.field_1724.method_24515()) : 0.0);
   }

   private static Value posString(boolean opposite, boolean camera) {
      Vec3d pos;
      if (camera) {
         pos = MeteorClient.mc.field_1773.method_19418().method_19326();
      } else {
         pos = MeteorClient.mc.field_1724 != null ? MeteorClient.mc.field_1724.method_19538() : Vec3d.field_1353;
      }

      double x = pos.field_1352;
      double z = pos.field_1350;
      if (opposite) {
         Dimension dimension = PlayerUtils.getDimension();
         if (dimension == Dimension.Overworld) {
            x /= HoleESP$Hole$ConstantPool.const_qWioQ9Fw6HagcB1;
            z /= CrystalAura$YawStepMode$ConstantPool.const_StOM1HQHCNN8uwB;
         } else if (dimension == Dimension.Nether) {
            x *= Friend$ConstantPool.const_Av2Gr23WVoTvLcN;
            z *= LightOverlay$Spawn$ConstantPool.const_fj4AHVF70oDjiT2;
         }
      }

      return posString(x, pos.field_1351, z);
   }

   private static Value posString(double x, double y, double z) {
      return Value.string(String.format(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ElytraFly$StaticGroundListener$ConstantPool.const_eAFJc2qePZ0nwYR))), x, y, z));
   }

   private static Value crosshairType() {
      if (MeteorClient.mc.field_1765 == null) {
         return Value.string(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(XAnchor$ConstantPool.const_6LtEqw6FY25otpL))));
      } else {
         return Value.string(switch (MeteorClient.mc.field_1765.method_17783()) {
            case field_1333 -> gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ClassPath$ConstantPool.const_ZyavhgHvwLCegrn)));
            case field_1332 -> gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AotuPlaceStep$BlockType$ConstantPool.const_2FA7awALWV94Gbb)));
            case field_1331 -> gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Macros$ConstantPool.const_Cos1F5SIZ6gKB29)));
            default -> throw new MatchException(null, null);
         });
      }
   }

   private static Value crosshairValue() {
      if (MeteorClient.mc.field_1687 != null && MeteorClient.mc.field_1765 != null) {
         if (MeteorClient.mc.field_1765.method_17783() == Type.field_1333) {
            return Value.string(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ScopedClassPoolFactory$ConstantPool.const_sLvfwaIKro8AjRW))));
         } else {
            return MeteorClient.mc.field_1765 instanceof BlockHitResult hit
               ? wrap(hit.method_17777(), MeteorClient.mc.field_1687.method_8320(hit.method_17777()))
               : wrap(((EntityHitResult)MeteorClient.mc.field_1765).method_17782());
         }
      } else {
         return Value.null_();
      }
   }

   public static Identifier popIdentifier(Starscript ss, String errorMessage) {
      try {
         return Identifier.method_60654(ss.popString(errorMessage));
      } catch (InvalidIdentifierException var3) {
         ss.error(var3.getMessage());
         return null;
      }
   }

   public static Value wrap(ItemStack itemStack) {
      String name = itemStack.method_7960()
         ? gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Vfs$DefaultUrlTypes$7$ConstantPool.const_720IydhSyFZ4IDg)))
         : Names.get(itemStack.method_7909());
      int durability = 0;
      if (!itemStack.method_7960() && itemStack.method_7963()) {
         durability = itemStack.method_7936() - itemStack.method_7919();
      }

      return Value.map(
         new ValueMap()
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CrystalAura$SwingMode$ConstantPool.const_ieH7DpnZ9CT7R9G))),
               Value.string(
                  itemStack.method_7947() <= 1
                     ? name
                     : String.format(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(AutoJump$Mode$ConstantPool.const_v5I3cIa5JckmCqI))), name, itemStack.method_7947())
               )
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Tabs$ConstantPool.const_AuwTy7FOj6sGSQv))), Value.string(name))
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(YZModules$ConstantPool.const_MJ99JtIYWqaHS6v))),
               Value.string(Registries.field_41178.method_10221(itemStack.method_7909()).toString())
            )
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TypeAnnotationsScanner$ConstantPool.const_vHxnmLfmAvXvNaG))), Value.number(itemStack.method_7947()))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CursorStyle$ConstantPool.const_uiJeTFi5VtMQ2S6))), Value.number(durability))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GuiTexture$ConstantPool.const_vnR6abYznBDnwDT))), Value.number(itemStack.method_7936()))
      );
   }

   public static Value wrap(BlockPos blockPos, BlockState blockState) {
      return Value.map(
         new ValueMap()
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SocksInitResponseDecoder$State$ConstantPool.const_wnFwSaKTeQlZZ0n))),
               Value.string(Names.get(blockState.method_26204()))
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(MethodDecl$ConstantPool.const_OtqBS9pqyhwrCrL))),
               Value.string(Registries.field_41175.method_10221(blockState.method_26204()).toString())
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WRoot$ConstantPool.const_iWtevWSGNvjAyaV))),
               Value.map(
                  new ValueMap()
                     .set(
                        gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(IPCUser$ConstantPool.const_Ay5qJYIq9qvwFXY))),
                        posString(blockPos.method_10263(), blockPos.method_10264(), blockPos.method_10260())
                     )
                     .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(GenericSetting$ConstantPool.const_GWBbYAyxlweTZQ2))), Value.number(blockPos.method_10263()))
                     .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(DropItemsEvent$ConstantPool.const_aiOOvcqsbCYtQQi))), Value.number(blockPos.method_10264()))
                     .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PacketUtils$ConstantPool.const_6qAgDOs5oEosi49))), Value.number(blockPos.method_10260()))
               )
            )
      );
   }

   public static Value wrap(Entity entity) {
      return Value.map(
         new ValueMap()
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Tracers$1$ConstantPool.const_UyFS776vQhYhitq))), Value.string(entity.method_5477().getString()))
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ClassFileWriter$ConstPoolWriter$ConstantPool.const_yGZ0epEQGTIrdi2))),
               Value.string(Registries.field_41177.method_10221(entity.method_5864()).toString())
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(ArmRenderEvent$ConstantPool.const_U5wjQwBdMlVESZB))),
               Value.number(entity instanceof LivingEntity ex ? ex.method_6032() : 0.0)
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CtClassType$ConstantPool.const_R3FTHwIRy1wwDHQ))),
               Value.number(entity instanceof LivingEntity e ? e.method_6067() : 0.0)
            )
            .set(
               gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(IntSetting$ConstantPool.const_aoVFAl9jTlLzrzI))),
               Value.map(
                  new ValueMap()
                     .set(
                        gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(WMultiLabel$ConstantPool.const_LEQKwLtNt6G8QIW))),
                        posString(entity.method_23317(), entity.method_23318(), entity.method_23321())
                     )
                     .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Breadcrumbs$ConstantPool.const_2WYggdrbbrqWtiF))), Value.number(entity.method_23317()))
                     .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(SwarmHost$ConstantPool.const_2JuLsyKWDdNySak))), Value.number(entity.method_23318()))
                     .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(NoRender$BannerRenderMode$ConstantPool.const_B2FBIp8JEU6nd1F))), Value.number(entity.method_23321()))
               )
            )
      );
   }

   public static Value wrap(HorizontalDirection dir) {
      return Value.map(
         new ValueMap()
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(CodeAnalyzer$ConstantPool.const_WpV7PAl6iS8OgM5))), Value.string(dir.name + " " + dir.axis))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(HighwayBuilder$State$8$ConstantPool.const_4fVgLgA1Veru7Y8))), Value.string(dir.name))
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(Scaffold$ListMode$ConstantPool.const_PO2nUw9vWfQgNNa))), Value.string(dir.axis))
      );
   }

   public static Value wrap(StatusEffectInstance effectInstance) {
      return Value.map(
         new ValueMap()
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(PathManagerTab$PathManagerScreen$ConstantPool.const_TAOVVHBbG9v4Lcv))), effectInstance.method_5584())
            .set(gugKyBLoO0(AJGq4e7qVn(dLj9ON9v6y(TypedBlock$ConstantPool.const_zbGGyLDPDrK1dX5))), effectInstance.method_5578() + 1)
      );
   }
}
