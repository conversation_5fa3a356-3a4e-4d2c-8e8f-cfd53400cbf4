package meteordevelopment.meteorclient.utils.usercheck;

import java.util.ArrayList;
import java.util.List;
import net.minecraft.client.MinecraftClient;

public class CheckUtils {
   public static boolean check() {
      MinecraftClient mc = MinecraftClient.method_1551();
      if (getUserNames().size() == 0) {
         return true;
      } else {
         return mc.field_1724 != null ? getUserNames().contains(mc.field_1724.method_7334().getName()) : false;
      }
   }

   public static List<String> getUserNames() {
      List<String> names = new ArrayList<>();
      return names;
   }
}
