package meteordevelopment.meteorclient.utils.render.postprocess;

import javassist.compiler.MemberCodeGen$JsrHook2$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.ModuleScreen$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.BetaMod$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.render.StorageESP;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$14$ConstantPool;
import meteordevelopment.meteorclient.utils.files.YZModules$ConstantPool;
import net.minecraft.entity.Entity;
import org.reflections.scanners.Scanners$7$ConstantPool;

public class StorageOutlineShader extends PostProcessShader {
   private static StorageESP storageESP;

   public StorageOutlineShader() {
      this.init(xymgFWpuje(Fq1bCjrybv(OCMzJr5ULe(Scanners$7$ConstantPool.const_haNrv2jOiQDAVIW))));
   }

   @Override
   protected void preDraw() {
      this.framebuffer.method_1230(false);
      this.framebuffer.method_1235(false);
   }

   @Override
   protected boolean shouldDraw() {
      if (storageESP == null) {
         storageESP = Modules.get().get(StorageESP.class);
      }

      return storageESP.isShader();
   }

   @Override
   public boolean shouldDraw(Entity entity) {
      return true;
   }

   @Override
   protected void setUniforms() {
      this.shader.set(xymgFWpuje(Fq1bCjrybv(OCMzJr5ULe(YZModules$ConstantPool.const_odmIY1jXmd6XDaz))), storageESP.outlineWidth.get());
      this.shader
         .set(
            xymgFWpuje(Fq1bCjrybv(OCMzJr5ULe(HighwayBuilder$State$14$ConstantPool.const_9QN91oOsuJ2BQxF))),
            storageESP.fillOpacity.get().intValue() / MemberCodeGen$JsrHook2$ConstantPool.const_UXXsxDCi785rlTr
         );
      this.shader.set(xymgFWpuje(Fq1bCjrybv(OCMzJr5ULe(ModuleScreen$ConstantPool.const_2dNSOxe446kq2H8))), storageESP.shapeMode.get().ordinal());
      this.shader.set(xymgFWpuje(Fq1bCjrybv(OCMzJr5ULe(BetaMod$1$ConstantPool.const_Y14Woqcc3SwYAGN))), storageESP.glowMultiplier.get());
   }
}
