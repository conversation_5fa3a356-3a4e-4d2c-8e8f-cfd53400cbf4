package meteordevelopment.meteorclient.settings;

import io.netty.handler.codec.socks.SocksAuthResponseDecoder$ConstantPool;
import io.netty.handler.codec.socks.SocksCmdResponse$1$ConstantPool;
import java.util.List;
import java.util.function.Consumer;
import javassist.ClassPool$ConstantPool;
import javassist.bytecode.DuplicateMemberException$ConstantPool;
import javassist.bytecode.SignatureAttribute$ConstantPool;
import javassist.bytecode.TypeAnnotationsAttribute$SubWalker$ConstantPool;
import javassist.compiler.ast.AssignExpr$ConstantPool;
import meteordevelopment.meteorclient.renderer.Fonts;
import meteordevelopment.meteorclient.renderer.text.FontFace;
import meteordevelopment.meteorclient.renderer.text.FontFamily;
import meteordevelopment.meteorclient.renderer.text.FontInfo;
import meteordevelopment.meteorclient.systems.modules.ggboy.GrimPacketMine$SwapMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.Phase$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.SpawnProofer$Mode$ConstantPool;
import net.minecraft.nbt.NbtCompound;
import org.reflections.vfs.JbossFile$ConstantPool;

public class FontFaceSetting extends Setting<FontFace> {
   public FontFaceSetting(
      String name, String description, FontFace defaultValue, Consumer<FontFace> onChanged, Consumer<Setting<FontFace>> onModuleActivated, IVisible visible
   ) {
      super(name, description, defaultValue, onChanged, onModuleActivated, visible);
   }

   protected FontFace parseImpl(String str) {
      String[] split = str.replace(
            g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(ClassPool$ConstantPool.const_zhwQh5Gq6EInVJa))),
            g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(GrimPacketMine$SwapMode$ConstantPool.const_BBJK5bWPHu7LNzU)))
         )
         .split(g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(JbossFile$ConstantPool.const_p7XDwRNCnCpDYvd))));
      if (split.length != 2) {
         return null;
      } else {
         for (FontFamily family : Fonts.FONT_FAMILIES) {
            if (family.getName()
               .replace(
                  g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(SpawnProofer$Mode$ConstantPool.const_NiI3462B3bBkjpC))),
                  g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(TypeAnnotationsAttribute$SubWalker$ConstantPool.const_i8tvO3jSaKgHBOq)))
               )
               .equals(split[0])) {
               try {
                  return family.get(FontInfo.Type.valueOf(split[1]));
               } catch (IllegalArgumentException var6) {
                  return null;
               }
            }
         }

         return null;
      }
   }

   @Override
   public List<String> getSuggestions() {
      return List.of(
         g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(SocksAuthResponseDecoder$ConstantPool.const_wlbw1ZETrQeN5CO))),
         g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(AssignExpr$ConstantPool.const_lWiBjyjEIE9rgd2)))
      );
   }

   protected boolean isValueValid(FontFace value) {
      if (value == null) {
         return false;
      } else {
         for (FontFamily fontFamily : Fonts.FONT_FAMILIES) {
            if (fontFamily.hasType(value.info.type())) {
               return true;
            }
         }

         return false;
      }
   }

   @Override
   protected NbtCompound save(NbtCompound tag) {
      tag.method_10582(g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(DuplicateMemberException$ConstantPool.const_RRg6qBxeLvKxtN8))), this.get().info.family());
      tag.method_10582(g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(SocksCmdResponse$1$ConstantPool.const_RyLNoefrGyALe1b))), this.get().info.type().toString());
      return tag;
   }

   protected FontFace load(NbtCompound tag) {
      String family = tag.method_10558(g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(SignatureAttribute$ConstantPool.const_gsybYQVvnQe3FGd))));

      FontInfo.Type type;
      try {
         type = FontInfo.Type.valueOf(tag.method_10558(g7iA4VmbUH(qrQUqkTQD9(vdtgaMlUP6(Phase$ConstantPool.const_UNRIdwvObotewDw)))));
      } catch (IllegalArgumentException var7) {
         this.set(Fonts.DEFAULT_FONT);
         return this.get();
      }

      boolean changed = false;

      for (FontFamily fontFamily : Fonts.FONT_FAMILIES) {
         if (fontFamily.getName().equals(family)) {
            this.set(fontFamily.get(type));
            changed = true;
         }
      }

      if (!changed) {
         this.set(Fonts.DEFAULT_FONT);
      }

      return this.get();
   }

   public static class Builder extends Setting.SettingBuilder<FontFaceSetting.Builder, FontFace, FontFaceSetting> {
      public Builder() {
         super(Fonts.DEFAULT_FONT);
      }

      public FontFaceSetting build() {
         return new FontFaceSetting(this.name, this.description, this.defaultValue, this.onChanged, this.onModuleActivated, this.visible);
      }
   }
}
