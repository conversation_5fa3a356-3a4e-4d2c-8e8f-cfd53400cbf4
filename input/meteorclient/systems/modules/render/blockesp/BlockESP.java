package meteordevelopment.meteorclient.systems.modules.render.blockesp;

import de.florianmichael.waybackauthlib.WaybackAuthLib$Response$ConstantPool;
import de.florianmichael.waybackauthlib.WaybackAuthLib$User$ConstantPool;
import io.netty.handler.codec.socks.SocksAuthResponseDecoder$State$ConstantPool;
import it.unimi.dsi.fastutil.longs.Long2ObjectMap;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javassist.CtField$DoubleInitializer$ConstantPool;
import javassist.bytecode.MemberrefInfo$ConstantPool;
import javax.annotation.meta.Exclusive$ConstantPool;
import meteordevelopment.discordipc.Opcode$ConstantPool;
import meteordevelopment.meteorclient.MixinPlugin$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlaceBlockEvent$ConstantPool;
import meteordevelopment.meteorclient.events.meteor.CharTypedEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.render.RenderItemEntityEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.BlockUpdateEvent;
import meteordevelopment.meteorclient.events.world.ChunkDataEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.BlockDataSetting;
import meteordevelopment.meteorclient.settings.BlockListSetting;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.GenericSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoMineSand$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.PacketMine$ConstantPool;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.misc.UnorderedArrayList;
import meteordevelopment.meteorclient.utils.misc.text.RunnableClickEvent$ConstantPool;
import meteordevelopment.meteorclient.utils.network.Http$Method$ConstantPool;
import meteordevelopment.meteorclient.utils.network.MeteorExecutor;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.render.FontUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.render.NametagUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.RainbowColors;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.world.Dimension;
import meteordevelopment.meteorclient.utils.world.Timer;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.compiler.Parser$ConstantPool;
import net.minecraft.block.Block;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.util.math.BlockPos.Mutable;
import net.minecraft.world.chunk.Chunk;

public class BlockESP extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final Setting<List<Block>> blocks = this.sgGeneral
      .add(
         new BlockListSetting.Builder()
            .name(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(PlaceBlockEvent$ConstantPool.const_HeKohVAyn35t6gY))))
            .description(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(SocksAuthResponseDecoder$State$ConstantPool.const_HfRQFoV0UddJiwg))))
            .onChanged(blocks1 -> {
               if (this.isActive() && Utils.canUpdate()) {
                  this.onActivate();
               }
            })
            .build()
      );
   private final Setting<BlockESP.HeightMode> searchMode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(HighwayBuilder$State$1$ConstantPool.const_3I7IH6oWhjHtqI7)))))
                  .description(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(AutoMineSand$ConstantPool.const_Lo9QwbOyTL9gaF4)))))
               .defaultValue(BlockESP.HeightMode.Three))
            .build()
      );
   private final Setting<Boolean> AutoRefresh = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(Http$Method$ConstantPool.const_y7wCjd96h2rV74C))))
            .description(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(PacketMine$ConstantPool.const_zCETAasVognOHAW))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Integer> freshTimes = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(Exclusive$ConstantPool.const_pj7yY6U2C7G9sjd))))
            .description(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(CharTypedEvent$ConstantPool.const_T4vysDFhbOI7AyW))))
            .defaultValue(20)
            .sliderRange(0, 200)
            .visible(() -> this.AutoRefresh.get())
            .build()
      );
   private final Setting<ESPBlockData> defaultBlockConfig = this.sgGeneral
      .add(
         ((GenericSetting.Builder)((GenericSetting.Builder)new GenericSetting.Builder()
                  .name(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(Opcode$ConstantPool.const_T4QSAeXVFAmJSoj)))))
               .description(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(CtField$DoubleInitializer$ConstantPool.const_CGiJ87Ur3QPh2DN)))))
            .defaultValue(
               new ESPBlockData(ShapeMode.Lines, new SettingColor(0, 255, 200), new SettingColor(0, 255, 200, 25), true, new SettingColor(0, 255, 200, 125))
            )
            .build()
      );
   private final Setting<Map<Block, ESPBlockData>> blockConfigs = this.sgGeneral
      .add(
         ((BlockDataSetting.Builder)((BlockDataSetting.Builder)new BlockDataSetting.Builder()
                  .name(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(NametagUtils$ConstantPool.const_46GX52p9DsyePPR)))))
               .description(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(Parser$ConstantPool.const_D1j9qgOjBYOylcR)))))
            .defaultData(this.defaultBlockConfig)
            .build()
      );
   private final Setting<Boolean> tracers = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(RunnableClickEvent$ConstantPool.const_sdG7Bh80htH2B8I))))
            .description(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(FontUtils$ConstantPool.const_4WV9Mca9Njk8yyi))))
            .defaultValue(false)
            .build()
      );
   private final Mutable blockPos = new Mutable();
   private final Long2ObjectMap<ESPChunk> chunks = new Long2ObjectOpenHashMap();
   private final List<ESPGroup> groups = new UnorderedArrayList<>();
   private Dimension lastDimension;
   private Timer freshTimer = new Timer();
   private BlockESP.HeightMode localMode = this.searchMode.get();

   public BlockESP() {
      super(
         Categories.Render,
         WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(WaybackAuthLib$User$ConstantPool.const_vW4nIfaXBwoZEPY))),
         new StringBuilder(WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(MemberrefInfo$ConstantPool.const_tT4UDGQ31GoTVQb)))),
         WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(MixinPlugin$ConstantPool.const_GezbNCiW6gUvmnJ))),
         WkW4roW6Nh(C4qrgZgj6i(l4JoltN9Re(WaybackAuthLib$Response$ConstantPool.const_HEYrZBKMDyliysd)))
      );
      RainbowColors.register(this::onTickRainbow);
   }

   @Override
   public void onActivate() {
      synchronized (this.chunks) {
         this.chunks.clear();
         this.groups.clear();
      }

      for (Chunk chunk : Utils.chunks()) {
         this.searchChunk(chunk);
      }

      this.lastDimension = PlayerUtils.getDimension();
      this.freshTimer.reset();
      this.localMode = this.searchMode.get();
   }

   @Override
   public void onDeactivate() {
      synchronized (this.chunks) {
         this.chunks.clear();
         this.groups.clear();
      }
   }

   private void onTickRainbow() {
      if (this.isActive()) {
         this.defaultBlockConfig.get().tickRainbow();

         for (ESPBlockData blockData : this.blockConfigs.get().values()) {
            blockData.tickRainbow();
         }
      }
   }

   ESPBlockData getBlockData(Block block) {
      ESPBlockData blockData = this.blockConfigs.get().get(block);
      return blockData == null ? this.defaultBlockConfig.get() : blockData;
   }

   private void updateChunk(int x, int z) {
      ESPChunk chunk = (ESPChunk)this.chunks.get(ChunkPos.method_8331(x, z));
      if (chunk != null) {
         chunk.update();
      }
   }

   private void updateBlock(int x, int y, int z) {
      ESPChunk chunk = (ESPChunk)this.chunks.get(ChunkPos.method_8331(x >> 4, z >> 4));
      if (chunk != null) {
         chunk.update(x, y, z);
      }
   }

   public ESPBlock getBlock(int x, int y, int z) {
      ESPChunk chunk = (ESPChunk)this.chunks.get(ChunkPos.method_8331(x >> 4, z >> 4));
      return chunk == null ? null : chunk.get(x, y, z);
   }

   public ESPGroup newGroup(Block block) {
      synchronized (this.chunks) {
         ESPGroup group = new ESPGroup(block);
         this.groups.add(group);
         return group;
      }
   }

   public void removeGroup(ESPGroup group) {
      synchronized (this.chunks) {
         this.groups.remove(group);
      }
   }

   @EventHandler
   private void onChunkData(ChunkDataEvent event) {
      this.searchChunk(event.chunk());
   }

   @EventHandler
   public void onTick(TickEvent.Pre event) {
      if (this.freshTimer.passedS(this.freshTimes.get().intValue()) && this.AutoRefresh.get() && this.searchMode.get() != BlockESP.HeightMode.Max) {
         this.reFresh();
         this.freshTimer.reset();
      }

      if (this.searchMode.get() != this.localMode) {
         this.localMode = this.searchMode.get();
         this.reFresh();
      }
   }

   private void reFresh() {
      synchronized (this.chunks) {
         this.chunks.clear();
         this.groups.clear();
      }

      for (Chunk chunk : Utils.chunks(true)) {
         if (!(PlayerUtils.distanceTo(chunk.method_12004().method_33943((int)mc.field_1724.method_23318())) > this.searchMode.get().getType())) {
            this.searchChunk(chunk);
         }
      }

      this.lastDimension = PlayerUtils.getDimension();
   }

   private void searchChunk(Chunk chunk) {
      MeteorExecutor.execute(() -> {
         if (this.isActive()) {
            ESPChunk schunk = ESPChunk.searchChunk(chunk, this.blocks.get(), this.searchMode.get());
            if (schunk.size() > 0) {
               synchronized (this.chunks) {
                  this.chunks.put(chunk.method_12004().method_8324(), schunk);
                  schunk.update();
                  this.updateChunk(chunk.method_12004().field_9181 - 1, chunk.method_12004().field_9180);
                  this.updateChunk(chunk.method_12004().field_9181 + 1, chunk.method_12004().field_9180);
                  this.updateChunk(chunk.method_12004().field_9181, chunk.method_12004().field_9180 - 1);
                  this.updateChunk(chunk.method_12004().field_9181, chunk.method_12004().field_9180 + 1);
               }
            }
         }
      });
   }

   @EventHandler
   private void onBlockUpdate(BlockUpdateEvent event) {
      int bx = event.pos.method_10263();
      int by = event.pos.method_10264();
      int bz = event.pos.method_10260();
      int chunkX = bx >> 4;
      int chunkZ = bz >> 4;
      long key = ChunkPos.method_8331(chunkX, chunkZ);
      boolean added = this.blocks.get().contains(event.newState.method_26204()) && !this.blocks.get().contains(event.oldState.method_26204());
      boolean removed = !added && !this.blocks.get().contains(event.newState.method_26204()) && this.blocks.get().contains(event.oldState.method_26204());
      if (added || removed) {
         MeteorExecutor.execute(() -> {
            synchronized (this.chunks) {
               ESPChunk chunk = (ESPChunk)this.chunks.get(key);
               if (chunk == null) {
                  chunk = new ESPChunk(chunkX, chunkZ);
                  if (chunk.shouldBeDeleted()) {
                     return;
                  }

                  this.chunks.put(key, chunk);
               }

               this.blockPos.method_10103(bx, by, bz);
               if (added) {
                  chunk.add(this.blockPos);
               } else {
                  chunk.remove(this.blockPos);
               }

               for (int x = -1; x < 2; x++) {
                  for (int z = -1; z < 2; z++) {
                     for (int y = -1; y < 2; y++) {
                        if (x != 0 || y != 0 || z != 0) {
                           this.updateBlock(bx + x, by + y, bz + z);
                        }
                     }
                  }
               }
            }
         });
      }
   }

   @EventHandler
   private void onPostTick(TickEvent.Post event) {
      Dimension dimension = PlayerUtils.getDimension();
      if (this.lastDimension != dimension) {
         this.onActivate();
      }

      this.lastDimension = dimension;
   }

   @EventHandler
   private void onRender(Render3DEvent event) {
      synchronized (this.chunks) {
         Iterator<ESPChunk> it = this.chunks.values().iterator();

         while (it.hasNext()) {
            ESPChunk chunk = it.next();
            if (chunk.shouldBeDeleted()) {
               MeteorExecutor.execute(() -> {
                  ObjectIterator var1 = chunk.blocks.values().iterator();

                  while (var1.hasNext()) {
                     ESPBlock block = (ESPBlock)var1.next();
                     block.group.remove(block, false);
                     block.loaded = false;
                  }
               });
               it.remove();
            } else {
               chunk.render(event);
            }
         }

         if (this.tracers.get()) {
            it = this.groups.iterator();

            while (it.hasNext()) {
               ESPGroup group = (ESPGroup)it.next();
               if (group.blocks.isEmpty()) {
                  it.remove();
               } else {
                  group.render(event);
               }
            }
         }
      }
   }

   public static enum HeightMode {
      Three(32),
      Six(64),
      Nine(96),
      Twelve(128),
      Max(RenderItemEntityEvent$ConstantPool.const_cjDqgdQe2par6LK);

      private int type;

      public int getType() {
         return this.type;
      }

      private HeightMode(int type) {
         this.type = type;
      }
   }
}
