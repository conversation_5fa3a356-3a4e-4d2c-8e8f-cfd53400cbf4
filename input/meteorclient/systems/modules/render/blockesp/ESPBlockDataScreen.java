package meteordevelopment.meteorclient.systems.modules.render.blockesp;

import javassist.bytecode.StackMap$Printer$ConstantPool;
import javax.annotation.meta.TypeQualifierDefault$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.RotationCommand$ConstantPool;
import meteordevelopment.meteorclient.gui.GuiTheme;
import meteordevelopment.meteorclient.gui.WindowScreen;
import meteordevelopment.meteorclient.gui.screens.ProxiesImportScreen$ConstantPool;
import meteordevelopment.meteorclient.renderer.Renderer3D$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.BlockDataSetting;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.Settings;
import meteordevelopment.meteorclient.systems.accounts.TexturesJson$Texture$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoWeapon$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.ElytraAndArmor$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.AutoNametag$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Flamethrower$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralPayloadType$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.world.Dir$ConstantPool;
import net.minecraft.block.Block;

public class ESPBlockDataScreen extends WindowScreen {
   private final ESPBlockData blockData;
   private final Block block;
   private final BlockDataSetting<ESPBlockData> setting;

   public ESPBlockDataScreen(GuiTheme theme, ESPBlockData blockData, Block block, BlockDataSetting<ESPBlockData> setting) {
      super(theme, DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(AutoNametag$ConstantPool.const_JoH3pmlDFXBnemN))));
      this.blockData = blockData;
      this.block = block;
      this.setting = setting;
   }

   @Override
   public void initWidgets() {
      Settings settings = new Settings();
      SettingGroup sgGeneral = settings.getDefaultGroup();
      SettingGroup sgTracer = settings.createGroup(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(TypeQualifierDefault$ConstantPool.const_11lraqpi9cOft1h))));
      sgGeneral.add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                           .name(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(Dir$ConstantPool.const_SBNNKnyH17OmBL6)))))
                        .description(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(TexturesJson$Texture$ConstantPool.const_oo5WJ72Wq6SuIVh)))))
                     .defaultValue(ShapeMode.Lines))
                  .onModuleActivated(shapeModeSetting -> shapeModeSetting.set(this.blockData.shapeMode)))
               .onChanged(shapeMode -> {
                  this.blockData.shapeMode = shapeMode;
                  this.changed(this.blockData, this.block, this.setting);
               }))
            .build()
      );
      sgGeneral.add(
         new ColorSetting.Builder()
            .name(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(AstralPayloadType$ConstantPool.const_B4DaKUTsBiQwAqV))))
            .description(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(ProxiesImportScreen$ConstantPool.const_Z7vSqBU0JJafbao))))
            .defaultValue(new SettingColor(0, 255, 200))
            .onModuleActivated(settingColorSetting -> settingColorSetting.set(this.blockData.lineColor))
            .onChanged(settingColor -> {
               this.blockData.lineColor.set((Color)settingColor);
               this.changed(this.blockData, this.block, this.setting);
            })
            .build()
      );
      sgGeneral.add(
         new ColorSetting.Builder()
            .name(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(Renderer3D$ConstantPool.const_WlvbjNmGA9mcM9N))))
            .description(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(AutoWeapon$ConstantPool.const_Bti0JgGaqDq99jq))))
            .defaultValue(new SettingColor(0, 255, 200, 25))
            .onModuleActivated(settingColorSetting -> settingColorSetting.set(this.blockData.sideColor))
            .onChanged(settingColor -> {
               this.blockData.sideColor.set((Color)settingColor);
               this.changed(this.blockData, this.block, this.setting);
            })
            .build()
      );
      sgTracer.add(
         new BoolSetting.Builder()
            .name(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(RotationCommand$ConstantPool.const_q8JVeZlCA8VWlnY))))
            .description(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(ElytraAndArmor$ConstantPool.const_cGXaEIvsYwUlrGb))))
            .defaultValue(true)
            .onModuleActivated(booleanSetting -> booleanSetting.set(this.blockData.tracer))
            .onChanged(aBoolean -> {
               this.blockData.tracer = aBoolean;
               this.changed(this.blockData, this.block, this.setting);
            })
            .build()
      );
      sgTracer.add(
         new ColorSetting.Builder()
            .name(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(StackMap$Printer$ConstantPool.const_8TT912ZTeVx9Eir))))
            .description(DqlYYq32Al(RBOnoclUtd(Bqr7lJlw7X(Flamethrower$ConstantPool.const_ddH92Ey35TJpbDV))))
            .defaultValue(new SettingColor(0, 255, 200, 125))
            .onModuleActivated(settingColorSetting -> settingColorSetting.set(this.blockData.tracerColor))
            .onChanged(settingColor -> {
               this.blockData.tracerColor = settingColor;
               this.changed(this.blockData, this.block, this.setting);
            })
            .build()
      );
      settings.onActivated();
      this.add(this.theme.settings(settings)).expandX();
   }

   private void changed(ESPBlockData blockData, Block block, BlockDataSetting<ESPBlockData> setting) {
      if (!blockData.isChanged() && block != null && setting != null) {
         setting.get().put(block, blockData);
         setting.onChanged();
      }

      blockData.changed();
   }
}
