package meteordevelopment.meteorclient.systems.modules.render.blockesp;

import io.netty.handler.codec.socksx.v5.Socks5CommandResponseDecoder$1$ConstantPool;
import javassist.bytecode.annotation.IntegerMemberValue$ConstantPool;
import javassist.expr.ExprEditor$NewOp$ConstantPool;
import javassist.runtime.Desc$1$ConstantPool;
import javassist.util.proxy.DefineClassHelper$1$ConstantPool;
import javassist.util.proxy.RuntimeSupport$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.commands.commands.RotationCommand$ConstantPool;
import meteordevelopment.meteorclient.events.game.GameJoinedEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.gui.screens.accounts.AddAlteningAccountScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.pressable.WPlus$ConstantPool;
import meteordevelopment.meteorclient.pathing.BaritoneSettings$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.StatusEffectAmplifierMapSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$AuthTokenResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoCrit$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.PalletBuilder$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ESP$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.LightOverlay$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.TunnelESP$Context$ConstantPool;
import meteordevelopment.meteorclient.utils.network.OnlinePlayers$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.IEventBus$ConstantPool;
import meteordevelopment.orbit.listeners.IListener$ConstantPool;
import meteordevelopment.starscript.compiler.Compiler$ConstantPool;
import net.minecraft.block.BlockState;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.BlockPos.Mutable;
import net.minecraft.util.math.Direction.Axis;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.util.shape.VoxelShapes;
import org.reflections.scanners.Scanners$8$ConstantPool;

public class ESPBlock {
   private static final Mutable blockPos = new Mutable();
   private static final BlockESP blockEsp = Modules.get().get(BlockESP.class);
   public static final int FO = 2;
   public static final int FO_RI = 4;
   public static final int RI = 8;
   public static final int BA_RI = 16;
   public static final int BA = 32;
   public static final int BA_LE = 64;
   public static final int LE = 128;
   public static final int FO_LE = 256;
   public static final int TO = 512;
   public static final int TO_FO = 1024;
   public static final int TO_BA = 2048;
   public static final int TO_RI = 4096;
   public static final int TO_LE = 8192;
   public static final int BO = 16384;
   public static final int BO_FO = 32768;
   public static final int BO_BA = 65536;
   public static final int BO_RI = 131072;
   public static final int BO_LE = 262144;
   public static final int[] SIDES = new int[]{2, 32, 128, 8, 512, 16384};
   public final int x;
   public final int y;
   public final int z;
   private BlockState state;
   public int neighbours;
   public ESPGroup group;
   public boolean loaded = true;

   public ESPBlock(int x, int y, int z) {
      this.x = x;
      this.y = y;
      this.z = z;
   }

   public ESPBlock getSideBlock(int side) {
      return switch (side) {
         case 2 -> blockEsp.getBlock(this.x, this.y, this.z + 1);
         case 8 -> blockEsp.getBlock(this.x + 1, this.y, this.z);
         case 32 -> blockEsp.getBlock(this.x, this.y, this.z - 1);
         case 128 -> blockEsp.getBlock(this.x - 1, this.y, this.z);
         case 512 -> blockEsp.getBlock(this.x, this.y + 1, this.z);
         case 16384 -> blockEsp.getBlock(this.x, this.y - 1, this.z);
         default -> null;
      };
   }

   private void assignGroup() {
      ESPGroup firstGroup = null;

      for (int side : SIDES) {
         if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~side) - ~side) - (~(this.neighbours - 1) + -1) == side) {
            ESPBlock neighbour = this.getSideBlock(side);
            if (neighbour != null && neighbour.group != null) {
               if (firstGroup == null) {
                  firstGroup = neighbour.group;
               } else if (firstGroup != neighbour.group) {
                  firstGroup.merge(neighbour.group);
               }
            }
         }
      }

      if (firstGroup == null) {
         firstGroup = blockEsp.newGroup(this.state.method_26204());
      }

      firstGroup.add(this);
   }

   public void update() {
      this.state = MeteorClient.mc.field_1687.method_8320(blockPos.method_10103(this.x, this.y, this.z));
      this.neighbours = 0;
      if (this.isNeighbour(Direction.field_11035)) {
         this.neighbours = 2 + (-this.neighbours + -1 + (~2 + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(1.0, 0.0, 1.0)) {
         this.neighbours = 4 + (-this.neighbours + -1 + (~(4 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbour(Direction.field_11034)) {
         this.neighbours = 8 + (-this.neighbours + -1 + (~(8 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(1.0, 0.0, Scanners$8$ConstantPool.const_nkSvZCSyyG2kJSR)) {
         this.neighbours = 16 + (-this.neighbours + -1 + (~(16 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbour(Direction.field_11043)) {
         this.neighbours = 32 + (-this.neighbours + -1 + (~32 + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(GameJoinedEvent$ConstantPool.const_JlNxt7odVDlqlt5, 0.0, Socks5CommandResponseDecoder$1$ConstantPool.const_lQ4Ego1RnkY1LbP)) {
         this.neighbours = 64 + (-this.neighbours + -1 + (~(64 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbour(Direction.field_11039)) {
         this.neighbours = 128 + (-this.neighbours + -1 + (~(128 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(PalletBuilder$ConstantPool.const_6BUCdC7AO341GHX, 0.0, 1.0)) {
         this.neighbours = 256 + (-this.neighbours + -1 + (~(256 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbour(Direction.field_11036)) {
         this.neighbours = 512 + (-this.neighbours + -1 + (~(512 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(0.0, 1.0, 1.0)) {
         this.neighbours = 1024 + (-this.neighbours + -1 + (~1024 + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(0.0, 1.0, StatusEffectAmplifierMapSetting$Builder$ConstantPool.const_TcijiAejgynI9Gq)) {
         this.neighbours = 2048 + (-this.neighbours + -1 + (~2048 + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(1.0, 1.0, 0.0)) {
         this.neighbours = 4096 + (-this.neighbours + -1 + (~(4096 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(IEventBus$ConstantPool.const_1QSr0gVwqhQLdSF, 1.0, 0.0)) {
         this.neighbours = 8192 + (-this.neighbours + -1 + (~(8192 - 1) + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbour(Direction.field_11033)) {
         this.neighbours = 16384 + (-this.neighbours + -1 + (~16384 + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(0.0, MicrosoftLogin$AuthTokenResponse$ConstantPool.const_8628AY1Fzp2dqDT, 1.0)) {
         this.neighbours = AutoCrit$ConstantPool.const_3C96XLN97TFFzqa
            + (-this.neighbours + -1 + (~AutoCrit$ConstantPool.const_3C96XLN97TFFzqa + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(0.0, Compiler$ConstantPool.const_DEVa5ZoYn7o9TPb, WPlus$ConstantPool.const_q1dNLkl0Omp66J2)) {
         this.neighbours = ExprEditor$NewOp$ConstantPool.const_loMmqvMY1VPLFbi
            + (-this.neighbours + -1 + (~ExprEditor$NewOp$ConstantPool.const_loMmqvMY1VPLFbi + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(1.0, LightOverlay$ConstantPool.const_YP4Bte2FLgrnIY7, 0.0)) {
         this.neighbours = RotationCommand$ConstantPool.const_QD0rvZG7LbItKQs
            + (-this.neighbours + -1 + (~RotationCommand$ConstantPool.const_QD0rvZG7LbItKQs + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.isNeighbourDiagonal(OnlinePlayers$ConstantPool.const_ChuQ40pEao7aSLJ, TunnelESP$Context$ConstantPool.const_LiGS5AgOBlu8d2F, 0.0)) {
         this.neighbours = DefineClassHelper$1$ConstantPool.const_6ralYNV7biiUgqq
            + (-this.neighbours + -1 + (~DefineClassHelper$1$ConstantPool.const_6ralYNV7biiUgqq + 1 + -1 & ~(-this.neighbours + -1)) - (-this.neighbours + -1));
      }

      if (this.group == null) {
         this.assignGroup();
      }
   }

   private boolean isNeighbour(Direction dir) {
      blockPos.method_10103(this.x + dir.method_10148(), this.y + dir.method_10164(), this.z + dir.method_10165());
      BlockState neighbourState = MeteorClient.mc.field_1687.method_8320(blockPos);
      if (neighbourState.method_26204() != this.state.method_26204()) {
         return false;
      } else {
         VoxelShape cube = VoxelShapes.method_1077();
         VoxelShape shape = this.state.method_26218(MeteorClient.mc.field_1687, blockPos);
         VoxelShape neighbourShape = neighbourState.method_26218(MeteorClient.mc.field_1687, blockPos);
         if (shape.method_1110()) {
            shape = cube;
         }

         if (neighbourShape.method_1110()) {
            neighbourShape = cube;
         }

         switch (dir) {
            case field_11035:
               if (shape.method_1105(Axis.field_11051) == 1.0 && neighbourShape.method_1091(Axis.field_11051) == 0.0) {
                  return true;
               }
               break;
            case field_11043:
               if (shape.method_1091(Axis.field_11051) == 0.0 && neighbourShape.method_1105(Axis.field_11051) == 1.0) {
                  return true;
               }
               break;
            case field_11034:
               if (shape.method_1105(Axis.field_11048) == 1.0 && neighbourShape.method_1091(Axis.field_11048) == 0.0) {
                  return true;
               }
               break;
            case field_11039:
               if (shape.method_1091(Axis.field_11048) == 0.0 && neighbourShape.method_1105(Axis.field_11048) == 1.0) {
                  return true;
               }
               break;
            case field_11036:
               if (shape.method_1105(Axis.field_11052) == 1.0 && neighbourShape.method_1091(Axis.field_11052) == 0.0) {
                  return true;
               }
               break;
            case field_11033:
               if (shape.method_1091(Axis.field_11052) == 0.0 && neighbourShape.method_1105(Axis.field_11052) == 1.0) {
                  return true;
               }
         }

         return false;
      }
   }

   private boolean isNeighbourDiagonal(double x, double y, double z) {
      blockPos.method_10102(this.x + x, this.y + y, this.z + z);
      return this.state.method_26204() == MeteorClient.mc.field_1687.method_8320(blockPos).method_26204();
   }

   public void render(Render3DEvent event) {
      double x1 = this.x;
      double y1 = this.y;
      double z1 = this.z;
      double x2 = this.x + 1;
      double y2 = this.y + 1;
      double z2 = this.z + 1;
      VoxelShape shape = this.state.method_26218(MeteorClient.mc.field_1687, blockPos);
      if (!shape.method_1110()) {
         x1 = this.x + shape.method_1091(Axis.field_11048);
         y1 = this.y + shape.method_1091(Axis.field_11052);
         z1 = this.z + shape.method_1091(Axis.field_11051);
         x2 = this.x + shape.method_1105(Axis.field_11048);
         y2 = this.y + shape.method_1105(Axis.field_11052);
         z2 = this.z + shape.method_1105(Axis.field_11051);
      }

      ESPBlockData blockData = blockEsp.getBlockData(this.state.method_26204());
      ShapeMode shapeMode = blockData.shapeMode;
      Color lineColor = blockData.lineColor;
      Color sideColor = blockData.sideColor;
      if (this.neighbours == 0) {
         event.renderer.box(x1, y1, z1, x2, y2, z2, sideColor, lineColor, shapeMode, 0);
      } else {
         if (shapeMode.lines()) {
            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~128) - ~128) - (~(this.neighbours - 1) + -1) != 128
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~32) - ~32) - (~(this.neighbours - 1) + -1) != 32
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~128) - ~128) - (~(this.neighbours - 1) + -1) == 128
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~32) - ~32) - (~this.neighbours + 1 + -1) == 32
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~64) - ~64) - (~this.neighbours + 1 + -1) != 64) {
               event.renderer.line(x1, y1, z1, x1, y2, z1, lineColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~128) - ~128) - (~(this.neighbours - 1) + -1) != 128
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~2) - ~2) - (~(this.neighbours - 1) + -1) != 2
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~128) - ~128) - (~this.neighbours + 1 + -1) == 128
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~2) - ~2) - (~(this.neighbours - 1) + -1) == 2
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~256) - ~256) - (~this.neighbours + 1 + -1) != 256) {
               event.renderer.line(x1, y1, z2, x1, y2, z2, lineColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~8) - ~8) - (~(this.neighbours - 1) + -1) != 8
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~32) - ~32) - (~this.neighbours + 1 + -1) != 32
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~8) - ~8) - (~this.neighbours + 1 + -1) == 8
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~32) - ~32) - (~(this.neighbours - 1) + -1) == 32
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~16) - ~16) - (~this.neighbours + 1 + -1) != 16) {
               event.renderer.line(x2, y1, z1, x2, y2, z1, lineColor);
            }

            if (~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~8) - ~8) - (~(this.neighbours - 1) + -1) != 8
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~2) - ~2) - (~(this.neighbours - 1) + -1) != 2
               || ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~8) - ~8) - (~(this.neighbours - 1) + -1) == 8
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~2) - ~2) - (~this.neighbours + 1 + -1) == 2
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~4) - ~4) - (~(this.neighbours - 1) + -1) != 4) {
               event.renderer.line(x2, y1, z2, x2, y2, z2, lineColor);
            }

            if (~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~32) - ~32) - (~(this.neighbours - 1) + -1) != 32
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~16384) - ~16384) - (~this.neighbours + 1 + -1) != 16384
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~32) - ~32) - (~(this.neighbours - 1) + -1) != 32
                  && ~this.neighbours
                        + 1
                        + -1
                        + (
                           (-(~this.neighbours + 1 + -1) + -1 | ~BaritoneSettings$ConstantPool.const_9SKBLIblWcIkX2V)
                              - ~BaritoneSettings$ConstantPool.const_9SKBLIblWcIkX2V
                        )
                        - (~(this.neighbours - 1) + -1)
                     == IntegerMemberValue$ConstantPool.const_2pAGBDVaSOdWMgF) {
               event.renderer.line(x1, y1, z1, x2, y1, z1, lineColor);
            }

            if (~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~2) - ~2) - (~this.neighbours + 1 + -1) != 2
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~16384) - ~16384) - (~(this.neighbours - 1) + -1) != 16384
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~2) - ~2) - (~(this.neighbours - 1) + -1) != 2
                  && ~(this.neighbours - 1)
                        + -1
                        + ((-(~(this.neighbours - 1) + -1) + -1 | ~ESP$Mode$ConstantPool.const_DBPoVqgWMGO5liM) - ~ESP$Mode$ConstantPool.const_DBPoVqgWMGO5liM)
                        - (~this.neighbours + 1 + -1)
                     == AddAlteningAccountScreen$ConstantPool.const_A9vp1DHLrf6am5l) {
               event.renderer.line(x1, y1, z2, x2, y1, z2, lineColor);
            }

            if (~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~32) - ~32) - (~(this.neighbours - 1) + -1) != 32
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~512) - ~512) - (~this.neighbours + 1 + -1) != 512
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~32) - ~32) - (~(this.neighbours - 1) + -1) != 32
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~2048) - ~2048) - (~(this.neighbours - 1) + -1) == 2048) {
               event.renderer.line(x1, y2, z1, x2, y2, z1, lineColor);
            }

            if (~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~2) - ~2) - (~(this.neighbours - 1) + -1) != 2
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~512) - ~512) - (~this.neighbours + 1 + -1) != 512
               || ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~2) - ~2) - (~this.neighbours + 1 + -1) != 2
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~1024) - ~1024) - (~this.neighbours + 1 + -1) == 1024) {
               event.renderer.line(x1, y2, z2, x2, y2, z2, lineColor);
            }

            if (~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~128) - ~128) - (~(this.neighbours - 1) + -1) != 128
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~16384) - ~16384) - (~this.neighbours + 1 + -1) != 16384
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~128) - ~128) - (~(this.neighbours - 1) + -1) != 128
                  && ~(this.neighbours - 1)
                        + -1
                        + ((-(~(this.neighbours - 1) + -1) + -1 | ~ESPBlock$ConstantPool.const_iBiOsJODlzeYGx4) - ~ESPBlock$ConstantPool.const_iBiOsJODlzeYGx4)
                        - (~(this.neighbours - 1) + -1)
                     == Desc$1$ConstantPool.const_obqE83kJREc4EGj) {
               event.renderer.line(x1, y1, z1, x1, y1, z2, lineColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~8) - ~8) - (~this.neighbours + 1 + -1) != 8
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~16384) - ~16384) - (~(this.neighbours - 1) + -1) != 16384
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~8) - ~8) - (~(this.neighbours - 1) + -1) != 8
                  && ~(this.neighbours - 1)
                        + -1
                        + (
                           (-(~(this.neighbours - 1) + -1) + -1 | ~RuntimeSupport$ConstantPool.const_JtQWyTHJNvw8jtF)
                              - ~RuntimeSupport$ConstantPool.const_JtQWyTHJNvw8jtF
                        )
                        - (~this.neighbours + 1 + -1)
                     == IListener$ConstantPool.const_lIl18tdrq9TDi1x) {
               event.renderer.line(x2, y1, z1, x2, y1, z2, lineColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~128) - ~128) - (~(this.neighbours - 1) + -1) != 128
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~512) - ~512) - (~(this.neighbours - 1) + -1) != 512
               || ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~128) - ~128) - (~this.neighbours + 1 + -1) != 128
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~8192) - ~8192) - (~this.neighbours + 1 + -1) == 8192) {
               event.renderer.line(x1, y2, z1, x1, y2, z2, lineColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~8) - ~8) - (~this.neighbours + 1 + -1) != 8
                  && ~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~512) - ~512) - (~this.neighbours + 1 + -1) != 512
               || ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~8) - ~8) - (~(this.neighbours - 1) + -1) != 8
                  && ~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~4096) - ~4096) - (~this.neighbours + 1 + -1) == 4096) {
               event.renderer.line(x2, y2, z1, x2, y2, z2, lineColor);
            }
         }

         if (shapeMode.sides()) {
            if (~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~16384) - ~16384) - (~(this.neighbours - 1) + -1) != 16384) {
               event.renderer.quadHorizontal(x1, y1, z1, x2, z2, sideColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~512) - ~512) - (~this.neighbours + 1 + -1) != 512) {
               event.renderer.quadHorizontal(x1, y2, z1, x2, z2, sideColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~2) - ~2) - (~this.neighbours + 1 + -1) != 2) {
               event.renderer.quadVertical(x1, y1, z2, x2, y2, z2, sideColor);
            }

            if (~(this.neighbours - 1) + -1 + ((-(~(this.neighbours - 1) + -1) + -1 | ~32) - ~32) - (~(this.neighbours - 1) + -1) != 32) {
               event.renderer.quadVertical(x1, y1, z1, x2, y2, z1, sideColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~8) - ~8) - (~(this.neighbours - 1) + -1) != 8) {
               event.renderer.quadVertical(x2, y1, z1, x2, y2, z2, sideColor);
            }

            if (~this.neighbours + 1 + -1 + ((-(~this.neighbours + 1 + -1) + -1 | ~128) - ~128) - (~(this.neighbours - 1) + -1) != 128) {
               event.renderer.quadVertical(x1, y1, z1, x1, y2, z2, sideColor);
            }
         }
      }
   }

   public static long getKey(int x, int y, int z) {
      return (long)y << 16
         | (long)(~(z - 1) + -1 + ((-(~(z - 1) + -1) + -1 | ~15) - ~15) - (~z + 1 + -1)) << 8
         | ~x + 1 + -1 + ((-(~x + 1 + -1) + -1 | ~15) - ~15) - (~x + 1 + -1);
   }

   public static long getKey(BlockPos blockPos) {
      return getKey(blockPos.method_10263(), blockPos.method_10264(), blockPos.method_10260());
   }
}
