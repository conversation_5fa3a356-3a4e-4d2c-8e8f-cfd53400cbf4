package meteordevelopment.meteorclient.systems.modules.render.blockesp;

public final class ESPChunk$ConstantPool {
   public static String const_b0ZakJqBovn1EdE = SVTetrGFBd(rtBzPgw65p("ϤέΪϤΗΰΥΧίΉΥδΐΥΦΨΡϤϬδΫηϾϤ"));
   public static int const_o6AmqkepS1nnnYV = (int)((int)-487778836L ^ -256971593 ^ ((int)1939143336L ^ 1641480958));
   public static String const_ZacOOnevgz9vpKS = c8nmZP7nJc(rtBzPgw65p("ˠ˨˹˨ˢ˿˩˨˻˨ˡˢ˽ˠ˨ˣ˹ʣˠ˨˹˨ˢ˿ˮˡˤ˨ˣ˹ʣˠˤ˵ˤˣʣˤˣ˩ˤ˪ˢ"));
   public static String const_6ym0JdpGNUrvaDF = j58Jq7uUmJ(rtBzPgw65p("͔͇͐"));
   public static String const_4N2tZLiwcwGqLnZ = tF9Awrymcd(rtBzPgw65p("ĂŵŲ"));
   public static String const_fXiwWvpkvFjtjLS = 7fqI2AhWq4(rtBzPgw65p("ъѹѴѵѿѰѹёѳѪѹџЮяьѽѿѷѹѨ"));
   public static String const_iqoLLMtOgGJJLga = ASNoVeBbSA(rtBzPgw65p("ϋϏϒύϐϖ\u0382ϏχϖχύϐφχϔχώύϒϏχόϖΌϏχϖχύϐρώϋχόϖΌϯχϖχύϐϡώϋχόϖΙΨ"));
   public static double const_5t1eleqif7Kicbm = Double.longBitsToDouble(
      -3885324969868493351L ^ 3645116709054370792L ^ 6800101600956579067L ^ -1800405980641865014L
   );
}
