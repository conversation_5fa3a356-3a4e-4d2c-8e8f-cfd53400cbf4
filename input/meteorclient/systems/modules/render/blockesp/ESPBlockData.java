package meteordevelopment.meteorclient.systems.modules.render.blockesp;

import javassist.CtClass$ConstantPool;
import javassist.bytecode.ByteArray$ConstantPool;
import javassist.bytecode.stackmap.TypeData$ArrayElement$ConstantPool;
import javax.annotation.Nonnegative$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.FakePlayerCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.InteractEntityEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.GuiTheme;
import meteordevelopment.meteorclient.gui.WidgetScreen;
import meteordevelopment.meteorclient.gui.utils.IScreenFactory;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.CustomTextRenderer$ConstantPool;
import meteordevelopment.meteorclient.settings.BlockDataSetting;
import meteordevelopment.meteorclient.settings.IBlockData;
import meteordevelopment.meteorclient.systems.Systems$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.Marker$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Excavator$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.IChangeable;
import meteordevelopment.meteorclient.utils.misc.ICopyable;
import meteordevelopment.meteorclient.utils.misc.ISerializable;
import meteordevelopment.meteorclient.utils.player.CustomPlayerInput$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import net.minecraft.block.Block;
import net.minecraft.nbt.NbtCompound;
import org.reflections.serializers.XmlSerializer$ConstantPool;

public class ESPBlockData implements ICopyable<ESPBlockData>, ISerializable<ESPBlockData>, IChangeable, IBlockData<ESPBlockData>, IScreenFactory {
   public ShapeMode shapeMode;
   public SettingColor lineColor;
   public SettingColor sideColor;
   public boolean tracer;
   public SettingColor tracerColor;
   private boolean changed;

   public ESPBlockData(ShapeMode shapeMode, SettingColor lineColor, SettingColor sideColor, boolean tracer, SettingColor tracerColor) {
      this.shapeMode = shapeMode;
      this.lineColor = lineColor;
      this.sideColor = sideColor;
      this.tracer = tracer;
      this.tracerColor = tracerColor;
   }

   @Override
   public WidgetScreen createScreen(GuiTheme theme, Block block, BlockDataSetting<ESPBlockData> setting) {
      return new ESPBlockDataScreen(theme, this, block, setting);
   }

   @Override
   public WidgetScreen createScreen(GuiTheme theme) {
      return new ESPBlockDataScreen(theme, this, null, null);
   }

   @Override
   public boolean isChanged() {
      return this.changed;
   }

   public void changed() {
      this.changed = true;
   }

   public void tickRainbow() {
      this.lineColor.update();
      this.sideColor.update();
      this.tracerColor.update();
   }

   public ESPBlockData set(ESPBlockData value) {
      this.shapeMode = value.shapeMode;
      this.lineColor.set((Color)value.lineColor);
      this.sideColor.set((Color)value.sideColor);
      this.tracer = value.tracer;
      this.tracerColor.set((Color)value.tracerColor);
      this.changed = value.changed;
      return this;
   }

   public ESPBlockData copy() {
      return new ESPBlockData(
         this.shapeMode, new SettingColor(this.lineColor), new SettingColor(this.sideColor), this.tracer, new SettingColor(this.tracerColor)
      );
   }

   @Override
   public NbtCompound toTag() {
      NbtCompound tag = new NbtCompound();
      tag.method_10582(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(CustomPlayerInput$ConstantPool.const_PFDal10yEONEvGy))), this.shapeMode.name());
      tag.method_10566(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(CustomTextRenderer$ConstantPool.const_OOdVTXMFCYpyyTE))), this.lineColor.toTag());
      tag.method_10566(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(Excavator$ConstantPool.const_Ys5FpqAF5c9zvF6))), this.sideColor.toTag());
      tag.method_10556(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(InteractEntityEvent$ConstantPool.const_qprJQlZJZAVwyPL))), this.tracer);
      tag.method_10566(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(Systems$ConstantPool.const_ngv0sQHTVIeyTDl))), this.tracerColor.toTag());
      tag.method_10556(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(Nonnegative$ConstantPool.const_e9qCFGorSseNo7X))), this.changed);
      return tag;
   }

   public ESPBlockData fromTag(NbtCompound tag) {
      this.shapeMode = ShapeMode.valueOf(tag.method_10558(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(ByteArray$ConstantPool.const_epe1yFeMCrWknov)))));
      this.lineColor.fromTag(tag.method_10562(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(FakePlayerCommand$ConstantPool.const_w1NVvnyLSCgWK1q)))));
      this.sideColor.fromTag(tag.method_10562(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(CtClass$ConstantPool.const_oa99yh9AFvgbT2L)))));
      this.tracer = tag.method_10577(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(TypeData$ArrayElement$ConstantPool.const_Ep9CQEVBA6bwSN2))));
      this.tracerColor.fromTag(tag.method_10562(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(Marker$ConstantPool.const_nqJ1bAfrTerO61y)))));
      this.changed = tag.method_10577(W1wA83IgG2(TOeFFNAHZX(BblYzNl6pg(XmlSerializer$ConstantPool.const_ySB7GjBEM8YjQhq))));
      return this;
   }
}
