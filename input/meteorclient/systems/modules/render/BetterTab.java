package meteordevelopment.meteorclient.systems.modules.render;

import javassist.CtNewClass$ConstantPool;
import javassist.SerialVersionUID$2$ConstantPool;
import javassist.bytecode.DeprecatedAttribute$ConstantPool;
import javassist.bytecode.MemberrefInfo$ConstantPool;
import javassist.compiler.MemberCodeGen$ConstantPool;
import meteordevelopment.meteorclient.MixinPlugin$ConstantPool;
import meteordevelopment.meteorclient.events.meteor.ActiveModulesChangedEvent$ConstantPool;
import meteordevelopment.meteorclient.events.meteor.KeyEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent$Send$ConstantPool;
import meteordevelopment.meteorclient.events.render.RenderBossBarEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.StorageBlockListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.ModulesTab$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.StarscriptTextBoxRenderer$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.KeybindSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.accounts.UuidToProfileResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.config.Config;
import meteordevelopment.meteorclient.systems.friends.Friend;
import meteordevelopment.meteorclient.systems.friends.Friends;
import meteordevelopment.meteorclient.systems.hud.screens.HudEditorScreen$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.Quiver$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.MarkerFactory$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$BlockadeType$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.LiquidFiller$Shape$ConstantPool;
import meteordevelopment.meteorclient.utils.files.StreamUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.render.ByteTexture$Format$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.listeners.LambdaListener$Factory$ConstantPool;
import meteordevelopment.starscript.value.ValueType$ConstantPool;
import net.minecraft.client.network.PlayerListEntry;
import net.minecraft.text.MutableText;
import net.minecraft.text.Text;
import net.minecraft.text.TextColor;
import net.minecraft.util.Formatting;
import net.minecraft.world.GameMode;

public class BetterTab extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   public final Setting<Integer> tabSize = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(LiquidFiller$Shape$ConstantPool.const_KFaOZt11aXGa41q))))
            .description(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(MixinPlugin$ConstantPool.const_nFYef6gSO6dqgqt))))
            .defaultValue(100)
            .min(1)
            .sliderRange(1, 1000)
            .build()
      );
   public final Setting<Integer> tabHeight = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(KeybindSetting$ConstantPool.const_iancwRrjYae2QY7))))
            .description(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(RenderBossBarEvent$ConstantPool.const_zgSjeUa4NBjz46r))))
            .defaultValue(20)
            .min(1)
            .sliderRange(1, 1000)
            .build()
      );
   private final Setting<Boolean> self = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(UuidToProfileResponse$ConstantPool.const_F4YMWAEiVPpm6rT))))
            .description(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(MemberrefInfo$ConstantPool.const_Fg3IdeI95eHb56p))))
            .defaultValue(true)
            .build()
      );
   private final Setting<SettingColor> selfColor = this.sgGeneral
      .add(
         new ColorSetting.Builder()
            .name(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(ByteTexture$Format$ConstantPool.const_YBWVBIxxWwcYwIv))))
            .description(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(ValueType$ConstantPool.const_67pgOEx7yqJg67q))))
            .defaultValue(new SettingColor(250, 130, 30))
            .visible(this.self::get)
            .build()
      );
   private final Setting<Boolean> friends = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(StorageBlockListSettingScreen$ConstantPool.const_m2uGAl1Fo92dBF3))))
            .description(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(ModulesTab$ConstantPool.const_AIIoOKjiShYLVji))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Boolean> accurateLatency = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(PacketEvent$Send$ConstantPool.const_e4jZ9FpcJ4G1IGN))))
            .description(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(Quiver$ConstantPool.const_RJibwSWTaQBBIy4))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> gamemode = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(CtNewClass$ConstantPool.const_gGyNeLqSJO4Nvcd))))
            .description(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(StarscriptTextBoxRenderer$ConstantPool.const_eAAsTyL7qk4brDc))))
            .defaultValue(false)
            .build()
      );

   public BetterTab() {
      super(
         Categories.Render,
         BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(HudEditorScreen$1$ConstantPool.const_g4xyaJAgGD6DHPk))),
         new StringBuilder(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(ActiveModulesChangedEvent$ConstantPool.const_qlkrsSdNnSm8fWu)))),
         BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(HighwayBuilder$BlockadeType$ConstantPool.const_1D0yAsI9QDIzWFA)))
      );
   }

   public Text getPlayerName(PlayerListEntry playerListEntry) {
      Color color = null;
      Text name = playerListEntry.method_2971();
      if (name == null) {
         name = Text.method_43470(playerListEntry.method_2966().getName());
      }

      if (playerListEntry.method_2966().getId().toString().equals(mc.field_1724.method_7334().getId().toString()) && this.self.get()) {
         color = this.selfColor.get();
      } else if (this.friends.get() && Friends.get().isFriend(playerListEntry)) {
         Friend friend = Friends.get().get(playerListEntry);
         if (friend != null) {
            color = Config.get().friendColor.get();
         }
      }

      if (color != null) {
         String nameString = name.getString();

         for (Formatting format : Formatting.values()) {
            if (format.method_543()) {
               nameString = nameString.replace(format.toString(), BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(MemberCodeGen$ConstantPool.const_WjWLPoPvCrles4y))));
            }
         }

         name = Text.method_43470(nameString).method_10862(name.method_10866().method_27703(TextColor.method_27717(color.getPacked())));
      }

      if (this.gamemode.get()) {
         GameMode gm = playerListEntry.method_2958();
         String gmText = BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(KeyEvent$ConstantPool.const_e1vk48oARdJesSw)));
         if (gm != null) {
            gmText = switch (gm) {
               case field_9219 -> BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(SerialVersionUID$2$ConstantPool.const_NnBqAzQ4z9bFDFa)));
               case field_9215 -> BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(StreamUtils$ConstantPool.const_YJ6LtQixWKtCRqr)));
               case field_9220 -> BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(MarkerFactory$ConstantPool.const_VLLL8BYykmDoUl4)));
               case field_9216 -> BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(DeprecatedAttribute$ConstantPool.const_w0dvaJvLFwLtom2)));
               default -> throw new MatchException(null, null);
            };
         }

         MutableText text = Text.method_43470(BJVg3wtQew(2b2PVmoEiP(ez6J3trdpJ(LambdaListener$Factory$ConstantPool.const_ZgSjj9XdupZuThs))));
         text.method_10852(name);
         text.method_27693(" [" + gmText + "]");
         name = text;
      }

      return name;
   }
}
