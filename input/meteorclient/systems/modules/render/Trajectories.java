package meteordevelopment.meteorclient.systems.modules.render;

import io.netty.handler.codec.socks.SocksInitRequestDecoder$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5ServerEncoder$ConstantPool;
import io.netty.handler.proxy.Socks5ProxyHandler$ConstantPool;
import java.util.ArrayList;
import java.util.List;
import javassist.bytecode.ByteStream$ConstantPool;
import javassist.bytecode.SignatureAttribute$ClassType$ConstantPool;
import javassist.bytecode.TypeAnnotationsAttribute$Renamer$ConstantPool;
import javassist.bytecode.TypeAnnotationsAttribute$SubCopier$ConstantPool;
import javassist.bytecode.TypeAnnotationsAttribute$TAWalker$ConstantPool;
import javassist.bytecode.analysis.ControlFlow$1$ConstantPool;
import javassist.bytecode.analysis.Frame$ConstantPool;
import javassist.bytecode.analysis.IntQueue$1$ConstantPool;
import javassist.bytecode.annotation.AnnotationsWriter$ConstantPool;
import javassist.bytecode.annotation.BooleanMemberValue$ConstantPool;
import javassist.convert.TransformCall$ConstantPool;
import javax.annotation.MatchesPattern$Checker$ConstantPool;
import meteordevelopment.meteorclient.Main$OperatingSystem$1$ConstantPool;
import meteordevelopment.meteorclient.asm.transformers.CanvasWorldRendererTransformer$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.VClipCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.EntityAddedEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.ItemUseCrosshairTargetEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.gui.widgets.WItemWithLabel$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.ICamera$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IClientPlayerInteractionManager$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.ShapeMode$ConstantPool;
import meteordevelopment.meteorclient.renderer.text.FontFamily$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.ItemListSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StatusEffectListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.AutoAnvil$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoCity$SwitchMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoWeb$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.BedAura$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.CrystalAura$YawStepMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AttackSwitchHammer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoDrink$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoPlow$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.LookBack$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.SkyLadder$BaseMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes.Bounce$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.AutoShearer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Nuker$SortMode$ConstantPool;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.entity.ProjectileEntitySimulator;
import meteordevelopment.meteorclient.utils.entity.Target$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.Pool;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.value.Value$Map$ConstantPool;
import meteordevelopment.starscript.value.ValueType$ConstantPool;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.projectile.ProjectileEntity;
import net.minecraft.item.CrossbowItem;
import net.minecraft.item.EggItem;
import net.minecraft.item.EnderPearlItem;
import net.minecraft.item.ExperienceBottleItem;
import net.minecraft.item.FishingRodItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.RangedWeaponItem;
import net.minecraft.item.SnowballItem;
import net.minecraft.item.ThrowablePotionItem;
import net.minecraft.item.TridentItem;
import net.minecraft.item.WindChargeItem;
import net.minecraft.registry.Registries;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.hit.HitResult.Type;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import org.joml.Vector3d;
import org.reflections.ReflectionUtils$2$ConstantPool;
import org.reflections.scanners.Scanners$7$ConstantPool;
import org.reflections.util.FilterBuilder$Include$ConstantPool;

public class Trajectories extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgRender = this.settings.createGroup(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(AutoWeb$ConstantPool.const_9YvLTXNnLh5VZeG))));
   private final Setting<List<Item>> items = this.sgGeneral
      .add(
         new ItemListSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(FontFamily$ConstantPool.const_7HjbFrpV6epFoLD))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(VClipCommand$ConstantPool.const_LYzS6quhA4o7Uzn))))
            .defaultValue(this.getDefaultItems())
            .filter(this::itemFilter)
            .build()
      );
   private final Setting<Boolean> otherPlayers = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(AttackSwitchHammer$ConstantPool.const_6HVd1JJiFQosjEn))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(AutoPlow$ConstantPool.const_U1aq1NyQcJAxsoS))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> firedProjectiles = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(Frame$ConstantPool.const_e6GvTTjI0tve4iQ))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(ControlFlow$1$ConstantPool.const_Q6BDowjrkBAbRqO))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> accurate = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(LightOverlay$1$ConstantPool.const_6iw9TQv2rFlDTgA))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(ValueType$ConstantPool.const_GewOvZqT7gvvhLg))))
            .defaultValue(false)
            .build()
      );
   public final Setting<Integer> simulationSteps = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(ICamera$ConstantPool.const_YfjdeQDOf74KDOa))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(TypeAnnotationsAttribute$Renamer$ConstantPool.const_S7cFca6eoeoJTY9))))
            .defaultValue(500)
            .sliderMax(5000)
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(Target$ConstantPool.const_hwL7JeUeArAxJqw)))))
                  .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(Main$OperatingSystem$1$ConstantPool.const_4YSd7TNyBQQ6Pov)))))
               .defaultValue(ShapeMode.Both))
            .build()
      );
   private final Setting<SettingColor> sideColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(AutoDrink$ConstantPool.const_CMvqovlCT1O2bJS))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(CanvasWorldRendererTransformer$ConstantPool.const_WbfiAFOk9RMA0mc))))
            .defaultValue(new SettingColor(255, 150, 0, 35))
            .build()
      );
   private final Setting<SettingColor> lineColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(BedAura$ConstantPool.const_k7jD4KVq9GohDKo))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(CrystalAura$YawStepMode$ConstantPool.const_XeQcVv9iFabL0dl))))
            .defaultValue(new SettingColor(255, 150, 0))
            .build()
      );
   private final Setting<Boolean> renderPositionBox = this.sgRender
      .add(
         new BoolSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(Socks5ServerEncoder$ConstantPool.const_HQT3BtOW2NPIFHL))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(Scanners$7$ConstantPool.const_wVe6Yq16NoK1yUc))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Double> positionBoxSize = this.sgRender
      .add(
         new DoubleSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(IClientPlayerInteractionManager$ConstantPool.const_jGtpFyDzh3YeLh7))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(LookBack$ConstantPool.const_qQoFlrqyyY7A4tw))))
            .defaultValue(MatchesPattern$Checker$ConstantPool.const_GOS1lVItV1GBUds)
            .sliderRange(AutoAnvil$ConstantPool.const_b3GYDW4JS8fuNwI, TransformCall$ConstantPool.const_sEgvWL9wM1Nwe8r)
            .visible(this.renderPositionBox::get)
            .build()
      );
   private final Setting<SettingColor> positionSideColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(ByteStream$ConstantPool.const_An7p1lYEizt3yCF))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(Value$Map$ConstantPool.const_bNwEJNWnve8XDF7))))
            .defaultValue(new SettingColor(255, 150, 0, 35))
            .visible(this.renderPositionBox::get)
            .build()
      );
   private final Setting<SettingColor> positionLineColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(ShapeMode$ConstantPool.const_OH6cFgOVdBnYrTT))))
            .description(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(TypeAnnotationsAttribute$SubCopier$ConstantPool.const_R8OVsHDnti4St20))))
            .defaultValue(new SettingColor(255, 150, 0))
            .visible(this.renderPositionBox::get)
            .build()
      );
   private final ProjectileEntitySimulator simulator = new ProjectileEntitySimulator();
   private final Pool<Vector3d> vec3s = new Pool<>(Vector3d::new);
   private final List<Trajectories.Path> paths = new ArrayList<>();
   private static final double MULTISHOT_OFFSET = Math.toRadians(EntityAddedEvent$ConstantPool.const_YTtdQoj1DlOTFrW);

   public Trajectories() {
      super(
         Categories.Render,
         jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(IntQueue$1$ConstantPool.const_UwzDsyOYRdiw6Qn))),
         new StringBuilder(jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(Socks5ProxyHandler$ConstantPool.const_LN1hTDVeTdhWz6Y)))),
         jQJ6CPsAeR(G2GYZ3VlJ9(NdTzBtAqqF(AnnotationsWriter$ConstantPool.const_0OBGJALsDa9dw8e)))
      );
   }

   private boolean itemFilter(Item item) {
      return item instanceof RangedWeaponItem
         || item instanceof FishingRodItem
         || item instanceof TridentItem
         || item instanceof SnowballItem
         || item instanceof EggItem
         || item instanceof EnderPearlItem
         || item instanceof ExperienceBottleItem
         || item instanceof ThrowablePotionItem
         || item instanceof WindChargeItem;
   }

   private List<Item> getDefaultItems() {
      List<Item> items = new ArrayList<>();

      for (Item item : Registries.field_41178) {
         if (this.itemFilter(item)) {
            items.add(item);
         }
      }

      return items;
   }

   private Trajectories.Path getEmptyPath() {
      for (Trajectories.Path path : this.paths) {
         if (path.points.isEmpty()) {
            return path;
         }
      }

      Trajectories.Path pathx = new Trajectories.Path();
      this.paths.add(pathx);
      return pathx;
   }

   private void calculatePath(PlayerEntity player, float tickDelta) {
      for (Trajectories.Path path : this.paths) {
         path.clear();
      }

      ItemStack itemStack = player.method_6047();
      if (!this.items.get().contains(itemStack.method_7909())) {
         itemStack = player.method_6079();
         if (!this.items.get().contains(itemStack.method_7909())) {
            return;
         }
      }

      if (this.simulator.set(player, itemStack, 0.0, this.accurate.get(), tickDelta)) {
         this.getEmptyPath().calculate();
         if (itemStack.method_7909() instanceof CrossbowItem && Utils.hasEnchantment(itemStack, Enchantments.field_9108)) {
            if (!this.simulator.set(player, itemStack, MULTISHOT_OFFSET, this.accurate.get(), tickDelta)) {
               return;
            }

            this.getEmptyPath().calculate();
            if (!this.simulator.set(player, itemStack, -MULTISHOT_OFFSET, this.accurate.get(), tickDelta)) {
               return;
            }

            this.getEmptyPath().calculate();
         }
      }
   }

   private void calculateFiredPath(Entity entity, double tickDelta) {
      for (Trajectories.Path path : this.paths) {
         path.clear();
      }

      if (this.simulator.set(entity, this.accurate.get())) {
         this.getEmptyPath().setStart(entity, tickDelta).calculate();
      }
   }

   @EventHandler
   private void onRender(Render3DEvent event) {
      float tickDelta = mc.field_1687.method_54719().method_54754() ? 1.0F : event.tickDelta;

      for (PlayerEntity player : mc.field_1687.method_18456()) {
         if (this.otherPlayers.get() || player == mc.field_1724) {
            this.calculatePath(player, tickDelta);

            for (Trajectories.Path path : this.paths) {
               path.render(event);
            }
         }
      }

      if (this.firedProjectiles.get()) {
         for (Entity entity : mc.field_1687.method_18112()) {
            if (entity instanceof ProjectileEntity) {
               this.calculateFiredPath(entity, tickDelta);

               for (Trajectories.Path path : this.paths) {
                  path.render(event);
               }
            }
         }
      }
   }

   private class Path {
      private final List<Vector3d> points = new ArrayList<>();
      private boolean hitQuad;
      private boolean hitQuadHorizontal;
      private double hitQuadX1;
      private double hitQuadY1;
      private double hitQuadZ1;
      private double hitQuadX2;
      private double hitQuadY2;
      private double hitQuadZ2;
      private Entity collidingEntity;
      public Vector3d lastPoint;

      public void clear() {
         for (Vector3d point : this.points) {
            Trajectories.this.vec3s.free(point);
         }

         this.points.clear();
         this.hitQuad = false;
         this.collidingEntity = null;
         this.lastPoint = null;
      }

      public void calculate() {
         this.addPoint();

         for (int i = 0;
            i < (Trajectories.this.simulationSteps.get() > 0 ? Trajectories.this.simulationSteps.get() : LightOverlay$ConstantPool.const_jVrsLanPYBGoNDV);
            i++
         ) {
            HitResult result = Trajectories.this.simulator.tick();
            if (result != null) {
               this.processHitResult(result);
               break;
            }

            this.addPoint();
         }
      }

      public Trajectories.Path setStart(Entity entity, double tickDelta) {
         this.lastPoint = new Vector3d(
            MathHelper.method_16436(tickDelta, entity.field_6038, entity.method_23317()),
            MathHelper.method_16436(tickDelta, entity.field_5971, entity.method_23318()),
            MathHelper.method_16436(tickDelta, entity.field_5989, entity.method_23321())
         );
         return this;
      }

      private void addPoint() {
         this.points.add(Trajectories.this.vec3s.get().set(Trajectories.this.simulator.pos));
      }

      private void processHitResult(HitResult result) {
         if (result.method_17783() == Type.field_1332) {
            BlockHitResult r = (BlockHitResult)result;
            this.hitQuad = true;
            this.hitQuadX1 = r.method_17784().field_1352;
            this.hitQuadY1 = r.method_17784().field_1351;
            this.hitQuadZ1 = r.method_17784().field_1350;
            this.hitQuadX2 = r.method_17784().field_1352;
            this.hitQuadY2 = r.method_17784().field_1351;
            this.hitQuadZ2 = r.method_17784().field_1350;
            if (r.method_17780() == Direction.field_11036 || r.method_17780() == Direction.field_11033) {
               this.hitQuadHorizontal = true;
               this.hitQuadX1 = this.hitQuadX1 - SignatureAttribute$ClassType$ConstantPool.const_0ouKmRg7AKPthFs;
               this.hitQuadZ1 = this.hitQuadZ1 - SocksInitRequestDecoder$ConstantPool.const_AKjnm7io1LFCAf2;
               this.hitQuadX2 = this.hitQuadX2 + BooleanMemberValue$ConstantPool.const_FNOD9vWhyXOqLxQ;
               this.hitQuadZ2 = this.hitQuadZ2 + ReflectionUtils$2$ConstantPool.const_jfyInIlFNbf5avO;
            } else if (r.method_17780() != Direction.field_11043 && r.method_17780() != Direction.field_11035) {
               this.hitQuadHorizontal = false;
               this.hitQuadZ1 = this.hitQuadZ1 - Bounce$1$ConstantPool.const_LLABegN7D6qgUmB;
               this.hitQuadY1 = this.hitQuadY1 - Nuker$SortMode$ConstantPool.const_RF3JdmQ44gigJCH;
               this.hitQuadZ2 = this.hitQuadZ2 + TypeAnnotationsAttribute$TAWalker$ConstantPool.const_bclyWc4t0dLwx2I;
               this.hitQuadY2 = this.hitQuadY2 + StatusEffectListSetting$Builder$ConstantPool.const_jnTL7m7WyZ0qV0L;
            } else {
               this.hitQuadHorizontal = false;
               this.hitQuadX1 = this.hitQuadX1 - ItemUseCrosshairTargetEvent$ConstantPool.const_Rwd72lq7GYEnUEb;
               this.hitQuadY1 = this.hitQuadY1 - FilterBuilder$Include$ConstantPool.const_PDBuW7qy6uNpQeQ;
               this.hitQuadX2 = this.hitQuadX2 + AutoShearer$ConstantPool.const_u88tByCJbnjBbrA;
               this.hitQuadY2 = this.hitQuadY2 + WItemWithLabel$ConstantPool.const_fO59LqK5kwlAbTj;
            }

            this.points.add(Utils.set(Trajectories.this.vec3s.get(), result.method_17784()));
         } else if (result.method_17783() == Type.field_1331) {
            this.collidingEntity = ((EntityHitResult)result).method_17782();
            this.points.add(Utils.set(Trajectories.this.vec3s.get(), result.method_17784()).add(0.0, this.collidingEntity.method_17682() / 2.0F, 0.0));
         }
      }

      public void render(Render3DEvent event) {
         for (Vector3d point : this.points) {
            if (this.lastPoint != null) {
               event.renderer.line(this.lastPoint.x, this.lastPoint.y, this.lastPoint.z, point.x, point.y, point.z, Trajectories.this.lineColor.get());
               if (Trajectories.this.renderPositionBox.get()) {
                  event.renderer
                     .box(
                        point.x - Trajectories.this.positionBoxSize.get(),
                        point.y - Trajectories.this.positionBoxSize.get(),
                        point.z - Trajectories.this.positionBoxSize.get(),
                        point.x + Trajectories.this.positionBoxSize.get(),
                        point.y + Trajectories.this.positionBoxSize.get(),
                        point.z + Trajectories.this.positionBoxSize.get(),
                        Trajectories.this.positionSideColor.get(),
                        Trajectories.this.positionLineColor.get(),
                        Trajectories.this.shapeMode.get(),
                        0
                     );
               }
            }

            this.lastPoint = point;
         }

         if (this.hitQuad) {
            if (this.hitQuadHorizontal) {
               event.renderer
                  .sideHorizontal(
                     this.hitQuadX1,
                     this.hitQuadY1,
                     this.hitQuadZ1,
                     this.hitQuadX1 + AutoCity$SwitchMode$ConstantPool.const_VDVag8c81PVEDBb,
                     this.hitQuadZ1 + SkyLadder$BaseMode$ConstantPool.const_p2t6GDjqTJEDUME,
                     Trajectories.this.sideColor.get(),
                     Trajectories.this.lineColor.get(),
                     Trajectories.this.shapeMode.get()
                  );
            } else {
               event.renderer
                  .sideVertical(
                     this.hitQuadX1,
                     this.hitQuadY1,
                     this.hitQuadZ1,
                     this.hitQuadX2,
                     this.hitQuadY2,
                     this.hitQuadZ2,
                     Trajectories.this.sideColor.get(),
                     Trajectories.this.lineColor.get(),
                     Trajectories.this.shapeMode.get()
                  );
            }
         }

         if (this.collidingEntity != null) {
            double x = (this.collidingEntity.method_23317() - this.collidingEntity.field_6014) * event.tickDelta;
            double y = (this.collidingEntity.method_23318() - this.collidingEntity.field_6036) * event.tickDelta;
            double z = (this.collidingEntity.method_23321() - this.collidingEntity.field_5969) * event.tickDelta;
            Box box = this.collidingEntity.method_5829();
            event.renderer
               .box(
                  x + box.field_1323,
                  y + box.field_1322,
                  z + box.field_1321,
                  x + box.field_1320,
                  y + box.field_1325,
                  z + box.field_1324,
                  Trajectories.this.sideColor.get(),
                  Trajectories.this.lineColor.get(),
                  Trajectories.this.shapeMode.get(),
                  0
               );
         }
      }
   }
}
