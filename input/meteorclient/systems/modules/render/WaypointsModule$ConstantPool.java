package meteordevelopment.meteorclient.systems.modules.render;

public final class WaypointsModule$ConstantPool {
   public static int const_aaZOytbJzidQUyY = (int)(-888628817866730921L ^ 888628818847057411L) ^ (int)1538404069L ^ -622904404;
   public static String const_nOrTz2ltq4gE1I1 = pIFo1GQ6or(nMQLTgtQ7g("ȫȣȾȺȷ"));
   public static String const_y9c84MOwPk35ZLc = w66iUrG1bV(nMQLTgtQ7g("٪١ٶ١خ٬١ٮ٧خُ٢٪٥٣ٴ"));
   public static String const_oJ8YbTrF0AWMeQ2 = 05V8BOb7wF(nMQLTgtQ7g("ӮҊҬҧҰҧөҪҧҨҡө҉ҤҬңҥҲӽҏ҅ӯҐ"));
   public static String const_4aQttFG1GTWtxOs = 7ws5yU4ld8(nMQLTgtQ7g("_BM^I"));
   public static double const_qjMjImVhGK6lF1M = Double.longBitsToDouble(
      5521897121644842617L ^ -7513737737405142357L ^ 8435945638614667556L ^ -1294203631301397002L
   );
   public static int const_vY43ikYyZUQVlWy = (int)((int)(440040594 ^ -678026287) ^ (int)(970179647 ^ -192963650));
   public static double const_KV4bnidWb0WqJUx = Double.longBitsToDouble(
      -1846255627686719174L ^ 5760251082773150221L ^ -381039253923126269L ^ 1371921473345998644L
   );
   public static String const_JrVChik376qdYal = d0qoyebHLF(nMQLTgtQ7g("*=0)9"));
   public static String const_RcIwFQQVQGVOtA6 = TBoXaZBuik(nMQLTgtQ7g("қҙқҐҝ"));
   public static long const_n5bYFuBdBT16SoY = 1580030666223652236L ^ -3162728454371536716L ^ 4573717944596873472L ^ -785704687426544536L;
   public static String const_YXDWNfaxL8Yj4g1 = jiqYwBwo6N(nMQLTgtQ7g("ӝәӃӓҝӓӟӜӟӂ"));
   public static String const_Gfm7vSQTDv9wOjt = TFWAAyrefS(nMQLTgtQ7g("ŹŅňčŃŌŀňčŏŌŎņŊşłŘŃŉčŎłŁłşă"));
   public static String const_cnEOv0Vb9KO4BwX = 0Wjwzo6YuI(nMQLTgtQ7g("Îÿìê÷ýòûí¾êñ¾ðñê¾ìûðúûì°"));
   public static double const_liWtblpeeLanboz = Double.longBitsToDouble(
      -6958413050806170684L ^ 664376989967928128L ^ 6279629056471949483L ^ -100435767934738385L
   );
   public static int const_2rBYQaFDe68cA05 = (int)((int)(1964060946 ^ -1277284968) ^ ((int)1073948717L ^ -2033438245));
   public static String const_RL2dANGrZaSdlol = PIoOSBN4ur(nMQLTgtQ7g("סזםחזׁ"));
   public static String const_aTfSVcm7JotSALP = VkGA0uLy4A(nMQLTgtQ7g("ŠŮŭšũűįŶŭįŲŮţšŧ"));
   public static String const_OzfDabbdebTq44a = 3SGLLATTGa(nMQLTgtQ7g("̵̵̨̢̤̤̥̣Ϳ̵̢̳̤̱̹̿̾̾;̷̠̾"));
   public static double const_eyI4N7PP3rKSYrn = Double.longBitsToDouble(
      8581163985777644045L ^ 679401847123657507L ^ -6706328625829759152L ^ -2056589314300041602L
   );
}
