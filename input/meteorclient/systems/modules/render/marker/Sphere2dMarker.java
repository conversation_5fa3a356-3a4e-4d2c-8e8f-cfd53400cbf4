package meteordevelopment.meteorclient.systems.modules.render.marker;

import java.util.ArrayList;
import java.util.List;
import javassist.bytecode.analysis.Analyzer$ConstantPool;
import javassist.bytecode.annotation.AnnotationImpl$ConstantPool;
import javassist.bytecode.annotation.ByteMemberValue$ConstantPool;
import javassist.bytecode.stackmap.TypeData$ConstantPool;
import javassist.util.proxy.DefineClassHelper$Java11$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PushEntityEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.gui.screens.MarkerScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WHorizontalSeparator$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.BlockPosSetting;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.KeybindSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.combat.AutoArmor$ArmorPiece$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoTrap$TopMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.Surround$Center$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoCrit$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AntiVoid$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.Jesus$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$DistanceColorMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.LiquidFiller$ListMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.VeinMiner$ListMode$ConstantPool;
import meteordevelopment.meteorclient.utils.ReflectInit$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.TargetUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.Keybind;
import meteordevelopment.meteorclient.utils.network.MeteorExecutor;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.starscript.value.Value$Map$ConstantPool;
import net.minecraft.util.math.BlockPos;
import org.reflections.scanners.MethodAnnotationsScanner$ConstantPool;
import org.reflections.util.FilterBuilder$ConstantPool;
import org.reflections.vfs.JarInputDir$1$ConstantPool;

public class Sphere2dMarker extends BaseMarker {
   public static final String type = "Sphere-2D";
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgRender = this.settings.createGroup(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(AnnotationImpl$ConstantPool.const_fyohAbp6lBS5So4))));
   private final SettingGroup sgKeybinding = this.settings.createGroup(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(TypeData$ConstantPool.const_P65qjKdFhe5kb9D))));
   private final Setting<BlockPos> center = this.sgGeneral
      .add(
         new BlockPosSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(AutoCrit$ConstantPool.const_9WB1fiH9ECLdBfT))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(LiquidFiller$ListMode$ConstantPool.const_IP3F2BaDyDyW6N1))))
            .onChanged(bp -> this.dirty = true)
            .build()
      );
   private final Setting<Integer> radius = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(DefineClassHelper$Java11$ConstantPool.const_4FJZnKlsXBaYNND))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(ReflectInit$ConstantPool.const_yBtJFG6hCOanDNr))))
            .defaultValue(20)
            .min(1)
            .noSlider()
            .onChanged(r -> this.dirty = true)
            .build()
      );
   private final Setting<Integer> layer = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(AntiVoid$ConstantPool.const_Iu0lh0wzW9hjTgH))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(Nametags$DistanceColorMode$ConstantPool.const_XNuFrlrtyNw814d))))
            .defaultValue(0)
            .min(0)
            .noSlider()
            .onChanged(l -> this.dirty = true)
            .build()
      );
   private final Setting<Boolean> limitRenderRange = this.sgRender
      .add(
         new BoolSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(PushEntityEvent$ConstantPool.const_QpT4LS9eYofpNjq))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(MethodAnnotationsScanner$ConstantPool.const_8B8SOTIEaYcEDgn))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Integer> renderRange = this.sgRender
      .add(
         new IntSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(Surround$Center$ConstantPool.const_WGlaVIagvFQQ9kZ))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(MarkerScreen$ConstantPool.const_BJPpIoXCLqjY9b7))))
            .defaultValue(10)
            .min(1)
            .sliderRange(1, 20)
            .visible(this.limitRenderRange::get)
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(AutoArmor$ArmorPiece$ConstantPool.const_VPVqw4J1JAU330L)))))
                  .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(VeinMiner$ListMode$ConstantPool.const_YjUneGuXtrNBHcX)))))
               .defaultValue(ShapeMode.Both))
            .build()
      );
   private final Setting<SettingColor> sideColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(Analyzer$ConstantPool.const_kwaB7bVyIEZA9Mg))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(Value$Map$ConstantPool.const_bnGSuevqHURz56B))))
            .defaultValue(new SettingColor(0, 100, 255, 50))
            .build()
      );
   private final Setting<SettingColor> lineColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(Surround$Center$ConstantPool.const_SMuT9tYvUIvkpGH))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(TargetUtils$ConstantPool.const_JF2VP8anDFBbD4V))))
            .defaultValue(new SettingColor(0, 100, 255, 255))
            .build()
      );
   private final Setting<Keybind> nextLayerKey = this.sgKeybinding
      .add(
         new KeybindSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(AutoTrap$TopMode$ConstantPool.const_k15K57QQW04o0TF))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(WHorizontalSeparator$ConstantPool.const_YvvroWKNQFzXoja))))
            .action(() -> {
               if (this.isVisible() && this.layer.get() < this.radius.get() * 2) {
                  this.layer.set(this.layer.get() + 1);
               }
            })
            .build()
      );
   private final Setting<Keybind> prevLayerKey = this.sgKeybinding
      .add(
         new KeybindSetting.Builder()
            .name(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(FilterBuilder$ConstantPool.const_VjCcGutFV6m9Xc8))))
            .description(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(JarInputDir$1$ConstantPool.const_I27FsARniG6Kwoc))))
            .action(() -> {
               if (this.isVisible()) {
                  this.layer.set(this.layer.get() - 1);
               }
            })
            .build()
      );
   private final List<Sphere2dMarker.Block> blocks = new ArrayList<>();
   private boolean dirty = true;
   private boolean calculating;

   public Sphere2dMarker() {
      super(We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(ByteMemberValue$ConstantPool.const_b7dtNFaiPAN27YF))));
   }

   @Override
   protected void render(Render3DEvent event) {
      if (this.dirty && !this.calculating) {
         this.calcCircle();
      }

      synchronized (this.blocks) {
         for (Sphere2dMarker.Block block : this.blocks) {
            if (!this.limitRenderRange.get() || PlayerUtils.isWithin(block.x, block.y, block.z, this.renderRange.get().intValue())) {
               event.renderer
                  .box(
                     block.x,
                     block.y,
                     block.z,
                     block.x + 1,
                     block.y + 1,
                     block.z + 1,
                     this.sideColor.get(),
                     this.lineColor.get(),
                     this.shapeMode.get(),
                     block.excludeDir
                  );
            }
         }
      }
   }

   @Override
   public String getTypeName() {
      return We72o5SBwM(OfjxuDJ6dA(D4mKelKWYO(Jesus$Mode$ConstantPool.const_iVYm6tL9JdtBdpd)));
   }

   private void calcCircle() {
      this.calculating = true;
      this.blocks.clear();
      Runnable action = () -> {
         int cX = this.center.get().method_10263();
         int cY = this.center.get().method_10264();
         int cZ = this.center.get().method_10260();
         int rSq = this.radius.get() * this.radius.get();
         int dY = 1 - (this.radius.get() & 1) + this.layer.get();
         int dX = 0;

         while (true) {
            int dZ = (int)Math.round(Math.sqrt(rSq - (dX * dX + dY * dY)));
            synchronized (this.blocks) {
               this.add(cX + dX, cY + dY, cZ + dZ);
               this.add(cX + dZ, cY + dY, cZ + dX);
               this.add(cX - dX, cY + dY, cZ - dZ);
               this.add(cX - dZ, cY + dY, cZ - dX);
               this.add(cX + dX, cY + dY, cZ - dZ);
               this.add(cX + dZ, cY + dY, cZ - dX);
               this.add(cX - dX, cY + dY, cZ + dZ);
               this.add(cX - dZ, cY + dY, cZ + dX);
            }

            if (dX >= dZ) {
               synchronized (this.blocks) {
                  for (Sphere2dMarker.Block block : this.blocks) {
                     for (Sphere2dMarker.Block b : this.blocks) {
                        if (b != block) {
                           if (b.x == block.x + 1 && b.z == block.z) {
                              block.excludeDir = 64 + (-block.excludeDir + -1 + (~64 + 1 + -1 & ~(-block.excludeDir + -1)) - (-block.excludeDir + -1));
                           }

                           if (b.x == block.x - 1 && b.z == block.z) {
                              block.excludeDir = 32 + (-block.excludeDir + -1 + (~32 + 1 + -1 & ~(-block.excludeDir + -1)) - (-block.excludeDir + -1));
                           }

                           if (b.x == block.x && b.z == block.z + 1) {
                              block.excludeDir = 16 + (-block.excludeDir + -1 + (~16 + 1 + -1 & ~(-block.excludeDir + -1)) - (-block.excludeDir + -1));
                           }

                           if (b.x == block.x && b.z == block.z - 1) {
                              block.excludeDir = 8 + (-block.excludeDir + -1 + (~(8 - 1) + -1 & ~(-block.excludeDir + -1)) - (-block.excludeDir + -1));
                           }
                        }
                     }
                  }
               }

               this.dirty = false;
               this.calculating = false;
               return;
            }

            dX++;
         }
      };
      if (this.radius.get() <= 50) {
         action.run();
      } else {
         MeteorExecutor.execute(action);
      }
   }

   private void add(int x, int y, int z) {
      for (Sphere2dMarker.Block b : this.blocks) {
         if (b.x == x && b.y == y && b.z == z) {
            return;
         }
      }

      this.blocks.add(new Sphere2dMarker.Block(x, y, z));
   }

   private static class Block {
      public final int x;
      public final int y;
      public final int z;
      public int excludeDir;

      public Block(int x, int y, int z) {
         this.x = x;
         this.y = y;
         this.z = z;
      }
   }
}
