package meteordevelopment.meteorclient.systems.modules.render.marker;

public final class MarkerFactory$Factory$ConstantPool {
   public static String const_jv2JKm1QGJRVyED = EJ01vLMg1d(g7kw8icby4("ݎݠݿݬݺܩݰݦݼܩݨݧݰܩݠݽݬݤܧ"));
   public static String const_r3NNb9Y6XdoVNCU = Obd12M17KU(g7kw8icby4("\u0082\u0097\u008a\u0090\u0095\u0096"));
   public static String const_Jrt4uWSrJ3jbuzv = aJh9wjyzLe(g7kw8icby4("\u07b9މދކޏ"));
   public static double const_3ALpzEyJ0ZbgmOB = Double.longBitsToDouble(
      -3421263372117945745L ^ 7799009045193461726L ^ -2369791768236806162L ^ 2572206849639314015L
   );
   public static String const_VatuJtsxFYYB3D9 = tEZySU8V7v(g7kw8icby4("ۖہۊۀہۖ"));
   public static String const_FuZo93vWFeQwWH4 = 18ly7e4kgw(g7kw8icby4("ȉȔȏȚȏȒȔȕ"));
   public static String const_fznVJFS74E46LVz = 6aatPSLvqD(g7kw8icby4("͊͵\u0378ͳ̽͛ͨͱͱ"));
   public static int const_olvVjiCeI8yEmxp = (int)((int)(600151968 ^ -1595049169) ^ (int)(-2022443063 ^ 73079108));
   public static int const_Qn1eWI9fen8bDQu = (int)(9073082176104137491L ^ 9073082175651028775L) ^ (int)-1664390870L ^ -2117241780;
}
