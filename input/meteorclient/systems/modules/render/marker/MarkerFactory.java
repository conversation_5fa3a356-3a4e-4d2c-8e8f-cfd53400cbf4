package meteordevelopment.meteorclient.systems.modules.render.marker;

import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthStatus$ConstantPool;
import java.util.HashMap;
import java.util.Map;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.WMeteorQuad$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Modules;

public class MarkerFactory {
   private final Map<String, MarkerFactory.Factory> factories = new HashMap<>();
   private final String[] names;

   public MarkerFactory() {
      this.factories.put(3rQ3rPHuNW(xA2f4bwRFW(6Jrl1A64jM(WMeteorQuad$ConstantPool.const_VWS12cJpTtQOudJ))), CuboidMarker::new);
      this.factories.put(3rQ3rPHuNW(xA2f4bwRFW(6Jrl1A64jM(Socks5PasswordAuthStatus$ConstantPool.const_1gYQJ0O9tMITrvl))), Sphere2dMarker::new);
      this.names = new String[this.factories.size()];
      int i = 0;

      for (String key : this.factories.keySet()) {
         this.names[i++] = key;
      }
   }

   public String[] getNames() {
      return this.names;
   }

   public BaseMarker createMarker(String name) {
      if (this.factories.containsKey(name)) {
         BaseMarker marker = this.factories.get(name).create();
         marker.settings.registerColorSettings(Modules.get().get(Marker.class));
         return marker;
      } else {
         return null;
      }
   }

   private interface Factory {
      BaseMarker create();
   }
}
