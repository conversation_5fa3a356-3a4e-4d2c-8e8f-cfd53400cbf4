package meteordevelopment.meteorclient.systems.modules.render.marker;

public final class Sphere2dMarker$Block$ConstantPool {
   public static String const_Dgzo0DpUiw4pFqQ = 2OwSf2JLEs(kyjYLCQBND("ֶָֻֻ֠ևֶָּ֥֢֥ևֲִֶֻ"));
   public static String const_NpUpC9GGaJnLwv9 = YqMbdVTJFb(
      kyjYLCQBND("ļĞĜėĚěşĜėĊđĔČşŗčĚĘĞčěēĚČČşĐęşĖęşċėĚĆŘčĚşĖđşĭľĲşĐčşČĞĉĚěşċĐşěĖČĔŖşĚćďĖčĚşĞđěşĞčĚşěĚēĚċĚěşĞęċĚčşċėĖČşđĊĒĝĚčşĐęşČĚĜĐđěČşŒŎşċĐşěĖČĞĝēĚ")
   );
   public static double const_c1NaAcXxbrRcWRC = Double.longBitsToDouble(
      -2576851121504542630L ^ -5968840028831533758L ^ -1755413481619621762L ^ -6247402299813048052L
   );
   public static double const_h0pvOHDiohoE5IO = Double.longBitsToDouble(
      6578975963204696413L ^ 8368925793816616937L ^ -7410242247901864252L ^ -700074942257252240L
   );
   public static int const_j7Jo71LFNFZfVrY = (int)((int)2065122461L ^ 986656155 ^ (int)(1838570103 ^ 743322476));
   public static String const_XQWSXj6gM2ZjiI9 = G8eBoJaeTG(kyjYLCQBND("Тђ"));
   public static String const_o2shKoYbgdV1Sqi = 3ZJh2La6YJ(kyjYLCQBND("ЏРлкнѩШлЮмФЬЧнѩнЦѩФЬнЬЦлѧРкЖФЦЭмХЬЖШЪнРпЬѡѠѩЧЬЬЭкѩнЦѩЫЬѩШѩкнлРЧЮѧ"));
   public static String const_V5XYQsOYbOwQiaT = qO7tFS83ag(kyjYLCQBND("\u05ceשׯ\u05f8׳\u05faש\u05f5\u05ceשׯײ׳\u05fa"));
}
