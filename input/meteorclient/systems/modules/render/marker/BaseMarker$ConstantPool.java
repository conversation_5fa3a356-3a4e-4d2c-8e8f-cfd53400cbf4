package meteordevelopment.meteorclient.systems.modules.render.marker;

public final class BaseMarker$ConstantPool {
   public static int const_N19Knv1jybIyw6S = (int)(-6551931347019871855L ^ 6551931346218689826L) ^ (int)(59111609 ^ -876932329);
   public static int const_t0h6wBoIGNLnQdZ = (int)(6882030336257067557L ^ 6882030336622306580L) ^ (int)-1495039063L ^ -1691964416;
   public static String const_EaVglFdiazdZS7N = U5HAoglNJZ(Qdz9Yqto27("ٲٹٮٹطٴٹٶٿطً٬٪ٱٶٿ"));
   public static String const_4T4X9k2W2FEeiGA = BiKgvBIqeX(Qdz9Yqto27("ΨΠλΠ"));
   public static int const_v2EWo7GGewhtvOq = (int)(-8084094062724453894L ^ -8084094062468728549L) ^ (int)-1856971872L ^ -2037199658;
   public static String const_Qy6xy2SLsTjtJLY = oGENBu80ne(Qdz9Yqto27("ȆȁȖȏȒȉȔȅȓ"));
   public static String const_nftANew6z3SZdo1 = dI9Xy9ayQi(Qdz9Yqto27("ٙ٩٫٦ٯت٥٬تپٯٲپت٣٬ت٤٥پت٥ټٯٸٸ٣ٮٮٯ٤ت٨ٳتپ٢ٯتٯ٦ٯ٧ٯ٤پؤ"));
   public static String const_rmZgJ70zhCUq9Av = gcUCyCDe1c(Qdz9Yqto27("ΉίδθήϽΩβϽήΤγξϽμΩΩμξζϽιθαμΤϽΪδΩεϽΩεθϽήθίΫθίϺήϽΉ\u038dΎϳ"));
   public static String const_vKUtCHU9TW4pDhw = l7m74FuIif(Qdz9Yqto27("\u0085£¤¤¹£¸²¥ö¯¹£ö¿¸ö´º¹µ½¥ö¢¹ö¦¤³ ³¸¢ö»·¥¥¿ ³öµ¤¯¥¢·ºö²·»·±³ø"));
   public static String const_YMugIlQpMII6i2q = IJ7Q2hQDtv(Qdz9Yqto27("͍̞͚͇͒͑͛͛͒͟͝"));
   public static int const_2a17UljgEMDq7If = (int)((int)-1181754368L ^ -364034285 ^ ((int)-1384986269L ^ -21998378));
   public static String const_TTpVvWNgvoJ12dy = 7q1GVriQvz(Qdz9Yqto27("Üûã´ùá÷ü´âñæàý÷õø´ÿúû÷ÿöõ÷ÿ´íûá´ãýøø´àõÿñº"));
   public static double const_gJW8YfH7v9D4VUn = Double.longBitsToDouble(
      -948315537818761194L ^ -5525247326352176834L ^ 5111097123292924235L ^ -5144576291794909085L
   );
   public static String const_Go2D5i4BWUd09We = jjGSsqLB5a(Qdz9Yqto27("̓ͼͽͷͼ̴Ͷ\u0378ͻͷͿ̴ͧ͠ͻ̴ͧͼͻ̴̹ͣͬͦ͵ͭͱͰ̺"));
   public static int const_erSWYstL12NZ12v = (int)((int)(1653181990 ^ 1006720931) ^ ((int)-2115005940L ^ -546879106));
}
