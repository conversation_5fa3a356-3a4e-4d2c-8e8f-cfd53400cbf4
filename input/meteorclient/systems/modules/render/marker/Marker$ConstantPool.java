package meteordevelopment.meteorclient.systems.modules.render.marker;

public final class Marker$ConstantPool {
   public static String const_bLQl0y4eFBT8Rka = YtsqHYUJIn(h5ELY7iJ6I("ئؤضرتطؠ"));
   public static int const_6ppwhe6vUFGDrsy = (int)(-5797620607511871143L ^ 5797620606664627599L) ^ (int)-1635040350L ^ 1458884635;
   public static String const_fN32j8RJVNadFBd = unURZoilNX(h5ELY7iJ6I(""));
   public static int const_zTEOQWj3QsalJhd = (int)((int)-473141406L ^ -988234366 ^ (int)(714430024 ^ 205620871));
   public static double const_eeioY13dWdBaCDO = Double.longBitsToDouble(
      3357068125520320250L ^ 7286640598224494144L ^ -2312153044876913101L ^ -3143372186032103799L
   );
   public static String const_e0IDVCsZIZGpDkL = B2YuqA4YO9(h5ELY7iJ6I("ˬː˝ʘˌˑ˛˓ʘ˜˝˔˙ˁʘ˚˝ˌˏ˝˝˖ʘˈ˔˙˛ˑ˖˟ʘ˙˖˛ː˗ˊˋʖ"));
   public static String const_NTDl648cB8D0J2A = kgBc7ZSoNs(h5ELY7iJ6I("\u05fbז\u05cdךל\u05cb\u05f9ד׆"));
   public static String const_amjbpY9ySBT7egW = e4WWaaM5ie(h5ELY7iJ6I("ʊʌʋʋʖʌʗʝʺʕʖʊʜ˔ʛʀ˔ʝʋʜʊʊ˔ʜʕʀʍʋʘ"));
   public static String const_EwQtIwG4cKnCKYD = 9CFl19rV2G(h5ELY7iJ6I("ЎбЩдѤжХЪРЫЩШнѪ"));
   public static String const_TLeqgqLJ4ehWtMy = kUsp8TQ2j5(h5ELY7iJ6I("ϝϺϢεϽϼϲϽε϶ϧϬϦϡϴϹϦεϷϺϠϻ϶ϰλ"));
   public static String const_nqJ1bAfrTerO61y = vrJHShcP0x(h5ELY7iJ6I("DBQSUBs_\\_B"));
   public static String const_7Gel8n1qhnTbl16 = dMW2orqINQ(h5ELY7iJ6I("hr\u007f~6xtwti"));
   public static String const_T7dOPph1GjTTxoD = shHQB81FhT(h5ELY7iJ6I("ɤɢ"));
}
