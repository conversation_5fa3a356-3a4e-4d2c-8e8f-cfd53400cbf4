package meteordevelopment.meteorclient.systems.modules.render.marker;

import io.netty.handler.codec.socksx.v5.Socks5AddressDecoder$1$ConstantPool;
import java.util.ArrayList;
import javassist.CtNewWrappedMethod$ConstantPool;
import javassist.expr.Handler$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.GuiTheme;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.widgets.WKeybind$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WLabel;
import meteordevelopment.meteorclient.gui.widgets.WWidget;
import meteordevelopment.meteorclient.gui.widgets.containers.WHorizontalList;
import meteordevelopment.meteorclient.gui.widgets.containers.WVerticalList;
import meteordevelopment.meteorclient.gui.widgets.input.WDropdown;
import meteordevelopment.meteorclient.gui.widgets.pressable.WButton;
import meteordevelopment.meteorclient.gui.widgets.pressable.WCheckbox;
import meteordevelopment.meteorclient.gui.widgets.pressable.WMinus;
import meteordevelopment.meteorclient.systems.hud.elements.ItemHud$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.AutoAnvil$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$16$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.NoLambdaFactoryException$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Block$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Conditional$ConstantPool;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.nbt.NbtElement;
import net.minecraft.nbt.NbtList;

public class Marker extends Module {
   private final MarkerFactory factory = new MarkerFactory();
   private final ArrayList<BaseMarker> markers = new ArrayList<>();

   public Marker() {
      super(
         Categories.Render,
         Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(Socks5AddressDecoder$1$ConstantPool.const_YulRnheOF6xqW1q))),
         new StringBuilder(Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(WKeybind$ConstantPool.const_leqsoGS5o1YPVoi)))),
         Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(NoLambdaFactoryException$ConstantPool.const_QWPldd7G3xdqEil)))
      );
   }

   @EventHandler
   private void onTick(TickEvent.Post event) {
      for (BaseMarker marker : this.markers) {
         if (marker.isVisible()) {
            marker.tick();
         }
      }
   }

   @EventHandler
   private void onRender(Render3DEvent event) {
      for (BaseMarker marker : this.markers) {
         if (marker.isVisible()) {
            marker.render(event);
         }
      }
   }

   @Override
   public NbtCompound toTag() {
      NbtCompound tag = super.toTag();
      NbtList list = new NbtList();

      for (BaseMarker marker : this.markers) {
         NbtCompound mTag = new NbtCompound();
         mTag.method_10582(Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(Expr$Block$ConstantPool.const_iyiiDq8w0jOs6BC))), marker.getTypeName());
         mTag.method_10566(Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(Expr$Conditional$ConstantPool.const_QjQ1qJ27gwqmK3Q))), marker.toTag());
         list.add(mTag);
      }

      tag.method_10566(Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(CtNewWrappedMethod$ConstantPool.const_wu7wT6bKpjAoBFg))), list);
      return tag;
   }

   @Override
   public Module fromTag(NbtCompound tag) {
      super.fromTag(tag);
      this.markers.clear();

      for (NbtElement tagII : tag.method_10554(Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(HighwayBuilder$State$16$ConstantPool.const_CqjByaAC9jcIoyQ))), 10)) {
         NbtCompound tagI = (NbtCompound)tagII;
         String type = tagI.method_10558(Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(AutoAnvil$ConstantPool.const_wGjYagQwZ2nCD7b))));
         BaseMarker marker = this.factory.createMarker(type);
         if (marker != null) {
            NbtCompound markerTag = (NbtCompound)tagI.method_10580(Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(Handler$ConstantPool.const_t8JX1Q5iGenpJi6))));
            if (markerTag != null) {
               marker.fromTag(markerTag);
            }

            this.markers.add(marker);
         }
      }

      return this;
   }

   @Override
   public WWidget getWidget(GuiTheme theme) {
      WVerticalList list = theme.verticalList();
      this.fillList(theme, list);
      return list;
   }

   protected void fillList(GuiTheme theme, WVerticalList list) {
      for (BaseMarker marker : this.markers) {
         WHorizontalList hList = list.add(theme.horizontalList()).expandX().widget();
         WLabel label = hList.add(theme.label(marker.name.get())).widget();
         label.tooltip = marker.description.get();
         hList.add(theme.label(" - " + marker.getDimension().toString())).expandX().widget().color = theme.textSecondaryColor();
         WCheckbox checkbox = hList.add(theme.checkbox(marker.isActive())).widget();
         checkbox.action = () -> {
            if (marker.isActive() != checkbox.checked) {
               marker.toggle();
            }
         };
         WButton edit = hList.add(theme.button(GuiRenderer.EDIT)).widget();
         edit.action = () -> mc.method_1507(marker.getScreen(theme));
         WMinus remove = hList.add(theme.minus()).widget();
         remove.action = () -> {
            this.markers.remove(marker);
            marker.settings.unregisterColorSettings();
            list.clear();
            this.fillList(theme, list);
         };
      }

      WHorizontalList bottom = list.add(theme.horizontalList()).expandX().widget();
      WDropdown<String> newMarker = bottom.add(theme.dropdown(this.factory.getNames(), this.factory.getNames()[0])).widget();
      WButton add = bottom.add(theme.button(Kley7jk4ll(41buDe2Jy8(yyOgIgn7oD(ItemHud$ConstantPool.const_KA7gxFtbedLn24W))))).expandX().widget();
      add.action = () -> {
         String name = newMarker.get();
         this.markers.add(this.factory.createMarker(name));
         list.clear();
         this.fillList(theme, list);
      };
   }
}
