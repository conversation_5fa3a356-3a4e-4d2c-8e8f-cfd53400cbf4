package meteordevelopment.meteorclient.systems.modules.render.marker;

public final class Sphere2dMarker$ConstantPool {
   public static String const_WVoeRoSGlpWiV6i = e1JEkTCzj9(QdiC4GCIJc("ԵԢԶԲԢԴԳ"));
   public static String const_FRn6oM7KjmXIaAh = QIIyw8nZLg(QdiC4GCIJc("͇͚͈͇ͪ͆͝͝Ϳ͈͌͜ͅ"));
   public static int const_2MhJ8tGLFwWdxq4 = (int)(-5033500223872713946L ^ 5033500222512499622L) ^ (int)(-1752230604 ^ 1057859067);
   public static int const_GYF3b8Wfgrsq3up = (int)(7963310888254108064L ^ 7963310887460078602L) ^ (int)(1420631150 ^ 1707142093);
   public static double const_GTgKjU3prBDbpmd = Double.longBitsToDouble(
      7012044800226616774L ^ -7591818326633906161L ^ -7396253925584785002L ^ 3364332757078529119L
   );
   public static double const_NLMlQCT42rtBTf7 = Double.longBitsToDouble(
      -2319380169161993695L ^ -8639749858171732555L ^ -5199416067903367734L ^ -6906991430131254690L
   );
   public static String const_IdGlNnFlggsNQFq = EYxmA4N4yL(QdiC4GCIJc("֢֞֯ת֤֧֫֯ת֥֬ת־֢֯תָֺ֥֣֦֬֯פ"));
   public static double const_i626ZMBoxoYCtoo = Double.longBitsToDouble(
      -5522290427296988238L ^ 6853422887668623073L ^ 3850910369912354864L ^ -1812581344131250951L
   );
}
