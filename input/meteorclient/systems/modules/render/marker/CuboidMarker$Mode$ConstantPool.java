package meteordevelopment.meteorclient.systems.modules.render.marker;

public final class CuboidMarker$Mode$ConstantPool {
   public static String const_AVTTddMnEJL9Az9 = vON6VAJvF4(6txwFwBQIo("ϲϱϴΰϤϩϠϵΰ϶ϿϢΰϱϢϢϱϩΰϣϹϪϵ"));
   public static String const_aISd5uoLoyevJHt = jdNFcBFtjw(6txwFwBQIo("̗̞̔̎̏̒̕"));
   public static double const_Lbi4Hna8YBQO5Lj = Double.longBitsToDouble(
      -5644289837493957319L ^ -8280402464064302592L ^ 7274731833624649604L ^ 1749762355712131261L
   );
   public static String const_g7oLKh4t5TAypQf = LNo1ZGBb2W(6txwFwBQIo("ڕڲڪ۽کڵڸ۽ڮڵڼڭڸڮ۽ڼگڸ۽گڸڳڹڸگڸڹ۳"));
   public static String const_LmFwAQ3GvtPEjdA = nVO8N1lwhV(6txwFwBQIo("ЙЗКИАЗВЈЏ"));
   public static String const_4uDEZCaVY8Gi94y = ObjBcOwAIw(6txwFwBQIo("ȸȖȟɛɐɛ"));
}
