package meteordevelopment.meteorclient.systems.modules.render.marker;

import io.netty.handler.codec.socksx.v5.DefaultSocks5InitialRequest$ConstantPool;
import javassist.CtArray$ConstantPool;
import meteordevelopment.meteorclient.events.entity.EntityRemovedEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.gui.tabs.builtin.MacrosTab$ConstantPool;
import meteordevelopment.meteorclient.renderer.Mesh$Attrib$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.BlockPosSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.friends.Friend$APIResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.Criticals$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.DirectFly$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.FlyOnFirstTime$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.TridentExp$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.FastClimb$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoReplenish$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WaypointsModule$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WaypointsModule$EditWaypointScreen$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.Pool$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import net.minecraft.util.math.BlockPos;

public class CuboidMarker extends BaseMarker {
   public static final String type = "Cuboid";
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgRender = this.settings.createGroup(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(WaypointsModule$ConstantPool.const_RL2dANGrZaSdlol))));
   private final Setting<BlockPos> pos1 = this.sgGeneral
      .add(
         new BlockPosSetting.Builder()
            .name(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(FlyOnFirstTime$ConstantPool.const_NCDCb0av8c7bVlm))))
            .description(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(FastClimb$ConstantPool.const_qFerFywugdTJ41l))))
            .build()
      );
   private final Setting<BlockPos> pos2 = this.sgGeneral
      .add(
         new BlockPosSetting.Builder()
            .name(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(DefaultSocks5InitialRequest$ConstantPool.const_OGdIn7qIn9lJbFa))))
            .description(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(Pool$ConstantPool.const_kpjwSDpW5Vj4AgT))))
            .build()
      );
   private final Setting<CuboidMarker.Mode> mode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(Mesh$Attrib$ConstantPool.const_qELfiSVGNvGNrJP)))))
                  .description(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(Criticals$ConstantPool.const_aeVI97r6dHcQIvI)))))
               .defaultValue(CuboidMarker.Mode.Full))
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(AutoReplenish$ConstantPool.const_6NWowQtVQcoUKjv)))))
                  .description(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(DirectFly$ConstantPool.const_vHVVIYYdxGo8Y47)))))
               .defaultValue(ShapeMode.Both))
            .build()
      );
   private final Setting<SettingColor> sideColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(WaypointsModule$EditWaypointScreen$ConstantPool.const_WiwReJMA4nQaTAa))))
            .description(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(MacrosTab$ConstantPool.const_4OTxzjmSxNDEtdq))))
            .defaultValue(new SettingColor(0, 100, 255, 50))
            .build()
      );
   private final Setting<SettingColor> lineColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(TridentExp$ConstantPool.const_qBNTLaNrgemqrAY))))
            .description(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(CtArray$ConstantPool.const_bypjeddGR7MLubt))))
            .defaultValue(new SettingColor(0, 100, 255, 255))
            .build()
      );

   public CuboidMarker() {
      super(bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(EntityRemovedEvent$ConstantPool.const_PEONpfRPFiaCRrG))));
   }

   @Override
   public String getTypeName() {
      return bQsbGlvOY9(JpkmtS3whT(BxnyYyveqs(Friend$APIResponse$ConstantPool.const_WwwPJeriS5K5n96)));
   }

   @Override
   protected void render(Render3DEvent event) {
      int minX = Math.min(this.pos1.get().method_10263(), this.pos2.get().method_10263());
      int minY = Math.min(this.pos1.get().method_10264(), this.pos2.get().method_10264());
      int minZ = Math.min(this.pos1.get().method_10260(), this.pos2.get().method_10260());
      int maxX = Math.max(this.pos1.get().method_10263(), this.pos2.get().method_10263());
      int maxY = Math.max(this.pos1.get().method_10264(), this.pos2.get().method_10264());
      int maxZ = Math.max(this.pos1.get().method_10260(), this.pos2.get().method_10260());
      event.renderer.box(minX, minY, minZ, maxX + 1, maxY + 1, maxZ + 1, this.sideColor.get(), this.lineColor.get(), this.shapeMode.get(), 0);
   }

   public static enum Mode {
      Full;
   }
}
