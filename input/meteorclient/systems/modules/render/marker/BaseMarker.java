package meteordevelopment.meteorclient.systems.modules.render.marker;

import javassist.bytecode.SignatureAttribute$ClassType$ConstantPool;
import meteordevelopment.discordipc.RichPresence$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.gui.GuiTheme;
import meteordevelopment.meteorclient.gui.screens.MarkerScreen;
import meteordevelopment.meteorclient.gui.screens.settings.EntityTypeListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.StorageBlockListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WWidget;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.Settings;
import meteordevelopment.meteorclient.settings.StringSetting;
import meteordevelopment.meteorclient.systems.accounts.AllMod$1$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoArmor$Protection$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AotuPlaceStep$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WaypointsModule$WIcon$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$RestockTask$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.ISerializable;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.world.Dimension;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.nbt.NbtCompound;
import org.reflections.util.QueryFunction$ConstantPool;
import org.reflections.vfs.ZipDir$ConstantPool;

public abstract class BaseMarker implements ISerializable<BaseMarker> {
   public final Settings settings = new Settings();
   protected final SettingGroup sgBase = this.settings.createGroup(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(AllMod$1$1$ConstantPool.const_IFjJSdSZe96lA1T))));
   public final Setting<String> name = this.sgBase
      .add(
         new StringSetting.Builder()
            .name(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(EntityTypeListSettingScreen$ConstantPool.const_G0RtdGO9JdDRbSB))))
            .description(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(QueryFunction$ConstantPool.const_3IX2gOiPJC5JnHe))))
            .build()
      );
   protected final Setting<String> description = this.sgBase
      .add(
         new StringSetting.Builder()
            .name(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(AutoArmor$Protection$ConstantPool.const_4OfGtuxK9SG7TI4))))
            .description(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(AotuPlaceStep$ConstantPool.const_JhFOtoINVq4ZMOD))))
            .build()
      );
   private final Setting<Dimension> dimension = this.sgBase
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(HighwayBuilder$RestockTask$ConstantPool.const_qBsX4cFdwOAcw1G)))))
                  .description(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(SignatureAttribute$ClassType$ConstantPool.const_yF2bkkTQkDrTU2h)))))
               .defaultValue(Dimension.Overworld))
            .build()
      );
   private final Setting<Boolean> active = this.sgBase
      .add(
         new BoolSetting.Builder()
            .name(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(RichPresence$ConstantPool.const_nLjgJBnoatSQ2IC))))
            .description(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(StorageBlockListSettingScreen$ConstantPool.const_T4dDBCR9FiWDhac))))
            .defaultValue(false)
            .build()
      );

   public BaseMarker(String name) {
      this.name.set(name);
      this.dimension.set(PlayerUtils.getDimension());
   }

   protected void render(Render3DEvent event) {
   }

   protected void tick() {
   }

   public Screen getScreen(GuiTheme theme) {
      return new MarkerScreen(theme, this);
   }

   public WWidget getWidget(GuiTheme theme) {
      return null;
   }

   public String getName() {
      return this.name.get();
   }

   public String getTypeName() {
      return null;
   }

   public boolean isActive() {
      return this.active.get();
   }

   public boolean isVisible() {
      return this.isActive() && PlayerUtils.getDimension() == this.dimension.get();
   }

   public Dimension getDimension() {
      return this.dimension.get();
   }

   public void toggle() {
      this.active.set(!this.active.get());
   }

   @Override
   public NbtCompound toTag() {
      NbtCompound tag = new NbtCompound();
      tag.method_10566(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(ZipDir$ConstantPool.const_SXQHWGbA2MyS4JO))), this.settings.toTag());
      return tag;
   }

   public BaseMarker fromTag(NbtCompound tag) {
      NbtCompound settingsTag = (NbtCompound)tag.method_10580(RGKaw6JrQb(alts7uS2K1(n4QXyj24TQ(WaypointsModule$WIcon$ConstantPool.const_X3IkrTE8ea2tx6G))));
      if (settingsTag != null) {
         this.settings.fromTag(settingsTag);
      }

      return this;
   }
}
