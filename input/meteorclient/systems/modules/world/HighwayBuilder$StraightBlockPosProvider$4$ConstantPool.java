package meteordevelopment.meteorclient.systems.modules.world;

public final class HighwayBuilder$StraightBlockPosProvider$4$ConstantPool {
   public static int const_24MJesiAVYYIfce = (int)(-8894491168229325244L ^ -8894491168291041192L) ^ (int)-1212907911L ^ -**********;
   public static String const_ThqOLBqGwVLnlU6 = PeDVrDVTSq(o6rFiqDLCa("ТЎДЍЅсЏЎЕсЇЈЏЅсЂЍЀВВсЈЏсЅЄВЂГЈБЕЎГскБЎВсќс"));
   public static String const_Dg3khxoYEqdlo8I = qd9eHw4rNN(o6rFiqDLCa("ˏ˝"));
   public static String const_No8yeSiEYG7roBQ = Wz4Hr5W1WJ(o6rFiqDLCa("ְֲפְַ\u05feפַ\u05f9\u05f8ףַ\u05f6ַס\u05f6\u05fb\u05fe׳ַק\u05f6ף\u05ffֹ"));
   public static double const_Vn98u4Brbn8tJ99 = Double.longBitsToDouble(
      -7521093327657618920L ^ -6755141403743265282L ^ -5484003937270843598L ^ -4157604621011789612L
   );
   public static int const_nkNgrFazR4arSix = (int)(178956862075027003L ^ 178956860446721864L) ^ (int)951690448L ^ **********;
   public static int const_VU6egQlbauoDKdp = (int)(-189232023481899486L ^ 189232023679112230L) ^ (int)-1037364740L ^ 831857649;
   public static String const_P4QJqbNHuThhUeP = rKDoODKgKe(o6rFiqDLCa("פנ\u05fe\u05f9װ"));
   public static float const_tDYrLn3beeSvdAk = Float.intBitsToFloat((int)(552243491 ^ -91028961) ^ (int)(920724948 ^ -1347635480));
   public static String const_vnl4IYiLbKlNwR8 = czIFoj4njY(o6rFiqDLCa("ΐΑϒΕΊΒΏ"));
   public static double const_Dwz0KeAWs9f5ZQM = Double.longBitsToDouble(
      2227283793496209923L ^ 8335092569526371698L ^ 2613909872874692988L ^ 653212603767086605L
   );
   public static String const_b66WXYih4ANGUH6 = SPnlsBTlwk(o6rFiqDLCa("Ê\u0083\u0084Ê"));
   public static String const_fjuTQA2DfPSeSVa = neI9Bbf6QD(o6rFiqDLCa("̛̛̛̯̘̖̟͚͚̞̟̜͚̟͚̟̖͚͚̜̖̟͚͚͚̔̎̓̔̎̓̃̎̒̈̀̓̊̓̓̔̊̎̒̕͝"));
}
