package meteordevelopment.meteorclient.systems.modules.world;

public final class VeinMiner$ListMode$ConstantPool {
   public static String const_7ZT7BAKafEdjTvn = URXQ54GdgL(Mg1O9DdRIt("ܢܮܬܬܠܯܥܾܴܯܴܱܱܲܮܳܵܤܥ"));
   public static String const_TtPdtBJ9pBzeygM = 4iGxOOLzX9(Mg1O9DdRIt("դկոկԡբկՠթԡՁլդիխպ"));
   public static String const_JoO9takB5RAtoBj = O8RIBFiNiI(
      Mg1O9DdRIt(
         "ڲڏۛڟڔڞڈڕۜڏۛښژڏڎښڗڗڂۛڏښڐڞۛڏڌڞڕڏڂۛڏڒژڐڈۛڏڔۛڋڗښژڞۛښۛڙڗڔژڐۗۛڏړڒڈۛژڔڈڏۛڒڈۛڈڔۛړڒڜړۛڙڞژښڎڈڞۛڌڞۛڌښڕڏۛڏڔۛڜڞڕڞډښڗڗڂۛژڔڕڈڞډڍڞۛڙڗڔژڐڈۛڌړڒژړۛږڒڜړڏۛڙڞۛڗڒږڒڏڞڟە"
      )
   );
   public static String const_r8Di7DaykOZykR4 = aJ6q9bkMT5(Mg1O9DdRIt("ɟɰȶɷȶɺɷɯɳɤȶɿɥȶɣɸɷɴɺɳȶɢɹȶɴɳȶɵɹɸɥɢɤɣɵɢɳɲȺȶɼɣɥɢȶɥɽɿɦȶɿɢȸ"));
   public static String const_hulSqe41WktLYUa = WwE9a9Kfwu(Mg1O9DdRIt("ņŀŌńžŌňŏńłœŀŇŕ"));
   public static String const_GCIHZMIWNhDB3Fj = vJVQtYy9NQ(Mg1O9DdRIt("ЇибФѰгпмпТѰФпѰХУеѰжпТѰежжегФУѾ"));
   public static String const_JXNdczzUUWvDSyK = Y4avf00jId(Mg1O9DdRIt("ΈΊΝΞΝΊΊΝΜϕΈΊΗΌΝΛΌΑΗΖ"));
   public static String const_fYSRBEfKLlmeuBi = 4eFANUyYZu(Mg1O9DdRIt("\u0015\u0012\u0011\u0018P\u001b\u0014\u0011\u0011\u0018\u000f"));
   public static String const_ocsP1NL49Q5UkMp = diGy4oMbY1(Mg1O9DdRIt("Δ\u0383Έ\u0382\u0383Δ"));
   public static String const_8F9DA8FdMWTBcIj = kUFBjHghSB(Mg1O9DdRIt("ŖŻŻŸŠŤķŮŸŢķţŸķŤŹŲŶżķŠſžŻŲķžŹķŐłŞŤĹ"));
   public static String const_YjUneGuXtrNBHcX = ONvX14wo7n(Mg1O9DdRIt("ق٥ٽتپ٢ٯتٹ٢٫ٺٯٹت٫ٸٯتٸٯ٤ٮٯٸٯٮؤ"));
   public static String const_AxaR9b2tvdnQA4s = F83jU7F6Tr(Mg1O9DdRIt("ʜʆʇʟʚʝʖ"));
   public static String const_WN1hFW3cKjv5GXf = 8cXJLoTork(Mg1O9DdRIt("ȑȦȵȮȦȥȫȢȀȢȳ"));
}
