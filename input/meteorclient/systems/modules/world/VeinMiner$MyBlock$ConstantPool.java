package meteordevelopment.meteorclient.systems.modules.world;

public final class VeinMiner$MyBlock$ConstantPool {
   public static String const_klQD7S17y5JyPTb = gnqi9oeFQg(3eUS1xIlcv("۞ۍۍ۞ۆۭۚ۞ۛۼۗ۞ۍ"));
   public static String const_xcNDQJw92qgun2t = OrHARcGC4S(3eUS1xIlcv("Ⱥȶ"));
   public static String const_V971872neLa7UOd = iQoQWBLDoY(3eUS1xIlcv("ǽǖǛǐǙǖǌǋƘǌǐǝƘǑǌǝǕƘǑǖƘǁǗǍǊƘǐǙǖǜƖƘǪǽǩǭǱǪǽǫƘǻǊǝǙǌǑǎǝƘǕǗǜǝƖ"));
   public static float const_GbMbG5dFSUc02N7 = Float.intBitsToFloat((int)(1710412181 ^ -462925432) ^ (int)(237861161 ^ 1292106036));
   public static String const_MfNFmdTDzuDHMNN = gl6jl41OQo(3eUS1xIlcv("ݥݒݒݏݒ܀ݗݒ݉ݔ݉ݎ݇܀ݍ݁ݐ܀ݔ݅ݘݔݕݒ݅"));
   public static double const_TBBK0rlO7NYNnMS = Double.longBitsToDouble(
      -693804891387174344L ^ 3333103402849620102L ^ 523001058495025983L ^ 2354399036727943553L
   );
   public static String const_NuxqwgiGoqorltK = rBoLLVkJOQ(3eUS1xIlcv("ѿјрЗујЗфрўуєџЗујЗіЗчўєќіяђЙ"));
   public static int const_Yt2YaXQV8oTTDYr = (int)(-4629553254530052237L ^ 4629553256182534538L) ^ (int)1269389667L ^ -690911604;
   public static int const_7x6NM14noCYixE6 = (int)((int)1953310142L ^ -899921551 ^ (int)(1808932862 ^ -706514082));
   public static int const_FDW1IxtQodbJ3vN = (int)((int)1918830555L ^ -1854589030 ^ (int)(500152096 ^ -18513295));
   public static String const_Woahtw9bpvVfr6n = A8arKjA2xI(3eUS1xIlcv("?+*1s,;-.?)0"));
   public static String const_QqQdCbDv4fyiD49 = WtAIvJFgxh(3eUS1xIlcv("ǖǠǭǪǨǯǦơǬǮǥǤƯ"));
   public static double const_f7qiYpVt96djt4K = Double.longBitsToDouble(
      1485542007813404211L ^ 9162337477599185802L ^ 5809543892414207708L ^ 8864553642813598565L
   );
   public static String const_wVDn4C1FTJRy0Ld = 8d42SMZQNQ(3eUS1xIlcv("̴̵̨̧̧̡̨̧̮̣̥̩̪̩̩̠̲̮̣̫̣̲̫̣̒ͦͦͦͦͦͨ"));
   public static int const_VobYWeSeVJDu7PY = (int)((int)(152803146 ^ 568161278) ^ (int)(286589145 ^ 970188715));
}
