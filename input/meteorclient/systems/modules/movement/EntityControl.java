package meteordevelopment.meteorclient.systems.modules.movement;

import javassist.bytecode.MethodHandleInfo$ConstantPool;
import meteordevelopment.meteorclient.Main$OperatingSystem$1$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.screens.CommitsScreen$ConstantPool;
import meteordevelopment.meteorclient.mixin.ClientPlayerEntityAccessor;
import meteordevelopment.meteorclient.mixininterface.IHorseBaseEntity;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.render.prompts.Prompt$PromptScreen$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.passive.AbstractHorseEntity;

public class EntityControl extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final Setting<Boolean> maxJump = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7L0qvlerR8(J2tB7dBh64(UalQAO1JKw(MethodHandleInfo$ConstantPool.const_AzwjoSn6snq9bPt))))
            .description(7L0qvlerR8(J2tB7dBh64(UalQAO1JKw(CommitsScreen$ConstantPool.const_hDKO2N2PTwQQAlA))))
            .defaultValue(true)
            .build()
      );

   public EntityControl() {
      super(
         Categories.Movement,
         7L0qvlerR8(J2tB7dBh64(UalQAO1JKw(Main$OperatingSystem$1$ConstantPool.const_ZfkHZBS8wDoxbGv))),
         new StringBuilder(7L0qvlerR8(J2tB7dBh64(UalQAO1JKw(Prompt$PromptScreen$ConstantPool.const_sBFWJ9yEj9ywXCq)))),
         7L0qvlerR8(J2tB7dBh64(UalQAO1JKw(AutoJump$Mode$ConstantPool.const_lYptwoojXZ9QVL1)))
      );
   }

   @Override
   public void onDeactivate() {
      if (Utils.canUpdate() && mc.field_1687.method_18112() != null) {
         for (Entity entity : mc.field_1687.method_18112()) {
            if (entity instanceof AbstractHorseEntity) {
               ((IHorseBaseEntity)entity).setSaddled(false);
            }
         }
      }
   }

   @EventHandler
   private void onTick(TickEvent.Pre event) {
      for (Entity entity : mc.field_1687.method_18112()) {
         if (entity instanceof AbstractHorseEntity) {
            ((IHorseBaseEntity)entity).setSaddled(true);
         }
      }

      if (this.maxJump.get()) {
         ((ClientPlayerEntityAccessor)mc.field_1724).setMountJumpStrength(1.0F);
      }
   }
}
