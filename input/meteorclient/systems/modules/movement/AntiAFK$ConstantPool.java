package meteordevelopment.meteorclient.systems.modules.movement;

public final class AntiAFK$ConstantPool {
   public static String const_TCQUvtX4T1Hwi7d = Nn4QI9mUO3(Yl9AnUZn9u("ՠՇՏՊՃՂԆՒՉԆՔՃՋՉՐՃԆՖՔՉ՞՟ԆՃՈՅՉՂՃՔՕԜ"));
   public static int const_0T2GA9ylssybXTW = (int)(5553136015694129476L ^ 5553136015818984813L) ^ (int)(-1575824145 ^ -1432144411);
   public static double const_IAiGMNZoi6I6ryN = Double.longBitsToDouble(
      732682139078246078L ^ -6469135006215482438L ^ 6151057536285410041L ^ -5089363039321624579L
   );
   public static int const_yD1lOFeOS9DjhNw = (int)((int)(-925812460 ^ 190991750) ^ ((int)-480757798L ^ 552288664));
   public static String const_g7gt4aIL7fV141t = NHUABXHwXb(Yl9AnUZn9u("ط"));
   public static String const_go4FZzsVIJhKeNt = SS293POtDm(Yl9AnUZn9u("ϭϱάϣϰϡϪ"));
   public static double const_ClcBM7ZLQGX4hZ0 = Double.longBitsToDouble(
      -1269166107647776902L ^ 7981851695763496966L ^ 8043545146071270522L ^ -5818837343464848634L
   );
   public static String const_x1B99dCLq5qLTG0 = Sq4sXgeyjZ(Yl9AnUZn9u("δΈ΅Ν\u0381Ζ"));
   public static String const_sCYpHTSMqpGfOoy = Ba61dAeQPJ(Yl9AnUZn9u("嚛巈嘰篶姸ĤĲı"));
   public static double const_qvq25OuvnlWqtKd = Double.longBitsToDouble(
      -7706283756600647597L ^ -2312236650335176780L ^ -6325409001933897137L ^ -6721010281658886744L
   );
   public static String const_6aoWS1eOdLFr2Q4 = ewsI7ivFgr(Yl9AnUZn9u("\u001bj"));
}
