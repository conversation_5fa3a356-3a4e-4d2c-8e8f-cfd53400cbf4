package meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes;

import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.gui.screens.settings.ColorSettingScreen$WHueQuad$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFlightMode;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFlightModes;
import meteordevelopment.meteorclient.utils.PreInit$ConstantPool;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket.Mode;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.OnGroundOnly;
import net.minecraft.util.math.Vec3d;

public class Packet extends ElytraFlightMode {
   private final Vec3d vec3d = new Vec3d(0.0, 0.0, 0.0);

   public Packet() {
      super(ElytraFlightModes.Packet);
   }

   @Override
   public void onDeactivate() {
      this.mc.field_1724.method_31549().field_7479 = false;
      this.mc.field_1724.method_31549().field_7478 = false;
   }

   @Override
   public void onTick() {
      super.onTick();
      if (this.mc.field_1724.method_31548().method_7372(2).method_7909() == Items.field_8833
         && !(this.mc.field_1724.field_6017 <= PreInit$ConstantPool.const_6jS4A6HCcOQOeIN)
         && !this.mc.field_1690.field_1832.method_1434()) {
         if (this.mc.field_1690.field_1894.method_1434()) {
            this.vec3d.method_1031(0.0, 0.0, this.elytraFly.horizontalSpeed.get());
            this.vec3d.method_1024(-((float)Math.toRadians(this.mc.field_1724.method_36454())));
         } else if (this.mc.field_1690.field_1881.method_1434()) {
            this.vec3d.method_1031(0.0, 0.0, this.elytraFly.horizontalSpeed.get());
            this.vec3d.method_1024((float)Math.toRadians(this.mc.field_1724.method_36454()));
         }

         if (this.mc.field_1690.field_1903.method_1434()) {
            this.vec3d.method_1031(0.0, this.elytraFly.verticalSpeed.get(), 0.0);
         } else if (!this.mc.field_1690.field_1903.method_1434()) {
            this.vec3d.method_1031(0.0, -this.elytraFly.verticalSpeed.get(), 0.0);
         }

         this.mc.field_1724.method_18799(this.vec3d);
         this.mc.field_1724.field_3944.method_52787(new ClientCommandC2SPacket(this.mc.field_1724, Mode.field_12982));
         this.mc.field_1724.field_3944.method_52787(new OnGroundOnly(true));
      }
   }

   @Override
   public void onPacketSend(PacketEvent.Send event) {
      if (event.packet instanceof PlayerMoveC2SPacket) {
         this.mc.field_1724.field_3944.method_52787(new ClientCommandC2SPacket(this.mc.field_1724, Mode.field_12982));
      }
   }

   @Override
   public void onPlayerMove() {
      this.mc.field_1724.method_31549().field_7479 = true;
      this.mc
         .field_1724
         .method_31549()
         .method_7248(this.elytraFly.horizontalSpeed.get().floatValue() / ColorSettingScreen$WHueQuad$ConstantPool.const_IvsOw85BEDTXGtw);
   }
}
