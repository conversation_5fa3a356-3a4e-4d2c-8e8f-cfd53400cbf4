package meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes;

import io.netty.handler.codec.socks.SocksAuthResponseDecoder$1$ConstantPool;
import javassist.SerialVersionUID$3$ConstantPool;
import javassist.bytecode.stackmap.MapMaker$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFlightMode;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFlightModes;

public class Pitch40 extends ElytraFlightMode {
   private boolean pitchingDown = true;
   private int pitch;

   public Pitch40() {
      super(ElytraFlightModes.Pitch40);
   }

   @Override
   public void onActivate() {
      if (this.mc.field_1724.method_23318() < this.elytraFly.pitch40upperBounds.get()) {
         this.elytraFly.error(PE0e8GS9sv(a27L9bI2I5(SiEbNQSkbp(SerialVersionUID$3$ConstantPool.const_YDzL5JDBinatwvt))), new Object[0]);
         this.elytraFly.toggle();
      }

      this.pitch = 40;
   }

   @Override
   public void onDeactivate() {
   }

   @Override
   public void onTick() {
      super.onTick();
      if (this.pitchingDown && this.mc.field_1724.method_23318() <= this.elytraFly.pitch40lowerBounds.get()) {
         this.pitchingDown = false;
      } else if (!this.pitchingDown && this.mc.field_1724.method_23318() >= this.elytraFly.pitch40upperBounds.get()) {
         this.pitchingDown = true;
      }

      if (!this.pitchingDown && this.mc.field_1724.method_36455() > SocksAuthResponseDecoder$1$ConstantPool.const_ckgbJTHfO1NSV1v) {
         this.pitch = (int)(this.pitch - this.elytraFly.pitch40rotationSpeed.get());
         if (this.pitch < -40) {
            this.pitch = -40;
         }
      } else if (this.pitchingDown && this.mc.field_1724.method_36455() < MapMaker$ConstantPool.const_vMxJFai2c9abNra) {
         this.pitch = (int)(this.pitch + this.elytraFly.pitch40rotationSpeed.get());
         if (this.pitch > 40) {
            this.pitch = 40;
         }
      }

      this.mc.field_1724.method_36457(this.pitch);
   }

   @Override
   public void autoTakeoff() {
   }

   @Override
   public void handleHorizontalSpeed(PlayerMoveEvent event) {
      this.velX = event.movement.field_1352;
      this.velZ = event.movement.field_1350;
   }

   @Override
   public void handleVerticalSpeed(PlayerMoveEvent event) {
   }

   @Override
   public void handleFallMultiplier() {
   }

   @Override
   public void handleAutopilot() {
   }
}
