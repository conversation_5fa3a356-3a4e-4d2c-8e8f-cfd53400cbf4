package meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes;

public final class Packet$ConstantPool {
   public static String const_Nj24ElnGFnBdF4F = uDktrhSQ2I(OTgutG4Y22("ϬϯϪήϪϫϭϢϯϼϧϠϩήϭϢϯϽϽ"));
   public static String const_BBGJFHFEtRGRy16 = caJYq7eM1J(OTgutG4Y22("حؤضح؆تءؠ"));
   public static String const_DrR293IlwaQzKLU = QMvyC2op7D(OTgutG4Y22("̧̡̡̬̻̬̬̣̪̬̎̾̾͢͢"));
   public static String const_qlr4rFqQWoQLloB = 4jqxwNZzYn(OTgutG4Y22("PYL"));
   public static int const_af2rGHnML2CryBS = (int)(8723913780796469148L ^ 8723913779702737655L) ^ (int)(-183530241 ^ -1260614246);
   public static int const_3NYdjDofwwhen2Y = (int)((int)(-532989645 ^ 818686368) ^ (int)(793688139 ^ -4608900));
   public static String const_BgwIwYqABjwonrD = LyRk6YIeo3(OTgutG4Y22("ؼغجػ٧ءئؤج"));
}
