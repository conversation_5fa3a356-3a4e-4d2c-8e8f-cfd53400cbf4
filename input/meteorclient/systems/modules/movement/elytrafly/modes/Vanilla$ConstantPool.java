package meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes;

public final class Vanilla$ConstantPool {
   public static int const_I5FFVyrSOxQMy18 = (int)(-3223749020096596437L ^ 3223749019987266720L) ^ (int)(578810510 ^ -1023029734);
   public static int const_roSoSirb04ctlPr = (int)(-5471748651326354736L ^ -5471748650464618386L) ^ (int)572144206L ^ 2008493634;
   public static String const_criOpByiQ9t28F9 = 4JYwLSGof6(gbjD4DiZIg("ïã"));
   public static String const_DyyjT4a2Ptv14Ny = ie0v69teJK(gbjD4DiZIg("˕˛˔ʑ"));
   public static String const_YwkY6Ju6T1d2D2z = wByiN6Jdes(gbjD4DiZIg("έΨΤΨήά"));
   public static int const_a5JnyneMGHsxLBr = (int)(3012437178275034639L ^ -3012437178510380823L) ^ (int)-2140842817L ^ 1838371017;
   public static double const_nNYBnvbVVw0GfjS = Double.longBitsToDouble(
      1054988427390272571L ^ -2170526207098409421L ^ 6948139574680719366L ^ -5705937952410273266L
   );
   public static int const_Llr1IP9ptCbpdOJ = (int)(1793969112796973918L ^ -1793969112842981206L) ^ (int)-795718874L ^ 673429346;
   public static String const_gq8tFe1DacxOg7g = MIUpONywxY(gbjD4DiZIg("±¾»»ú¸§¶´¾£®"));
   public static int const_trcdGiuRyiwGMus = (int)(-8517725733261842739L ^ 8517725731619757684L) ^ (int)(331876338 ^ -1843971636);
   public static String const_kTPTBWD4WJsKzeB = s024E2mYSq(gbjD4DiZIg("ʁ"));
   public static String const_fDLA5oNT6dJeJL9 = 2NgNaxRb2e(gbjD4DiZIg("ҘұҢҽҹҾҷ"));
}
