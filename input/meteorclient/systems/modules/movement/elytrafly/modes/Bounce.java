package meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes;

import meteordevelopment.meteorclient.commands.arguments.SettingValueArgumentType$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFlightMode;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFlightModes;
import meteordevelopment.meteorclient.systems.modules.player.Rotation;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$DiagonalBlockPosProvider$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.input.Input;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.item.ElytraItem;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket.Mode;
import net.minecraft.network.packet.s2c.play.PlayerPositionLookS2CPacket;

public class Bounce extends ElytraFlightMode {
   boolean rubberbanded = false;
   int tickDelay = this.elytraFly.restartDelay.get();
   double prevFov;

   public Bounce() {
      super(ElytraFlightModes.Bounce);
   }

   @Override
   public void onTick() {
      super.onTick();
      if (this.mc.field_1690.field_1903.method_1434() && !this.mc.field_1724.method_6128()) {
         this.mc.method_1562().method_52787(new ClientCommandC2SPacket(this.mc.field_1724, Mode.field_12982));
      }

      if (checkConditions(this.mc.field_1724)) {
         if (!this.rubberbanded) {
            if (this.prevFov != 0.0 && !this.elytraFly.sprint.get()) {
               this.mc.field_1690.method_42454().method_41748(0.0);
            }

            if (this.elytraFly.autoJump.get()) {
               this.setPressed(this.mc.field_1690.field_1903, true);
            }

            this.setPressed(this.mc.field_1690.field_1894, true);
            this.mc.field_1724.method_36456(this.getYawDirection());
            this.mc.field_1724.method_36457(this.elytraFly.pitch.get().floatValue());
         }

         if (!this.elytraFly.sprint.get()) {
            if (this.mc.field_1724.method_6128()) {
               this.mc.field_1724.method_5728(this.mc.field_1724.method_24828());
            } else {
               this.mc.field_1724.method_5728(true);
            }
         }

         if (this.rubberbanded && this.elytraFly.restart.get()) {
            if (this.tickDelay > 0) {
               this.tickDelay--;
            } else {
               this.mc.method_1562().method_52787(new ClientCommandC2SPacket(this.mc.field_1724, Mode.field_12982));
               this.rubberbanded = false;
               this.tickDelay = this.elytraFly.restartDelay.get();
            }
         }
      }
   }

   @Override
   public void onPreTick() {
      super.onPreTick();
      if (checkConditions(this.mc.field_1724) && this.elytraFly.sprint.get()) {
         this.mc.field_1724.method_5728(true);
      }
   }

   private void unpress() {
      this.setPressed(this.mc.field_1690.field_1894, false);
      if (this.elytraFly.autoJump.get()) {
         this.setPressed(this.mc.field_1690.field_1903, false);
      }
   }

   @Override
   public void onPacketReceive(PacketEvent.Receive event) {
      if (event.packet instanceof PlayerPositionLookS2CPacket) {
         this.rubberbanded = true;
         this.mc.field_1724.method_23670();
      }
   }

   @Override
   public void onPacketSend(PacketEvent.Send event) {
      if (event.packet instanceof ClientCommandC2SPacket
         && ((ClientCommandC2SPacket)event.packet).method_12365().equals(Mode.field_12982)
         && !this.elytraFly.sprint.get()) {
         this.mc.field_1724.method_5728(true);
      }
   }

   private void setPressed(KeyBinding key, boolean pressed) {
      key.method_23481(pressed);
      Input.setKeyState(key, pressed);
   }

   public static boolean recastElytra(ClientPlayerEntity player) {
      if (checkConditions(player) && ignoreGround(player)) {
         player.field_3944.method_52787(new ClientCommandC2SPacket(player, Mode.field_12982));
         return true;
      } else {
         return false;
      }
   }

   public static boolean checkConditions(ClientPlayerEntity player) {
      ItemStack itemStack = player.method_6118(EquipmentSlot.field_6174);
      return !player.method_31549().field_7479
         && !player.method_5765()
         && !player.method_6101()
         && itemStack.method_31574(Items.field_8833)
         && ElytraItem.method_7804(itemStack);
   }

   private static boolean ignoreGround(ClientPlayerEntity player) {
      if (!player.method_5799() && !player.method_6059(StatusEffects.field_5902)) {
         ItemStack itemStack = player.method_6118(EquipmentSlot.field_6174);
         if (itemStack.method_31574(Items.field_8833) && ElytraItem.method_7804(itemStack)) {
            player.method_23669();
            return true;
         } else {
            return false;
         }
      } else {
         return false;
      }
   }

   private float getYawDirection() {
      return switch ((Rotation.LockMode)this.elytraFly.yawLockMode.get()) {
         case None -> this.mc.field_1724.method_36454();
         case Smart -> Math.round((this.mc.field_1724.method_36454() + 1.0F) / HighwayBuilder$DiagonalBlockPosProvider$ConstantPool.const_G67FRFAcbr9enRT)
            * SettingValueArgumentType$ConstantPool.const_6Y8mIyXU8Be0Ib8;
         case Simple -> this.elytraFly.yaw.get().floatValue();
         case Target -> this.mc.field_1724.method_36454();
      };
   }

   @Override
   public void onActivate() {
      this.prevFov = (Double)this.mc.field_1690.method_42454().method_41753();
   }

   @Override
   public void onDeactivate() {
      this.unpress();
      this.rubberbanded = false;
      if (this.prevFov != 0.0 && !this.elytraFly.sprint.get()) {
         this.mc.field_1690.method_42454().method_41748(this.prevFov);
      }
   }
}
