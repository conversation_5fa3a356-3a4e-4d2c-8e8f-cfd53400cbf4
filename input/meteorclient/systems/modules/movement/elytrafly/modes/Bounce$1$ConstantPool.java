package meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes;

public final class Bounce$1$ConstantPool {
   public static String const_NDbDawgEFT7mept = S5Ch8aG1DY(2qowNVz1TB("\u0604؎؍\u0603ؖش\u0603؎ؗ؇"));
   public static String const_XJG9NLwnc6nsLJw = 4IjOnoUXoq(2qowNVz1TB("ҟҀҟ"));
   public static int const_OJxWD8AlxhpwtQ7 = (int)(-3608101270886354150L ^ 3608101270236558125L) ^ (int)1553900880L ^ -106586079;
   public static String const_aJUsV2E1vTjilV1 = JyFVln5BvO(2qowNVz1TB("֏ֲֳָֹֿ"));
   public static String const_6IQ1DwqSJJHIec6 = Omuc4ogYQk(2qowNVz1TB("\u0530ԆԏԆԀԗՃԡԏԌԀԈ"));
   public static String const_aVSaC96tX7togzt = x3FHlrcQ1F(2qowNVz1TB("ŨŔřĜşœŐœŎĜœŚĜňŔřĜŏŕŘřŏĜœŚĜňŔřĜŞŐœşŗŏĜňŔŝňĜşŝŒĜŞřĜŞŎœŗřŒĒ"));
   public static double const_LLABegN7D6qgUmB = Double.longBitsToDouble(
      8934785294886213924L ^ -740123748696012030L ^ 8054138951630201287L ^ -2426813443085863967L
   );
}
