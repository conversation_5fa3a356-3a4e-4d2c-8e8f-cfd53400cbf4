package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

public final class ElytraFly$ConstantPool {
   public static String const_lRALYXYlO6dDkGn = 6TflkutiiQ(UbsNnVgwng("ɳ"));
   public static String const_S2bVPibORIIsn8t = GvEKlaBRnV(UbsNnVgwng("̖̍͏"));
   public static int const_hHbvnloed9uiJoj = (int)(798606069114219474L ^ 798606069013892871L) ^ (int)(-155449073 ^ -55481317);
   public static double const_6B2QtfcmoYi56fF = Double.longBitsToDouble(
      -1271820894014162851L ^ -6409753519145650133L ^ -886245566906285817L ^ -369010555331575439L
   );
   public static String const_J4u0ZJm7vaKHbnL = 6SNav5O30G(UbsNnVgwng("ؾؙ\u061c؝؋٘ؗ؍،٘،ؐ؝٘؛ؗؔؗ؊ٖ"));
   public static String const_Gn2i9sw3LQaIRjD = vAZWdwjWgg(UbsNnVgwng("ԵԶԣԣԢ"));
   public static float const_Ag1wBiVE1q0WeyK = Float.intBitsToFloat((int)(368539100 ^ 1804749429) ^ (int)-1614553746L ^ -601967829);
   public static String const_BtnkqANUnyv2RJE = I17Q7QeC7i(UbsNnVgwng("ӮӪӳӸӫ"));
}
