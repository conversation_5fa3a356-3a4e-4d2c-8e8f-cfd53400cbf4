package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

import de.florianmichael.waybackauthlib.WaybackAuthLib$User$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5CommandType$ConstantPool;
import javassist.ByteArrayClassPath$BytecodeURLConnection$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.gui.WidgetScreen$WFullScreenRoot$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WWindow$WHeader$ConstantPool;
import meteordevelopment.meteorclient.renderer.Mesh$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.SelfTrap$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Ambience$Custom$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.render.WireframeEntityRenderer$ConstantPool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket.Mode;
import net.minecraft.util.Hand;
import net.minecraft.util.math.Vec3d;
import org.reflections.serializers.JavaCodeSerializer$ConstantPool;
import org.reflections.vfs.JbossFile$ConstantPool;
import org.reflections.vfs.Vfs$DefaultUrlTypes$3$ConstantPool;

public class ElytraFlightMode {
   protected final MinecraftClient mc;
   protected final ElytraFly elytraFly = Modules.get().get(ElytraFly.class);
   private final ElytraFlightModes type;
   protected boolean lastJumpPressed;
   protected boolean incrementJumpTimer;
   protected boolean lastForwardPressed;
   protected int jumpTimer;
   protected double velX;
   protected double velY;
   protected double velZ;
   protected double ticksLeft;
   protected Vec3d forward;
   protected Vec3d right;
   protected double acceleration;

   public ElytraFlightMode(ElytraFlightModes type) {
      this.mc = MinecraftClient.method_1551();
      this.type = type;
   }

   public void onTick() {
      if (this.elytraFly.autoReplenish.get()) {
         FindItemResult fireworks = InvUtils.find(Items.field_8639);
         if (fireworks.found() && !fireworks.isHotbar()) {
            InvUtils.move().from(fireworks.slot()).toHotbar(this.elytraFly.replenishSlot.get() - 1);
         }
      }

      if (this.elytraFly.replace.get()) {
         ItemStack chestStack = this.mc.field_1724.method_31548().method_7372(2);
         if (chestStack.method_7909() == Items.field_8833 && chestStack.method_7936() - chestStack.method_7919() <= this.elytraFly.replaceDurability.get()) {
            FindItemResult elytra = InvUtils.find(
               stack -> stack.method_7936() - stack.method_7919() > this.elytraFly.replaceDurability.get() && stack.method_7909() == Items.field_8833
            );
            InvUtils.move().from(elytra.slot()).toArmor(2);
         }
      }
   }

   public void onPreTick() {
   }

   public void onPacketSend(PacketEvent.Send event) {
   }

   public void onPacketReceive(PacketEvent.Receive event) {
   }

   public void onPlayerMove() {
   }

   public void onActivate() {
      this.lastJumpPressed = false;
      this.jumpTimer = 0;
      this.ticksLeft = 0.0;
      this.acceleration = 0.0;
   }

   public void onDeactivate() {
   }

   public void autoTakeoff() {
      if (this.incrementJumpTimer) {
         this.jumpTimer++;
      }

      boolean jumpPressed = this.mc.field_1690.field_1903.method_1434();
      if (this.elytraFly.autoTakeOff.get() && jumpPressed) {
         if (!this.lastJumpPressed && !this.mc.field_1724.method_6128()) {
            this.jumpTimer = 0;
            this.incrementJumpTimer = true;
         }

         if (this.jumpTimer >= 8) {
            this.jumpTimer = 0;
            this.incrementJumpTimer = false;
            this.mc.field_1724.method_6100(false);
            this.mc.field_1724.method_5728(true);
            this.mc.field_1724.method_6043();
            this.mc.method_1562().method_52787(new ClientCommandC2SPacket(this.mc.field_1724, Mode.field_12982));
         }
      }

      this.lastJumpPressed = jumpPressed;
   }

   public void handleAutopilot() {
      if (this.mc.field_1724.method_6128()) {
         if (this.elytraFly.autoPilot.get()
            && this.mc.field_1724.method_23318() > this.elytraFly.autoPilotMinimumHeight.get()
            && this.elytraFly.flightMode.get() != ElytraFlightModes.Bounce) {
            this.mc.field_1690.field_1894.method_23481(true);
            this.lastForwardPressed = true;
         }

         if (this.elytraFly.useFireworks.get()) {
            if (this.ticksLeft <= 0.0) {
               this.ticksLeft = this.elytraFly.autoPilotFireworkDelay.get() * WWindow$WHeader$ConstantPool.const_DQeHjjvx1rWVhC9;
               FindItemResult itemResult = InvUtils.findInHotbar(Items.field_8639);
               if (!itemResult.found()) {
                  return;
               }

               if (itemResult.isOffhand()) {
                  this.mc.field_1761.method_2919(this.mc.field_1724, Hand.field_5810);
                  this.mc.field_1724.method_6104(Hand.field_5810);
               } else {
                  InvUtils.swap(itemResult.slot(), true);
                  this.mc.field_1761.method_2919(this.mc.field_1724, Hand.field_5808);
                  this.mc.field_1724.method_6104(Hand.field_5808);
                  InvUtils.swapBack();
               }
            }

            this.ticksLeft--;
         }
      }
   }

   public void handleHorizontalSpeed(PlayerMoveEvent event) {
      boolean a = false;
      boolean b = false;
      if (this.mc.field_1690.field_1894.method_1434()) {
         this.velX = this.velX + this.forward.field_1352 * this.getSpeed() * Vfs$DefaultUrlTypes$3$ConstantPool.const_aAaMbYPuIlDQTAI;
         this.velZ = this.velZ + this.forward.field_1350 * this.getSpeed() * NotebotUtils$ConstantPool.const_7ADgOtxidcOJTSl;
         a = true;
      } else if (this.mc.field_1690.field_1881.method_1434()) {
         this.velX = this.velX - this.forward.field_1352 * this.getSpeed() * JbossFile$ConstantPool.const_OC3UPrBlFJcogqj;
         this.velZ = this.velZ - this.forward.field_1350 * this.getSpeed() * WireframeEntityRenderer$ConstantPool.const_gWt1MmIv1qnvD21;
         a = true;
      }

      if (this.mc.field_1690.field_1849.method_1434()) {
         this.velX = this.velX + this.right.field_1352 * this.getSpeed() * Mesh$ConstantPool.const_SmnuuwHvEEVHNyW;
         this.velZ = this.velZ + this.right.field_1350 * this.getSpeed() * Socks5CommandType$ConstantPool.const_JJ52W9BcTUW7oxF;
         b = true;
      } else if (this.mc.field_1690.field_1913.method_1434()) {
         this.velX = this.velX - this.right.field_1352 * this.getSpeed() * WidgetScreen$WFullScreenRoot$ConstantPool.const_hSmd0khA15mv6iU;
         this.velZ = this.velZ - this.right.field_1350 * this.getSpeed() * JavaCodeSerializer$ConstantPool.const_VgjxXDGTF15EPHw;
         b = true;
      }

      if (a && b) {
         double diagonal = 1.0 / Math.sqrt(ByteArrayClassPath$BytecodeURLConnection$ConstantPool.const_aWVKij0dA9tw4NF);
         this.velX *= diagonal;
         this.velZ *= diagonal;
      }
   }

   public void handleVerticalSpeed(PlayerMoveEvent event) {
      if (this.mc.field_1690.field_1903.method_1434()) {
         this.velY = this.velY + Ambience$Custom$ConstantPool.const_WNBJe3TGJNS87Ig * this.elytraFly.verticalSpeed.get();
      } else if (this.mc.field_1690.field_1832.method_1434()) {
         this.velY = this.velY - WaybackAuthLib$User$ConstantPool.const_ymawQw5A2U1Xetb * this.elytraFly.verticalSpeed.get();
      }
   }

   public void handleFallMultiplier() {
      if (this.velY < 0.0) {
         this.velY = this.velY * this.elytraFly.fallMultiplier.get();
      } else if (this.velY > 0.0) {
         this.velY = 0.0;
      }
   }

   public void handleAcceleration() {
      if (this.elytraFly.acceleration.get()) {
         if (!PlayerUtils.isMoving()) {
            this.acceleration = 0.0;
         }

         this.acceleration = Math.min(
            this.acceleration + this.elytraFly.accelerationMin.get() + this.elytraFly.accelerationStep.get() * SelfTrap$ConstantPool.const_Lis8eOxa7ZViSfb,
            this.elytraFly.horizontalSpeed.get()
         );
      } else {
         this.acceleration = 0.0;
      }
   }

   public void zeroAcceleration() {
      this.acceleration = 0.0;
   }

   protected double getSpeed() {
      return this.elytraFly.acceleration.get() ? this.acceleration : this.elytraFly.horizontalSpeed.get();
   }

   public String getHudString() {
      return this.type.name();
   }
}
