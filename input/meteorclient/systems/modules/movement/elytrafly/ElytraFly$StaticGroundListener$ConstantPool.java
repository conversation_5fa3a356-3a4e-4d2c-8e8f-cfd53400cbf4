package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

public final class ElytraFly$StaticGroundListener$ConstantPool {
   public static String const_A4RYmNN4NtiQBjB = Ql6wvSdttg(cQQtnQOFqT("˦˄ˋˋˊˑʅˉˊ˂ʅˊːˑʅ˒ˍˌˉˀʅˋˊˑʅˉˊ˂˂ˀˁʅˌˋʋ"));
   public static String const_VB8C9q2GOarD1a6 = XVBrn4OW1C(cQQtnQOFqT("|w`w8zwxq8Ebd\u007fxq"));
   public static String const_TaAZhCOKpbXm3PD = 49No1GQGb2(cQQtnQOFqT("МЛГЖПОњЎЕњЈПЖЕЛОрњ"));
   public static String const_LswJdVAJXbGLypn = GaVjYeOdmF(cQQtnQOFqT("ʷʖʔʁ˘ʉʍʑʎʝʊ˘ʏʐʝʖ˘ʁʗʍ˟ʊʝ˘ʑʖ˘ʙ˘ʐʗʔʝ˖"));
   public static int const_l4y8g1pLYFuQbt9 = (int)(-6883436514806395046L ^ -6883436514513082889L) ^ (int)(-151410968 ^ -2054944689);
   public static String const_YdooSyaDyplJgH7 = tonvgaiJw7(cQQtnQOFqT("ŃŴſŵŴţŢıŰıŢŹŰŵŴţıžŧŴţıžŷıťŹŴıŴſťŸťŸŴŢĿ"));
   public static double const_vikaefbQw8WqB4n = Double.longBitsToDouble(
      4848352028990093372L ^ -5915061573929751462L ^ -2725766404009558133L ^ 8422423206726855661L
   );
   public static String const_eAFJc2qePZ0nwYR = OTLJjlmFU7(cQQtnQOFqT("ڏۭ۷۲۹ۧڱ۷ڎۭ۷۲۹ۧڱ۷ڍۭ۷۲۹ۧڱ"));
   public static int const_gjNYjYxbK4N9bHs = (int)(2566476734314544558L ^ -2566476733013571218L) ^ (int)(-1398761521 ^ 30679203);
}
