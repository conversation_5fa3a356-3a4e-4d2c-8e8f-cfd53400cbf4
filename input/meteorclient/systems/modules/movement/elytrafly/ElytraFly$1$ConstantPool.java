package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

public final class ElytraFly$1$ConstantPool {
   public static int const_tvOrjto1ltg6SYZ = (int)(-8956417639070376197L ^ -8956417640051853476L) ^ (int)289818837L ^ 1522934999;
   public static String const_mDQtDCiEL9AgQN3 = vwBFLSbsJ7(hQhOBcr924("ԸԅԎԊԀՋԟԄՋԞԅՆԘԛԎԈԟԊԟԎՅ"));
   public static String const_TkarQUsjcUtB1Zd = o2olam9mTO(hQhOBcr924("؍\u0604\u0604"));
   public static String const_1wJxh6iFxXSL1bl = 7qS1E9Letn(hQhOBcr924("ԴԥԸԴխԳԣԡԬԥ"));
   public static String const_NHwyGWV4cJhxD9O = ghGZnSZn6T(hQhOBcr924("śŸŸŻŽŪ"));
   public static double const_jiqRDtOw0N6pcTd = Double.longBitsToDouble(
      6267696406544116865L ^ -830154142057231030L ^ -6241182431781772254L ^ 5446464097324156393L
   );
   public static double const_Xh19FSROJIRQvaT = Double.longBitsToDouble(
      -2985011674195089120L ^ 768157420579669936L ^ -7502033367437595465L ^ 8371484508248031783L
   );
   public static int const_jmedkWY2l3xqD2m = (int)(7896296122659714666L ^ -7896296122039585200L) ^ (int)(1818822807 ^ -1198715631);
}
