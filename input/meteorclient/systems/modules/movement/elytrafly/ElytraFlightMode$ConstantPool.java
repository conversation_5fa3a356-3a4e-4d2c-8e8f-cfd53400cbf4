package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

public final class ElytraFlightMode$ConstantPool {
   public static String const_nRhVi4VTd22dixy = WmEDL2hQaD(rnvYrj6Q9Y("ɠɻɾɻɺɢɻ"));
   public static int const_FXz42JqUNgN1DvV = (int)((int)347274048L ^ -638865439 ^ ((int)-1026572593L ^ 261548504));
   public static String const_keBlnDjWYM4nrcQ = uS71yTUlSX(rnvYrj6Q9Y("ҚӎӌӑӝӛӛӚҖҗҞӝӟӐӐӑӊҞӊӟӕӛҞӟҞӎӟӌӟӓӛӊӛӌҞӘӑӌҞӘӗӛӒӚҞӌӛӟӚӗӐә"));
   public static String const_VLvesjY78XrgUji = etRivdHeTD(rnvYrj6Q9Y("ԧԅԎԟՄԆԂԅԎԈԙԊԍԟՄԈԇԊԘԘԴ՟՚Փ՟Ր"));
   public static String const_9ui4TvOBcYIL9tn = zPRY4F1SnR(rnvYrj6Q9Y("Jnib@fjb62>"));
   public static String const_JjsU0NruzI3ve8I = keI0sFbvSg(rnvYrj6Q9Y("ڛڶڭڱ"));
   public static double const_pVV7Yx0VDWnNrUH = Double.longBitsToDouble(
      -8377620462394571849L ^ 3839760442518586911L ^ 5383442231314327066L ^ -3773748773980413006L
   );
   public static String const_2tLsVFbQnOJtlOL = 5cAy0DFFds(rnvYrj6Q9Y("ɨɅɅɆɞɚȉɐɆɜȉɝɆȉɚɊɛɆɅɅȉɝɆȉɊɁɈɇɎɌȉɊɈɄɌɛɈȉɍɀɚɝɈɇɊɌȇ"));
}
