package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

public final class ElytraFlightModes$ConstantPool {
   public static String const_OxY6ve6i9fLSDr5 = jmDNLZybeM(lWWaOLLWLq("ͭ͡"));
   public static String const_XYS8w4iX1SWNl22 = z1eSldlE7w(lWWaOLLWLq("ʯˋ˭˦˱˦ʨ˫˦˩ˠʨˈ˥˭ˢˤ˳ʼ˜ˋ˭˦˱˦ʨ˫˦˩ˠʨ˔˳˵ˮ˩ˠʼʮˑ"));
   public static String const_b2XGo8iqcvAluwA = d2TTvOA8C1(lWWaOLLWLq("֚֝ֈ֝\u0590"));
   public static String const_qOtFuBFt7T6vjW7 = qeH4JlQKMB(lWWaOLLWLq("\\{|a|ty|o|{r5nh"));
   public static String const_aIzrLdXYgyU7XOH = w97KOOSr1N(lWWaOLLWLq("ȧȱɦȢȯȼȢȧɦ"));
}
