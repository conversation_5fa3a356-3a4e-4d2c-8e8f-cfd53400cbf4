package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

import io.netty.handler.codec.socks.SocksAuthResponseDecoder$ConstantPool;
import io.netty.handler.codec.socksx.v4.Socks4ClientDecoder$State$ConstantPool;
import io.netty.handler.codec.socksx.v4.Socks4ServerDecoder$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5CommandRequestDecoder$ConstantPool;
import io.netty.handler.proxy.HttpProxyHandler$ConstantPool;
import javassist.ByteArrayClassPath$BytecodeURLConnection$ConstantPool;
import javassist.ClassPool$ConstantPool;
import javassist.CtField$ParamInitializer$ConstantPool;
import javassist.bytecode.AnnotationDefaultAttribute$ConstantPool;
import javassist.bytecode.AttributeInfo$ConstantPool;
import javassist.bytecode.BootstrapMethodsAttribute$BootstrapMethod$ConstantPool;
import javassist.bytecode.FloatInfo$ConstantPool;
import javassist.bytecode.MethodInfo$ConstantPool;
import javassist.bytecode.SourceFileAttribute$ConstantPool;
import javassist.bytecode.analysis.ControlFlow$2$ConstantPool;
import javassist.bytecode.analysis.IntQueue$ConstantPool;
import javassist.bytecode.stackmap.TypeData$NullType$ConstantPool;
import javassist.compiler.JvstCodeGen$ConstantPool;
import javassist.compiler.JvstTypeChecker$ConstantPool;
import javassist.compiler.MemberResolver$ConstantPool;
import javassist.compiler.Token$ConstantPool;
import javassist.compiler.ast.CondExpr$ConstantPool;
import javassist.expr.Cast$ProceedForCast$ConstantPool;
import javassist.scopedpool.ScopedClassPool$ConstantPool;
import javassist.util.proxy.ProxyFactory$1$ConstantPool;
import javassist.util.proxy.ProxyFactory$ClassLoaderProvider$ConstantPool;
import javassist.util.proxy.ProxyObjectOutputStream$ConstantPool;
import javassist.util.proxy.RuntimeSupport$DefaultMethodHandler$ConstantPool;
import javax.annotation.CheckForNull$ConstantPool;
import javax.annotation.WillCloseWhenClosed$ConstantPool;
import meteordevelopment.discordipc.DiscordIPC$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.commands.commands.NameHistoryCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.InteractEntityEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.ItemUseCrosshairTargetEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent;
import meteordevelopment.meteorclient.events.entity.player.StartBreakingBlockEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.TravelEventPost$ConstantPool;
import meteordevelopment.meteorclient.events.game.ItemStackTooltipEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent$Receive$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.renderer.GuiDebugRenderer$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.CommitsScreen$Response$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.MarkerScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.DynamicRegistryListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.ConfigTab$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.FriendsTab$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.MacrosTab$EditMacroScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.input.WMeteorDropdown$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.input.WTextBox$ICompletionItem$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IClientPlayerInteractionManager$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IEntityRenderer$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.renderer.PostProcessRenderer$ConstantPool;
import meteordevelopment.meteorclient.renderer.text.CustomTextRenderer$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnchantmentListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.EnumSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.KeybindSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StringListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.Vector3dSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.HudRenderer$FontHolder$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.AnchorAura$RotationMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AntiBed$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoArmor$ArmorPiece$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoEXP$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoTrap$BottomMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoWeb$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.KillAura$Weapon$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.DirectFly$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.GgboyAutoLog$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.MoveCrit$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.VelocityPlus$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.AntiPacketKick$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.BookBot$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.LongJump$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes.Bounce;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes.Packet;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes.Pitch40;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes.Vanilla;
import meteordevelopment.meteorclient.systems.modules.movement.speed.SpeedModes$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.ChestSwap;
import meteordevelopment.meteorclient.systems.modules.player.FakePlayer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.Portals$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.Rotation;
import meteordevelopment.meteorclient.systems.modules.render.ESP$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Freecam;
import meteordevelopment.meteorclient.systems.modules.render.HandView$SwingMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$MBPIterator$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$12$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$15$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$18$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$5$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.InfinityMiner$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.StashFinder$ChunkScreen$ConstantPool;
import meteordevelopment.meteorclient.systems.proxies.Proxies$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.DamageUtils$RaycastFactory$ConstantPool;
import meteordevelopment.meteorclient.utils.network.OnlinePlayers$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.decoder.SongDecoder$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.decoder.TextSongDecoder$ConstantPool;
import meteordevelopment.meteorclient.utils.render.ByteTexture$ConstantPool;
import meteordevelopment.meteorclient.utils.render.MeshVertexConsumerProvider$ConstantPool;
import meteordevelopment.meteorclient.utils.render.postprocess.StorageOutlineShader$ConstantPool;
import meteordevelopment.meteorclient.utils.tooltip.ContainerTooltipComponent$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockPosX$ConstantPool;
import meteordevelopment.meteorclient.utils.world.CardinalDirection$ConstantPool;
import meteordevelopment.meteorclient.utils.world.Timer$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.listeners.LambdaListener$ConstantPool;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.item.ElytraItem;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket.Mode;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.OnGroundOnly;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult.Type;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.RaycastContext;
import net.minecraft.world.RaycastContext.FluidHandling;
import net.minecraft.world.RaycastContext.ShapeType;
import org.reflections.scanners.TypeAnnotationsScanner$ConstantPool;
import org.reflections.vfs.JbossDir$ConstantPool;
import org.reflections.vfs.SystemDir$ConstantPool;
import org.reflections.vfs.Vfs$DefaultUrlTypes$6$ConstantPool;

public class ElytraFly extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgInventory = this.settings
      .createGroup(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(EnchantmentListSetting$ConstantPool.const_BNBamcJatHCjR18))));
   private final SettingGroup sgAutopilot = this.settings
      .createGroup(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ItemStackTooltipEvent$ConstantPool.const_6IsFCBW73qjS6ev))));
   public final Setting<ElytraFlightModes> flightMode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                           .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(WillCloseWhenClosed$ConstantPool.const_hagWtdX75Kf9IwD)))))
                        .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(TextSongDecoder$ConstantPool.const_enYGY22AyNvGOrO)))))
                     .defaultValue(ElytraFlightModes.Vanilla))
                  .onModuleActivated(flightModesSetting -> this.onModeChanged((ElytraFlightModes)flightModesSetting.get())))
               .onChanged(this::onModeChanged))
            .build()
      );
   public final Setting<Boolean> autoTakeOff = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(Socks5CommandRequestDecoder$ConstantPool.const_axxIHb64xrwa042))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(FriendsTab$ConstantPool.const_769QZcqYw6t5XrN))))
            .defaultValue(false)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> fallMultiplier = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(HighwayBuilder$State$15$ConstantPool.const_VDqRNmHimcK0YOV))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(GuiDebugRenderer$ConstantPool.const_PCQT4hDGZGx4Zr1))))
            .defaultValue(Timer$ConstantPool.const_gEIdRsNj9LF7DWC)
            .min(0.0)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> horizontalSpeed = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(BootstrapMethodsAttribute$BootstrapMethod$ConstantPool.const_xXQKW1FwzPSvmgv))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(MeshVertexConsumerProvider$ConstantPool.const_7hPLh7rnY8WDTWv))))
            .defaultValue(1.0)
            .min(0.0)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> verticalSpeed = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(DirectFly$ConstantPool.const_zsAgQwwv6s2g6B9))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(DynamicRegistryListSettingScreen$ConstantPool.const_QgtrlmTzeGhSc4y))))
            .defaultValue(1.0)
            .min(0.0)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Boolean> acceleration = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(PostProcessRenderer$ConstantPool.const_AfMl2UVdGQdW22c))))
            .defaultValue(false)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> accelerationStep = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(CardinalDirection$ConstantPool.const_eveJudOBxQwdAA7))))
            .min(JvstTypeChecker$ConstantPool.const_fjwvvGqp6rBbH8d)
            .max(MethodInfo$ConstantPool.const_jtWjtQySQT1g692)
            .defaultValue(1.0)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Pitch40 && this.acceleration.get() && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> accelerationMin = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(InteractEntityEvent$ConstantPool.const_7QcrgYkNS8yNWAh))))
            .min(Socks4ServerDecoder$ConstantPool.const_FBiILgdvEjIYetP)
            .defaultValue(0.0)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Pitch40 && this.acceleration.get() && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Boolean> stopInWater = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ContainerTooltipComponent$ConstantPool.const_ugoq4luaL1bfVlO))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ByteTexture$ConstantPool.const_r4lyAEGUuodCBer))))
            .defaultValue(true)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Boolean> dontGoIntoUnloadedChunks = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(AnnotationDefaultAttribute$ConstantPool.const_j6Q3RexA5w6tbje))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(AnchorAura$RotationMode$ConstantPool.const_SITpvdcLZnLuIJ9))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Boolean> autoHover = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(OnlinePlayers$ConstantPool.const_ICdX6knyqtgrRzF))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(MarkerScreen$ConstantPool.const_wYceS0c4abNiT4i))))
            .defaultValue(false)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Boolean> noCrash = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(TypeData$NullType$ConstantPool.const_XZDjVoBCunsvjiO))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(SongDecoder$ConstantPool.const_2dWLVrt45KObRtL))))
            .defaultValue(false)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Integer> crashLookAhead = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(AutoArmor$ArmorPiece$ConstantPool.const_Q7J5D4hubLHS7A7))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(Vector3dSetting$ConstantPool.const_T9cOQgelySNFmae))))
            .defaultValue(5)
            .range(1, 15)
            .sliderMin(1)
            .visible(() -> this.noCrash.get() && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   private final Setting<Boolean> instaDrop = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(FloatInfo$ConstantPool.const_SNUlyUYr1MvVkrD))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(AntiBed$ConstantPool.const_ijM4760LaLqNIwP))))
            .defaultValue(false)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> pitch40lowerBounds = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(AutoEXP$Mode$ConstantPool.const_EQtYQqVwoNyGQnI))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(IClientPlayerInteractionManager$ConstantPool.const_G2i6i4eBv4KljAy))))
            .defaultValue(AutoWeb$ConstantPool.const_DSBoXDwLJ66f6aJ)
            .min(KeybindSetting$ConstantPool.const_CPWdddimRVkmvec)
            .sliderMax(AutoTrap$BottomMode$ConstantPool.const_dbyn1Auf4eu0uIy)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Pitch40)
            .build()
      );
   public final Setting<Double> pitch40upperBounds = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(DamageUtils$RaycastFactory$ConstantPool.const_TttevoqVTAgotj2))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ConfigTab$ConstantPool.const_ZTxBFmNMSgMj1YL))))
            .defaultValue(SourceFileAttribute$ConstantPool.const_D7nO17mTqNDVqWL)
            .min(ProxyFactory$ClassLoaderProvider$ConstantPool.const_k2bMd7eOQgaqwAi)
            .sliderMax(IntQueue$ConstantPool.const_VGSSb5avFkG8rvs)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Pitch40)
            .build()
      );
   public final Setting<Double> pitch40rotationSpeed = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(IEntityRenderer$ConstantPool.const_HqT9QLHqgoaAV8C))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(CheckForNull$ConstantPool.const_ASetvPFYh1dlr6I))))
            .defaultValue(JbossDir$ConstantPool.const_leGVgN3PJIKN2ze)
            .min(1.0)
            .sliderMax(BookBot$ConstantPool.const_3vvBGbyatRU6jai)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Pitch40)
            .build()
      );
   public final Setting<Boolean> autoJump = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(AttributeInfo$ConstantPool.const_8Vdoh8sYboIQfDF))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(LongJump$ConstantPool.const_ayn4bm1gkPLiA6o))))
            .defaultValue(true)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Rotation.LockMode> yawLockMode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                        .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(Cast$ProceedForCast$ConstantPool.const_YHFBAmgwt2rvD3I)))))
                     .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(SocksAuthResponseDecoder$ConstantPool.const_bBwS27vvAnVkyjF)))))
                  .defaultValue(Rotation.LockMode.Smart))
               .visible(() -> this.flightMode.get() == ElytraFlightModes.Bounce))
            .build()
      );
   public final Setting<Double> pitch = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(HandView$SwingMode$ConstantPool.const_amWjAOyhBUbOkQz))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(WTextBox$ICompletionItem$ConstantPool.const_TJenSc6JYgOll2w))))
            .defaultValue(DiscordIPC$ConstantPool.const_boAnGWgHjTG8Aiy)
            .range(0.0, Proxies$ConstantPool.const_Ti14nv9lGe4SoKW)
            .sliderRange(0.0, StorageOutlineShader$ConstantPool.const_9AAxiBIwyXoWSia)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> yaw = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(StringListSetting$Builder$ConstantPool.const_bVetjo6sgE7R29R))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(HudRenderer$FontHolder$ConstantPool.const_QqB4W2GcapSFgrl))))
            .defaultValue(0.0)
            .range(0.0, HighwayBuilder$State$12$ConstantPool.const_iILvZisnxAHjjdV)
            .sliderRange(0.0, CtField$ParamInitializer$ConstantPool.const_gyQpt7Fil7JIDov)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Bounce && this.yawLockMode.get() == Rotation.LockMode.Simple)
            .build()
      );
   public final Setting<Boolean> restart = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(AntiPacketKick$ConstantPool.const_yruOheGkdgBB0sz))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(WMeteorDropdown$ConstantPool.const_VHyecBXnm86giID))))
            .defaultValue(true)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Integer> restartDelay = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(VelocityPlus$ConstantPool.const_wWqR4FmXLILz3mW))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(EnumSetting$ConstantPool.const_mYARlrtyB0XIv6g))))
            .defaultValue(7)
            .min(0)
            .sliderRange(0, 20)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Bounce && this.restart.get())
            .build()
      );
   public final Setting<Boolean> sprint = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ProxyFactory$1$ConstantPool.const_nV0rhlaLtb57NiS))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(StashFinder$ChunkScreen$ConstantPool.const_IvDWxV2f0RddhPl))))
            .defaultValue(true)
            .visible(() -> this.flightMode.get() == ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Boolean> replace = this.sgInventory
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(CommitsScreen$Response$ConstantPool.const_2wGwdfgyob3WKdW))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(GgboyAutoLog$ConstantPool.const_oAzVsLIiqel48hr))))
            .defaultValue(false)
            .build()
      );
   public final Setting<Integer> replaceDurability = this.sgInventory
      .add(
         new IntSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(InfinityMiner$ConstantPool.const_gGq2ilFKTy1GPfn))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ESP$ConstantPool.const_eRAVaC4JxSwHvzy))))
            .defaultValue(2)
            .range(1, (Integer)Items.field_8833.method_57347().method_57829(DataComponentTypes.field_50072) - 1)
            .sliderRange(1, (Integer)Items.field_8833.method_57347().method_57829(DataComponentTypes.field_50072) - 1)
            .visible(this.replace::get)
            .build()
      );
   public final Setting<ElytraFly.ChestSwapMode> chestSwap = this.sgInventory
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(JvstCodeGen$ConstantPool.const_N2cviGIgJ3aSVwv)))))
                  .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(BlockPosX$ConstantPool.const_Ag2CyhwwH8YSLPj)))))
               .defaultValue(ElytraFly.ChestSwapMode.Never))
            .build()
      );
   public final Setting<Boolean> autoReplenish = this.sgInventory
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(HighwayBuilder$State$18$ConstantPool.const_JAxq2IfJtGmQa2r))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(MoveCrit$ConstantPool.const_D4BVk1rWSTS9GZr))))
            .defaultValue(false)
            .build()
      );
   public final Setting<Integer> replenishSlot = this.sgInventory
      .add(
         new IntSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(MemberResolver$ConstantPool.const_pGtNiKC75qHdyVY))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(TypeAnnotationsScanner$ConstantPool.const_l1wbdceFy2AVVFt))))
            .defaultValue(9)
            .range(1, 9)
            .sliderRange(1, 9)
            .visible(this.autoReplenish::get)
            .build()
      );
   public final Setting<Boolean> autoPilot = this.sgAutopilot
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(PacketEvent$Receive$ConstantPool.const_YOlaYAUVdLkLBL9))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(Socks4ClientDecoder$State$ConstantPool.const_NVaASEe2Kr6acjy))))
            .defaultValue(false)
            .visible(() -> this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Boolean> useFireworks = this.sgAutopilot
      .add(
         new BoolSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(HighwayBuilder$MBPIterator$ConstantPool.const_qmH8umeV1uJ6xWM))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(LambdaListener$ConstantPool.const_H4qJ7a3UN82negD))))
            .defaultValue(false)
            .visible(() -> this.autoPilot.get() && this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> autoPilotFireworkDelay = this.sgAutopilot
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(TravelEventPost$ConstantPool.const_BQbdZM4DY4119cd))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ControlFlow$2$ConstantPool.const_QBdb1e9oQQOUv2d))))
            .min(1.0)
            .defaultValue(KillAura$Weapon$ConstantPool.const_XT7riQAiKJtlDDs)
            .sliderMax(ProxyObjectOutputStream$ConstantPool.const_Lgdb84cvMpTSr7V)
            .visible(() -> this.useFireworks.get() && this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   public final Setting<Double> autoPilotMinimumHeight = this.sgAutopilot
      .add(
         new DoubleSetting.Builder()
            .name(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(SystemDir$ConstantPool.const_Sgp24gAutO4F0W6))))
            .description(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ScopedClassPool$ConstantPool.const_GvHmOB6m6TnYgx1))))
            .defaultValue(LambdaListener$ConstantPool.const_lI40ikpyOCJCYnq)
            .min(CondExpr$ConstantPool.const_oiCOO32OqiD4QVJ)
            .sliderMax(ByteArrayClassPath$BytecodeURLConnection$ConstantPool.const_Ecsl6BxcQYO1D29)
            .visible(() -> this.autoPilot.get() && this.flightMode.get() != ElytraFlightModes.Pitch40 && this.flightMode.get() != ElytraFlightModes.Bounce)
            .build()
      );
   private ElytraFlightMode currentMode = new Vanilla();
   private final ElytraFly.StaticGroundListener staticGroundListener = new ElytraFly.StaticGroundListener();
   private final ElytraFly.StaticInstaDropListener staticInstadropListener = new ElytraFly.StaticInstaDropListener();

   public ElytraFly() {
      super(
         Categories.Movement,
         7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(ItemUseCrosshairTargetEvent$ConstantPool.const_y1TrVOjsqvTQu4t))),
         new StringBuilder(7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(SpeedModes$ConstantPool.const_un2JiUgWBggHvyl)))),
         7wlEb01wnO(tKG4n88bwi(bRy1nwjA2t(HttpProxyHandler$ConstantPool.const_BYrNBJdM7rnowsJ)))
      );
   }

   @Override
   public void onActivate() {
      this.currentMode.onActivate();
      if ((this.chestSwap.get() == ElytraFly.ChestSwapMode.Always || this.chestSwap.get() == ElytraFly.ChestSwapMode.WaitForGround)
         && mc.field_1724.method_6118(EquipmentSlot.field_6174).method_7909() != Items.field_8833
         && this.isActive()) {
         Modules.get().get(ChestSwap.class).swap();
      }
   }

   @Override
   public void onDeactivate() {
      if (this.autoPilot.get()) {
         mc.field_1690.field_1894.method_23481(false);
      }

      if (this.chestSwap.get() == ElytraFly.ChestSwapMode.Always && mc.field_1724.method_6118(EquipmentSlot.field_6174).method_7909() == Items.field_8833) {
         Modules.get().get(ChestSwap.class).swap();
      } else if (this.chestSwap.get() == ElytraFly.ChestSwapMode.WaitForGround) {
         this.enableGroundListener();
      }

      if (mc.field_1724.method_6128() && this.instaDrop.get()) {
         this.enableInstaDropListener();
      }

      this.currentMode.onDeactivate();
   }

   @EventHandler
   private void onPlayerMove(PlayerMoveEvent event) {
      if (mc.field_1724.method_6118(EquipmentSlot.field_6174).method_7909() instanceof ElytraItem) {
         this.currentMode.autoTakeoff();
         if (mc.field_1724.method_6128()) {
            if (this.flightMode.get() != ElytraFlightModes.Bounce) {
               this.currentMode.velX = 0.0;
               this.currentMode.velY = event.movement.field_1351;
               this.currentMode.velZ = 0.0;
               this.currentMode.forward = Vec3d.method_1030(0.0F, mc.field_1724.method_36454()).method_1021(Token$ConstantPool.const_VcgqW8Wj80b2edy);
               this.currentMode.right = Vec3d.method_1030(0.0F, mc.field_1724.method_36454() + FakePlayer$ConstantPool.const_tBtXSlq9FaBYTc2)
                  .method_1021(HighwayBuilder$State$5$ConstantPool.const_y2Ct0NFwO2LbYB5);
               if (mc.field_1724.method_5799() && this.stopInWater.get()) {
                  mc.method_1562().method_52787(new ClientCommandC2SPacket(mc.field_1724, Mode.field_12982));
                  return;
               }

               this.currentMode.handleFallMultiplier();
               this.currentMode.handleAutopilot();
               this.currentMode.handleAcceleration();
               this.currentMode.handleHorizontalSpeed(event);
               this.currentMode.handleVerticalSpeed(event);
            }

            int chunkX = (int)((mc.field_1724.method_23317() + this.currentMode.velX) / RuntimeSupport$DefaultMethodHandler$ConstantPool.const_4L1nzeEe1w1b0N6);
            int chunkZ = (int)((mc.field_1724.method_23321() + this.currentMode.velZ) / CustomTextRenderer$ConstantPool.const_OYt4qNdFYxgVYFt);
            if (this.dontGoIntoUnloadedChunks.get()) {
               if (mc.field_1687.method_2935().method_12123(chunkX, chunkZ)) {
                  if (this.flightMode.get() != ElytraFlightModes.Bounce) {
                     ((IVec3d)event.movement).set(this.currentMode.velX, this.currentMode.velY, this.currentMode.velZ);
                  }
               } else {
                  this.currentMode.zeroAcceleration();
                  ((IVec3d)event.movement).set(0.0, this.currentMode.velY, 0.0);
               }
            } else if (this.flightMode.get() != ElytraFlightModes.Bounce) {
               ((IVec3d)event.movement).set(this.currentMode.velX, this.currentMode.velY, this.currentMode.velZ);
            }

            if (this.flightMode.get() != ElytraFlightModes.Bounce) {
               this.currentMode.onPlayerMove();
            }
         } else if (this.currentMode.lastForwardPressed && this.flightMode.get() != ElytraFlightModes.Bounce) {
            mc.field_1690.field_1894.method_23481(false);
            this.currentMode.lastForwardPressed = false;
         }

         if (this.noCrash.get() && mc.field_1724.method_6128() && this.flightMode.get() != ElytraFlightModes.Bounce) {
            Vec3d lookAheadPos = mc.field_1724
               .method_19538()
               .method_1019(mc.field_1724.method_18798().method_1029().method_1021(this.crashLookAhead.get().intValue()));
            RaycastContext raycastContext = new RaycastContext(
               mc.field_1724.method_19538(),
               new Vec3d(lookAheadPos.method_10216(), mc.field_1724.method_23318(), lookAheadPos.method_10215()),
               ShapeType.field_17558,
               FluidHandling.field_1348,
               mc.field_1724
            );
            BlockHitResult hitResult = mc.field_1687.method_17742(raycastContext);
            if (hitResult != null && hitResult.method_17783() == Type.field_1332) {
               ((IVec3d)event.movement).set(0.0, this.currentMode.velY, 0.0);
            }
         }

         if (this.autoHover.get()
            && mc.field_1724.field_3913.field_3903
            && !Modules.get().get(Freecam.class).isActive()
            && mc.field_1724.method_6128()
            && this.flightMode.get() != ElytraFlightModes.Bounce) {
            BlockState underState = mc.field_1687.method_8320(mc.field_1724.method_24515().method_10074());
            Block under = underState.method_26204();
            BlockState under2State = mc.field_1687.method_8320(mc.field_1724.method_24515().method_10074().method_10074());
            Block under2 = under2State.method_26204();
            boolean underCollidable = under.field_23159 || !underState.method_26227().method_15769();
            boolean under2Collidable = under2.field_23159 || !under2State.method_26227().method_15769();
            if (!underCollidable && under2Collidable) {
               ((IVec3d)event.movement).set(event.movement.field_1352, Portals$ConstantPool.const_ObuqP1JysHFOFoL, event.movement.field_1350);
               mc.field_1724
                  .method_36457(
                     MathHelper.method_15363(
                        mc.field_1724.method_5695(0.0F),
                        Render3DEvent$ConstantPool.const_7TilR0S8uIAgl45,
                        Vfs$DefaultUrlTypes$6$ConstantPool.const_ligQD1zklNNYi0g
                     )
                  );
            }

            if (underCollidable) {
               ((IVec3d)event.movement).set(event.movement.field_1352, ClassPool$ConstantPool.const_T6LQlt8kh521iaR, event.movement.field_1350);
               mc.field_1724
                  .method_36457(
                     MathHelper.method_15363(
                        mc.field_1724.method_5695(0.0F),
                        StartBreakingBlockEvent$ConstantPool.const_ZUC9IlLsogcBL7g,
                        MacrosTab$EditMacroScreen$ConstantPool.const_1jQb2SoiQ1hLpzn
                     )
                  );
               if (mc.field_1724.method_19538().field_1351
                  <= mc.field_1724.method_24515().method_10074().method_10264() + NameHistoryCommand$ConstantPool.const_GJWQBFC76PW1Eo7) {
                  ((IVec3d)event.movement).set(event.movement.field_1352, 0.0, event.movement.field_1350);
                  mc.field_1724.method_5660(false);
                  mc.field_1724.field_3913.field_3903 = false;
               }
            }
         }
      }
   }

   public boolean canPacketEfly() {
      return this.isActive()
         && this.flightMode.get() == ElytraFlightModes.Packet
         && mc.field_1724.method_6118(EquipmentSlot.field_6174).method_7909() instanceof ElytraItem
         && !mc.field_1724.method_24828();
   }

   @EventHandler
   private void onTick(TickEvent.Post event) {
      this.currentMode.onTick();
   }

   @EventHandler
   private void onPreTick(TickEvent.Pre event) {
      this.currentMode.onPreTick();
   }

   @EventHandler
   private void onPacketSend(PacketEvent.Send event) {
      this.currentMode.onPacketSend(event);
   }

   @EventHandler
   private void onPacketReceive(PacketEvent.Receive event) {
      this.currentMode.onPacketReceive(event);
   }

   private void onModeChanged(ElytraFlightModes mode) {
      switch (mode) {
         case Vanilla:
            this.currentMode = new Vanilla();
            break;
         case Packet:
            this.currentMode = new Packet();
            break;
         case Pitch40:
            this.currentMode = new Pitch40();
            this.autoPilot.set(false);
            break;
         case Bounce:
            this.currentMode = new Bounce();
      }
   }

   protected void enableGroundListener() {
      MeteorClient.EVENT_BUS.subscribe(this.staticGroundListener);
   }

   protected void disableGroundListener() {
      MeteorClient.EVENT_BUS.unsubscribe(this.staticGroundListener);
   }

   protected void enableInstaDropListener() {
      MeteorClient.EVENT_BUS.subscribe(this.staticInstadropListener);
   }

   protected void disableInstaDropListener() {
      MeteorClient.EVENT_BUS.unsubscribe(this.staticInstadropListener);
   }

   @Override
   public String getInfoString() {
      return this.currentMode.getHudString();
   }

   public static enum AutoPilotMode {
      Vanilla,
      Pitch40;
   }

   public static enum ChestSwapMode {
      Always,
      Never,
      WaitForGround;
   }

   private class StaticGroundListener {
      @EventHandler
      private void chestSwapGroundListener(PlayerMoveEvent event) {
         if (ElytraFly.mc.field_1724 != null
            && ElytraFly.mc.field_1724.method_24828()
            && ElytraFly.mc.field_1724.method_6118(EquipmentSlot.field_6174).method_7909() == Items.field_8833) {
            Modules.get().get(ChestSwap.class).swap();
            ElytraFly.this.disableGroundListener();
         }
      }
   }

   private class StaticInstaDropListener {
      @EventHandler
      private void onInstadropTick(TickEvent.Post event) {
         if (ElytraFly.mc.field_1724 != null && ElytraFly.mc.field_1724.method_6128()) {
            ElytraFly.mc.field_1724.method_18800(0.0, 0.0, 0.0);
            ElytraFly.mc.field_1724.field_3944.method_52787(new OnGroundOnly(true));
         } else {
            ElytraFly.this.disableInstaDropListener();
         }
      }
   }
}
