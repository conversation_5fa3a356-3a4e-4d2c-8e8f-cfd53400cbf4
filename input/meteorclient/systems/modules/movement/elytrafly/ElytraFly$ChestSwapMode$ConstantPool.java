package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

public final class ElytraFly$ChestSwapMode$ConstantPool {
   public static String const_iQAvLOt3LgZDtxi = bsrw97VadQ(
      B9NO3vfDTY("υΓλυΓΤΉΓ\u0383ΏΎΎ΅\u0383ΔΣΏ\u038d\u038d\u0381Ύ΄υΓνπΤΉΓ\u0383ΏΎΎ΅\u0383Δ΅΄π\u0382ΙπΕΓ΅Βώ")
   );
   public static String const_7dDOthOH04vSF4a = bMZDWRjhIR(B9NO3vfDTY("٪ٻ٦٪٫٬ٻ٭رٷٽٱٰ٭رٹ٫ٷرٽٷ٬ٽٲٻذٮٰٹ"));
   public static String const_tlbSsiIn4s7NjyV = KaCGlCcoAb(B9NO3vfDTY("ϞϙϋόΕϚϊϝϙϓ"));
   public static String const_YKuOaUBGud1HaBL = d7TgbMIeXX(B9NO3vfDTY("̡̞̹Ͷ̴̱̿Ͷ̢̳̾Ͷ̷̷̲̻̱̳Ͷ̢̢̳̮Ͷ̥̹̣̺̲̾Ͷ̴̳\u0378"));
   public static int const_BfRZTOAAM2aNGF7 = (int)(-8030774896450599569L ^ -8030774897685852848L) ^ (int)93872724L ^ 1546120892;
   public static String const_AbahVm9gR79BmDg = 2mi5rjqSOe(B9NO3vfDTY("ŝłřŌřł"));
   public static String const_vg9D6jIXBvQJI74 = OE2IGitaL1(B9NO3vfDTY("\u07be\u07b5ޤ\u07b8\u07b5ޢ߽ޣ\u07bbީ߽\u07b3\u07bf\u07bc\u07bfޢ"));
}
