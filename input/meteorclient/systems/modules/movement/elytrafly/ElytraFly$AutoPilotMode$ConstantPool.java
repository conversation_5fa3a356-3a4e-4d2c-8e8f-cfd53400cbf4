package meteordevelopment.meteorclient.systems.modules.movement.elytrafly;

public final class ElytraFly$AutoPilotMode$ConstantPool {
   public static String const_YDJUBgqwAWW2HYk = 9WXVdNCd8o(IVW0DuQ1Oy("\u009f\u0081\u0086\u008c\u0087\u009f\u009b"));
   public static String const_l69IKJLaYoxNas1 = apQKqdeFLG(IVW0DuQ1Oy("ٸٻٺٳعٷٻٹٹٵٺٰع٤٦ٱٲٽ٬"));
   public static String const_nahHlo9k44fvxsj = h8owVUWtjS(IVW0DuQ1Oy("ܢ"));
   public static int const_wJrOnjSGNMRvn7B = (int)(-6399449058008541681L ^ -6399449058780712921L) ^ (int)(-209174553 ^ -1517053219);
   public static String const_DjkYOOIxen2jKP6 = F4QWgnJoZs(IVW0DuQ1Oy("߯"));
}
