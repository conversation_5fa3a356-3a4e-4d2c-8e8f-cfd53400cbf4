package meteordevelopment.meteorclient.systems.modules.movement;

public final class TridentBoost$ConstantPool {
   public static double const_azw2wkroOyWY87F = Double.longBitsToDouble(
      -9212835500678538061L ^ -6160667522035400140L ^ 3159368302376454767L ^ 4709027778383500520L
   );
   public static String const_ygj77OMuT0VYMBP = S0l9rGOaJ2(eLTr4v9YWY("ųűŬŠŦŰŰŬűżŪŧŦŭŷŪťŪŦű"));
   public static String const_qNNF4qNb77ZLYUO = oGDn5zJtqH(eLTr4v9YWY("ĐĬġŤķħĥĨġŪ"));
   public static float const_7TkNMuTubPzNiUg = Float.intBitsToFloat((int)-1839826604L ^ 1125165374 ^ (int)-772958472L ^ 1041738499);
   public static int const_owSiHbWXw6v2ZZd = (int)((int)-471453510L ^ 696306039 ^ (int)(-1160910760 ^ 1890260414));
}
