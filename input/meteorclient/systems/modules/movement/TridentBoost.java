package meteordevelopment.meteorclient.systems.modules.movement;

import javassist.bytecode.Opcode$ConstantPool;
import javassist.bytecode.annotation.NoSuchClassError$ConstantPool;
import javassist.compiler.TokenId$ConstantPool;
import javassist.convert.TransformNew$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.DropCommand$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.ggboy.MoveCrit$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WaypointsModule$WIcon$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.SortPriority$ConstantPool;

public class TridentBoost extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final Setting<Double> multiplier = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(Cb9H6FQAeW(X6zswAWwWA(lvvIjnwz25(AutoWasp$Action$ConstantPool.const_EQJYUSHrB2AL5Kf))))
            .description(Cb9H6FQAeW(X6zswAWwWA(lvvIjnwz25(NoSuchClassError$ConstantPool.const_dfH3o71FKYlOI2y))))
            .defaultValue(MoveCrit$ConstantPool.const_GtTwhnqDaDVAtgo)
            .min(WaypointsModule$WIcon$ConstantPool.const_jOTHyoSWEScOKIY)
            .sliderMin(1.0)
            .build()
      );
   private final Setting<Boolean> allowOutOfWater = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(Cb9H6FQAeW(X6zswAWwWA(lvvIjnwz25(SortPriority$ConstantPool.const_YqoFoLsmtJOSOhy))))
            .description(Cb9H6FQAeW(X6zswAWwWA(lvvIjnwz25(TokenId$ConstantPool.const_uPjjoxAFQd1Bjia))))
            .defaultValue(true)
            .build()
      );

   public TridentBoost() {
      super(
         Categories.Movement,
         Cb9H6FQAeW(X6zswAWwWA(lvvIjnwz25(DropCommand$ConstantPool.const_mEOuqIIFkFjDWFI))),
         new StringBuilder(Cb9H6FQAeW(X6zswAWwWA(lvvIjnwz25(Opcode$ConstantPool.const_fIGMAGz5yUzpiFw)))),
         Cb9H6FQAeW(X6zswAWwWA(lvvIjnwz25(TransformNew$ConstantPool.const_FJNkMAN46hwNljH)))
      );
   }

   public double getMultiplier() {
      return this.isActive() ? this.multiplier.get() : 1.0;
   }

   public boolean allowOutOfWater() {
      return this.isActive() ? this.allowOutOfWater.get() : false;
   }
}
