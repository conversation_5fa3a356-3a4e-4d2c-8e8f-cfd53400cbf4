package meteordevelopment.meteorclient.systems.modules.movement;

public final class Velocity$ConstantPool {
   public static String const_nqLMllVgYzoG6Kd = 9dMDoxfeOZ(
      Rrv9YeLlw2("ϮςϘρω\u038dσςϙ\u038dςϏϙότσ\u038dϝόϟόπψϙψϟϞ\u038dϋςϟ\u038dωψϞώϟτϝϙςϟ\u038d϶ϝςϞ\u038dΐ\u038d")
   );
   public static String const_afTQ9sSJaADedYx = oTbKltXAaQ(Rrv9YeLlw2("ؘأؼ"));
   public static String const_6N286z6NWdWkDaT = tO6rLlVBak(Rrv9YeLlw2("\u00023'!7!r%:;>7r?;<;<5|"));
   public static String const_f6YaKyoL7qXnykH = u75xCVvODa(Rrv9YeLlw2("ؙٖٟ٘ٛٗٝٚـّنٕٗـؙِٜٕٚ"));
   public static float const_eEOQQJqB9drYode = Float.intBitsToFloat((int)-1988936423L ^ 1411343735 ^ (int)(1001810311 ^ -1487261207));
   public static String const_CefJVal1HtbAVS4 = 8hW8L9qqH7(Rrv9YeLlw2("ͱ"));
}
