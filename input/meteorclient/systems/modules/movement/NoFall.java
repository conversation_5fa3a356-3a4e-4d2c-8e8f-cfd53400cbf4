package meteordevelopment.meteorclient.systems.modules.movement;

import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthRequest$ConstantPool;
import java.util.function.Predicate;
import javassist.CtMethod$StringConstParameter$ConstantPool;
import javassist.bytecode.CodeAttribute$ConstantPool;
import javassist.compiler.ast.MethodDecl$ConstantPool;
import javassist.expr.Expr$ConstantPool;
import javassist.scopedpool.ScopedClassPoolRepository$ConstantPool;
import javax.annotation.MatchesPattern$ConstantPool;
import meteordevelopment.discordipc.RichPresence$Timestamps$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.FovCommand$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixin.PlayerMoveC2SPacketAccessor;
import meteordevelopment.meteorclient.mixininterface.IPlayerMoveC2SPacket;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.renderer.DrawMode$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.Offhand$Item$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.Surround$Center$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoTool$ListMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.LightOverlay$Spawn$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.PopChams$GhostPlayer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Timer$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.EntityUtils;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.item.BlockItem;
import net.minecraft.item.Item;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult.Type;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.RaycastContext;
import net.minecraft.world.RaycastContext.FluidHandling;
import net.minecraft.world.RaycastContext.ShapeType;
import org.reflections.scanners.Scanners$7$ConstantPool;

public class NoFall extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final Setting<NoFall.Mode> mode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(CodeAttribute$ConstantPool.const_szMBKQApTwGny5L)))))
                  .description(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(RichPresence$Timestamps$ConstantPool.const_OOY79SqIHBPH8QR)))))
               .defaultValue(NoFall.Mode.Packet))
            .build()
      );
   private final Setting<NoFall.PlacedItem> placedItem = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                        .name(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(Offhand$Item$ConstantPool.const_kAMTgniqnQq9ysS)))))
                     .description(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(LightOverlay$Spawn$ConstantPool.const_4Nd3gHoGN1eGVwg)))))
                  .defaultValue(NoFall.PlacedItem.Bucket))
               .visible(() -> this.mode.get() == NoFall.Mode.Place))
            .build()
      );
   private final Setting<NoFall.PlaceMode> airPlaceMode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                        .name(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(Scanners$7$ConstantPool.const_NFSFnZFgQTwQA4g)))))
                     .description(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(Socks5PasswordAuthRequest$ConstantPool.const_2L6jju8BvD9jILO)))))
                  .defaultValue(NoFall.PlaceMode.BeforeDeath))
               .visible(() -> this.mode.get() == NoFall.Mode.AirPlace))
            .build()
      );
   private final Setting<Boolean> anchor = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(MethodDecl$ConstantPool.const_ta3JF0bj7BrxhbB))))
            .description(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(Expr$ConstantPool.const_l5m6wYzbESQqNAO))))
            .defaultValue(true)
            .visible(() -> this.mode.get() != NoFall.Mode.Packet)
            .build()
      );
   private final Setting<Boolean> antiBounce = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(MatchesPattern$ConstantPool.const_7vBMu6vAa1aQ1iL))))
            .description(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(Surround$Center$ConstantPool.const_2eAiNl8C0wVS7YG))))
            .defaultValue(true)
            .build()
      );
   private boolean placedWater;
   private BlockPos targetPos;
   private int timer;
   private boolean prePathManagerNoFall;

   public NoFall() {
      super(
         Categories.Movement,
         7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(DrawMode$ConstantPool.const_WlJ6SV9FyylYBhg))),
         new StringBuilder(7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(FovCommand$ConstantPool.const_F9AxY4ZLHe5WI41)))),
         7zihDHTReJ(tEVlYIFotF(lVvRItgIYe(CtMethod$StringConstParameter$ConstantPool.const_t5Dubjkvvi6ffWs)))
      );
   }

   @Override
   public void onActivate() {
      this.prePathManagerNoFall = PathManagers.get().getSettings().getNoFall().get();
      if (this.mode.get() == NoFall.Mode.Packet) {
         PathManagers.get().getSettings().getNoFall().set(true);
      }

      this.placedWater = false;
   }

   @Override
   public void onDeactivate() {
      PathManagers.get().getSettings().getNoFall().set(this.prePathManagerNoFall);
   }

   @EventHandler
   private void onSendPacket(PacketEvent.Send event) {
      if (!mc.field_1724.method_31549().field_7477
         && event.packet instanceof PlayerMoveC2SPacket
         && this.mode.get() == NoFall.Mode.Packet
         && ((IPlayerMoveC2SPacket)event.packet).getTag() != 1337) {
         if (!Modules.get().isActive(Flight.class)) {
            if (mc.field_1724.method_6128()) {
               return;
            }

            if (mc.field_1724.method_18798().field_1351 > AutoWalk$Direction$ConstantPool.const_b5AB27aIqqVgLyd) {
               return;
            }

            ((PlayerMoveC2SPacketAccessor)event.packet).setOnGround(true);
         } else {
            ((PlayerMoveC2SPacketAccessor)event.packet).setOnGround(true);
         }
      }
   }

   @EventHandler
   private void onTick(TickEvent.Pre event) {
      if (this.timer > 20) {
         this.placedWater = false;
         this.timer = 0;
      }

      if (!mc.field_1724.method_31549().field_7477) {
         if (this.mode.get() == NoFall.Mode.AirPlace) {
            if (!this.airPlaceMode.get().test(mc.field_1724.field_6017)) {
               return;
            }

            if (this.anchor.get()) {
               PlayerUtils.centerPlayer();
            }

            Rotations.rotate(
               mc.field_1724.method_36454(),
               PopChams$GhostPlayer$ConstantPool.const_vUQFqyW0etUQzLo,
               Timer$ConstantPool.const_IYnAJQFDVlqSnye,
               () -> {
                  double preY = mc.field_1724.method_18798().field_1351;
                  ((IVec3d)mc.field_1724.method_18798()).setY(0.0);
                  BlockUtils.place(
                     mc.field_1724.method_24515().method_10074(),
                     InvUtils.findInHotbar(itemStack -> itemStack.method_7909() instanceof BlockItem),
                     false,
                     0,
                     true
                  );
                  ((IVec3d)mc.field_1724.method_18798()).setY(preY);
               }
            );
         } else if (this.mode.get() == NoFall.Mode.Place) {
            NoFall.PlacedItem placedItem1 = mc.field_1687.method_8597().comp_644() && this.placedItem.get() == NoFall.PlacedItem.Bucket
               ? NoFall.PlacedItem.PowderSnow
               : this.placedItem.get();
            if (mc.field_1724.field_6017 > ScopedClassPoolRepository$ConstantPool.const_mLBJ65F7rHendYx && !EntityUtils.isAboveWater(mc.field_1724)) {
               Item item = placedItem1.item;
               FindItemResult findItemResult = InvUtils.findInHotbar(item);
               if (!findItemResult.found()) {
                  return;
               }

               if (this.anchor.get()) {
                  PlayerUtils.centerPlayer();
               }

               BlockHitResult result = mc.field_1687
                  .method_17742(
                     new RaycastContext(
                        mc.field_1724.method_19538(),
                        mc.field_1724.method_19538().method_1023(0.0, AutoTool$ListMode$ConstantPool.const_NnG21b3p4QM6s2f, 0.0),
                        ShapeType.field_17559,
                        FluidHandling.field_1348,
                        mc.field_1724
                     )
                  );
               if (result != null && result.method_17783() == Type.field_1332) {
                  this.targetPos = result.method_17777().method_10084();
                  if (placedItem1 == NoFall.PlacedItem.Bucket) {
                     this.useItem(findItemResult, true, this.targetPos, true);
                  } else {
                     this.useItem(findItemResult, placedItem1 == NoFall.PlacedItem.PowderSnow, this.targetPos, false);
                  }
               }
            }

            if (this.placedWater) {
               this.timer++;
               if (mc.field_1724.method_55667().method_26204() == placedItem1.block) {
                  this.useItem(InvUtils.findInHotbar(Items.field_8550), false, this.targetPos, true);
               } else if (mc.field_1687.method_8320(mc.field_1724.method_24515().method_10074()).method_26204() == Blocks.field_27879
                  && mc.field_1724.field_6017 == 0.0F
                  && placedItem1.block == Blocks.field_27879) {
                  this.useItem(InvUtils.findInHotbar(Items.field_8550), false, this.targetPos.method_10074(), true);
               }
            }
         }
      }
   }

   public boolean cancelBounce() {
      return this.isActive() && this.antiBounce.get();
   }

   private void useItem(FindItemResult item, boolean placedWater, BlockPos blockPos, boolean interactItem) {
      if (item.found()) {
         if (interactItem) {
            Rotations.rotate(Rotations.getYaw(blockPos), Rotations.getPitch(blockPos), 10, true, () -> {
               if (item.isOffhand()) {
                  mc.field_1761.method_2919(mc.field_1724, Hand.field_5810);
               } else {
                  InvUtils.swap(item.slot(), true);
                  mc.field_1761.method_2919(mc.field_1724, Hand.field_5808);
                  InvUtils.swapBack();
               }
            });
         } else {
            BlockUtils.place(blockPos, item, true, 10, true);
         }

         this.placedWater = placedWater;
      }
   }

   @Override
   public String getInfoString() {
      return this.mode.get().toString();
   }

   public static enum Mode {
      Packet,
      AirPlace,
      Place;
   }

   public static enum PlaceMode {
      BeforeDamage(height -> height > 2.0F),
      BeforeDeath(height -> height > Math.max(PlayerUtils.getTotalHealth(), 2.0F));

      private final Predicate<Float> fallHeight;

      private PlaceMode(Predicate<Float> fallHeight) {
         this.fallHeight = fallHeight;
      }

      public boolean test(float fallheight) {
         return this.fallHeight.test(fallheight);
      }
   }

   public static enum PlacedItem {
      Bucket(Items.field_8705, Blocks.field_10382),
      PowderSnow(Items.field_27876, Blocks.field_27879),
      HayBale(Items.field_17528, Blocks.field_10359),
      Cobweb(Items.field_8786, Blocks.field_10343),
      SlimeBlock(Items.field_8828, Blocks.field_10030);

      private final Item item;
      private final Block block;

      private PlacedItem(Item item, Block block) {
         this.item = item;
         this.block = block;
      }
   }
}
