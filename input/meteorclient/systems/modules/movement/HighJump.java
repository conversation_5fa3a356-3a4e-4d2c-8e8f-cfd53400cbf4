package meteordevelopment.meteorclient.systems.modules.movement;

import io.netty.handler.proxy.HttpProxyHandler$HttpClientCodecWrapper$ConstantPool;
import javassist.bytecode.annotation.NoSuchClassError$ConstantPool;
import javassist.bytecode.annotation.TypeAnnotationsWriter$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.JumpVelocityMultiplierEvent;
import meteordevelopment.meteorclient.gui.renderer.operations.TextOperation$ConstantPool;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$1$ConstantPool;
import meteordevelopment.orbit.EventHandler;

public class HighJump extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final Setting<Double> multiplier = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(Dyo11Al04G(4eF8wDbLIk(y4mz4EwaTd(HttpProxyHandler$HttpClientCodecWrapper$ConstantPool.const_hJiutSgDqOPn53y))))
            .description(Dyo11Al04G(4eF8wDbLIk(y4mz4EwaTd(HighwayBuilder$State$1$ConstantPool.const_GTayUDVaBkWTTyL))))
            .defaultValue(1.0)
            .min(0.0)
            .build()
      );

   public HighJump() {
      super(
         Categories.Movement,
         Dyo11Al04G(4eF8wDbLIk(y4mz4EwaTd(TextOperation$ConstantPool.const_RmfjB1hidsGkad6))),
         new StringBuilder(Dyo11Al04G(4eF8wDbLIk(y4mz4EwaTd(TypeAnnotationsWriter$ConstantPool.const_nvAyCt9e8A61irM)))),
         Dyo11Al04G(4eF8wDbLIk(y4mz4EwaTd(NoSuchClassError$ConstantPool.const_IheDXo9NZa8gyo1)))
      );
   }

   @EventHandler
   private void onJumpVelocityMultiplier(JumpVelocityMultiplierEvent event) {
      event.multiplier = (float)(event.multiplier * this.multiplier.get());
   }
}
