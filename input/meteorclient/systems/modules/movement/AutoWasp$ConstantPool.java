package meteordevelopment.meteorclient.systems.modules.movement;

public final class AutoWasp$ConstantPool {
   public static String const_2D6li7yXTqFHjQO = iqT38GC9AJ(yILgNBViJq("\u07b9ށރޒޫރޒއމބތރޅޒ"));
   public static String const_ZT1FTSavIM1jyiD = FOyuwNd5dV(yILgNBViJq("䤀茶鷓妭"));
   public static String const_TxQdGbIHVPOp3GB = 66owW5QR1c(yILgNBViJq("ƭƑƜǙƍƐƚƒǙƝƜƕƘƀǙƍƖǙƋƜƉƕƜƗƐƊƑǙƀƖƌƋǙƑƖƍƛƘƋǗ"));
   public static String const_W9ljY2EIQmBhxwT = tE3WFQqtUS(yILgNBViJq("ַ֚րփ֟֒֊րד\u0590ֆրև֚֜֞։֑֖֒֟ד֖֝֒֞և֒֔րד֑֒֜օ֖דփ֟֒֊֖ցրןד֚և֖֞րד֒֝֗ד֜և֛֖ցד֖֝և֚և֖֚րם"));
   public static int const_vOGrbDQGXL2Ul7O = (int)(1335481880916662245L ^ -1335481879438791836L) ^ (int)(1284164325 ^ -862145388);
}
