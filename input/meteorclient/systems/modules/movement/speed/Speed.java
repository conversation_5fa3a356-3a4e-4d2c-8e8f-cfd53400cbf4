package meteordevelopment.meteorclient.systems.modules.movement.speed;

import io.netty.handler.codec.socks.SocksResponseType$ConstantPool;
import javassist.CtMethod$LongConstParameter$ConstantPool;
import javassist.bytecode.ClassFileWriter$FieldWriter$ConstantPool;
import javassist.bytecode.ParameterAnnotationsAttribute$ConstantPool;
import javassist.expr.Cast$ProceedForCast$ConstantPool;
import javassist.tools.rmi.Proxy$ConstantPool;
import javax.annotation.WillNotClose$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent;
import meteordevelopment.meteorclient.events.entity.player.SendMovementPacketsEvent$ConstantPool;
import meteordevelopment.meteorclient.events.game.ItemStackTooltipEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.WMeteorWindow$WMeteorHeader$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IVisible$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.macros.Macros$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoBuy$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoCrit$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.speed.modes.Strafe;
import meteordevelopment.meteorclient.systems.modules.movement.speed.modes.Vanilla;
import meteordevelopment.meteorclient.systems.modules.movement.speed.modes.Vanilla$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.SpeedMine$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$IBlockPosProvider$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$RestockTask$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Timer;
import meteordevelopment.meteorclient.utils.misc.NbtUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.network.OnlinePlayers$ConstantPool;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.Rotations$ConstantPool;
import meteordevelopment.meteorclient.utils.render.PlayerHeadTexture$ConstantPool;
import meteordevelopment.meteorclient.utils.tooltip.ContainerTooltipComponent$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockIterator$Callback$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.utils.SFunction$ConstantPool;
import net.minecraft.entity.MovementType;
import net.minecraft.network.packet.s2c.play.PlayerPositionLookS2CPacket;

public class Speed extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   public final Setting<SpeedModes> speedMode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                           .name(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(CtMethod$LongConstParameter$ConstantPool.const_rJlOPRhPOoyuoz1)))))
                        .description(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(HighwayBuilder$IBlockPosProvider$ConstantPool.const_cODeZqiggUfiNrT)))))
                     .defaultValue(SpeedModes.Vanilla))
                  .onModuleActivated(speedModesSetting -> this.onSpeedModeChanged((SpeedModes)speedModesSetting.get())))
               .onChanged(this::onSpeedModeChanged))
            .build()
      );
   public final Setting<Double> vanillaSpeed = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(BlockIterator$Callback$ConstantPool.const_JbyvAYOgF1NW69l))))
            .description(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(OnlinePlayers$ConstantPool.const_0tkV6ld1lATVnBW))))
            .defaultValue(ItemStackTooltipEvent$ConstantPool.const_al0nelSDenSLgOY)
            .min(0.0)
            .sliderMax(HighwayBuilder$RestockTask$ConstantPool.const_qrSkeoI0SqWl4LB)
            .visible(() -> this.speedMode.get() == SpeedModes.Vanilla)
            .build()
      );
   public final Setting<Double> ncpSpeed = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(AutoCrit$ConstantPool.const_cIrXTfkB54JGe9T))))
            .description(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(SendMovementPacketsEvent$ConstantPool.const_QHYTyL6LIDIiSP2))))
            .visible(() -> this.speedMode.get() == SpeedModes.Strafe)
            .defaultValue(Macros$ConstantPool.const_AVqAcWarlFSDzvy)
            .min(0.0)
            .sliderMax(WMeteorWindow$WMeteorHeader$ConstantPool.const_GSSwVH4yhZtA6uw)
            .build()
      );
   public final Setting<Boolean> ncpSpeedLimit = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(SocksResponseType$ConstantPool.const_IEV5pWNQIVWOyPC))))
            .description(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(PlayerHeadTexture$ConstantPool.const_iviYwvG4lvvBF2y))))
            .visible(() -> this.speedMode.get() == SpeedModes.Strafe)
            .defaultValue(false)
            .build()
      );
   public final Setting<Double> timer = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(HighwayBuilder$State$1$ConstantPool.const_r59jQEafyOqIvlw))))
            .description(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(IVisible$ConstantPool.const_MDYlTGzdbIsZbyJ))))
            .defaultValue(1.0)
            .min(ParameterAnnotationsAttribute$ConstantPool.const_I9w6BA64uQ3iIqZ)
            .sliderMin(Proxy$ConstantPool.const_Ggf8wnyIu1SYZxG)
            .sliderMax(NbtUtils$ConstantPool.const_9gxTdB8VkQ7RAvJ)
            .build()
      );
   public final Setting<Boolean> inLiquids = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(AutoBuy$ConstantPool.const_hJDLOkvgGQoPsLh))))
            .description(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(Rotations$ConstantPool.const_88vOotuVHNNWx97))))
            .defaultValue(false)
            .build()
      );
   public final Setting<Boolean> whenSneaking = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(ContainerTooltipComponent$ConstantPool.const_OVaBNFla3wNGAqO))))
            .description(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(SpeedMine$ConstantPool.const_WV2G5BjA12IdJm5))))
            .defaultValue(false)
            .build()
      );
   public final Setting<Boolean> vanillaOnGround = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(Cast$ProceedForCast$ConstantPool.const_Dr2ediXtGTSmtuP))))
            .description(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(Vanilla$ConstantPool.const_yWAkiTFqCwrhE9E))))
            .visible(() -> this.speedMode.get() == SpeedModes.Vanilla)
            .defaultValue(false)
            .build()
      );
   private SpeedMode currentMode;

   public Speed() {
      super(
         Categories.Movement,
         AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(ClassFileWriter$FieldWriter$ConstantPool.const_INJbd76MnywMe9V))),
         new StringBuilder(AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(WillNotClose$ConstantPool.const_xLWwddJ4lhMQvYJ)))),
         AOrZ22VJhT(q4eSHvGCqo(kB92irkPbt(SFunction$ConstantPool.const_3Qa6aLnivlVDrOu)))
      );
      this.onSpeedModeChanged(this.speedMode.get());
   }

   @Override
   public void onActivate() {
      this.currentMode.onActivate();
   }

   @Override
   public void onDeactivate() {
      Modules.get().get(Timer.class).setOverride(1.0);
      this.currentMode.onDeactivate();
   }

   @EventHandler
   private void onPlayerMove(PlayerMoveEvent event) {
      if (event.type == MovementType.field_6308 && !mc.field_1724.method_6128() && !mc.field_1724.method_6101() && mc.field_1724.method_5854() == null) {
         if (this.whenSneaking.get() || !mc.field_1724.method_5715()) {
            if (!this.vanillaOnGround.get() || mc.field_1724.method_24828() || this.speedMode.get() != SpeedModes.Vanilla) {
               if (this.inLiquids.get() || !mc.field_1724.method_5799() && !mc.field_1724.method_5771()) {
                  if (this.timer.get() != 1.0) {
                     Modules.get().get(Timer.class).setOverride(PlayerUtils.isMoving() ? this.timer.get() : 1.0);
                  }

                  this.currentMode.onMove(event);
               }
            }
         }
      }
   }

   @EventHandler
   private void onPreTick(TickEvent.Pre event) {
      if (!mc.field_1724.method_6128() && !mc.field_1724.method_6101() && mc.field_1724.method_5854() == null) {
         if (this.whenSneaking.get() || !mc.field_1724.method_5715()) {
            if (!this.vanillaOnGround.get() || mc.field_1724.method_24828() || this.speedMode.get() != SpeedModes.Vanilla) {
               if (this.inLiquids.get() || !mc.field_1724.method_5799() && !mc.field_1724.method_5771()) {
                  this.currentMode.onTick();
               }
            }
         }
      }
   }

   @EventHandler
   private void onPacketReceive(PacketEvent.Receive event) {
      if (event.packet instanceof PlayerPositionLookS2CPacket) {
         this.currentMode.onRubberband();
      }
   }

   private void onSpeedModeChanged(SpeedModes mode) {
      switch (mode) {
         case Vanilla:
            this.currentMode = new Vanilla();
            break;
         case Strafe:
            this.currentMode = new Strafe();
      }
   }

   @Override
   public String getInfoString() {
      return this.currentMode.getHudString();
   }
}
