package meteordevelopment.meteorclient.systems.modules.movement.speed;

public final class SpeedModes$ConstantPool {
   public static String const_DIo9QRkoBrwqFx6 = VpbB8UnOw1(91jBLZU6gW("Ѫэіьёѕѝѱіюёыёњєѝѹііїьљьёїіы"));
   public static String const_uFQy4C9buTQAnHL = oHoQ9wdyOr(91jBLZU6gW("sfazgpJ%"));
   public static int const_yHGOZ1IWeT16Jvs = (int)((int)-1904367226L ^ 1668531060 ^ (int)(-1574332465 ^ 1328021384));
   public static String const_a9GEeTvG2mQaw32 = rwZUQlDTEd(91jBLZU6gW("ߧ߳"));
   public static String const_LLONi2FoDSClBrN = 8q0DsBCiyU(91jBLZU6gW("ľĵĢĵźĸĵĺĳźěĶľıķĠ"));
   public static String const_De3Vd2kn4TOva8j = rgSo2OYQbD(91jBLZU6gW("\b\u0003\u0014\u0003L\u000e\u0003\f\u0005L \r\r\u000e\u0007\u0003\f"));
   public static String const_Bv0jgpy19yo1NOR = YEqlVvNWWY(91jBLZU6gW("ܒܥܳܥܴݠܡܬܬݠܥܬܥܭܥܮܴܳݮ"));
   public static double const_12QBvqOWfOKSFNO = Double.longBitsToDouble(
      1035826053435664664L ^ 3586484151002140019L ^ -3394235948870310979L ^ -5800778738373965866L
   );
   public static String const_vST9AeFfEzs4fdk = lvwNM7Na9d(91jBLZU6gW("؏؇؎؍،"));
   public static float const_nDVVBMFQ0V3TsO4 = Float.intBitsToFloat((int)(-1924730736 ^ 2013085166) ^ (int)1589143698L ^ -1701469707);
   public static String const_un2JiUgWBggHvyl = lcGglHeW4j(91jBLZU6gW("鎽篠婖鳻"));
   public static int const_LwhTW1iO1dAL4Fp = (int)(6696411827102562551L ^ 6696411827039089277L) ^ (int)(-548259128 ^ -484592604);
}
