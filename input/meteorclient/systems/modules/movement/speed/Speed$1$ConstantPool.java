package meteordevelopment.meteorclient.systems.modules.movement.speed;

public final class Speed$1$ConstantPool {
   public static String const_N16DTvVNAeDyOh3 = B26qDsDqyi(YXJgvxjyMa("ޤ߿ޯ"));
   public static String const_7dWYCpiBtMBASBG = 54sD1CLX46(YXJgvxjyMa("˿˨ˎ˅˒˅ʊˈ˅ˊ˃ʊ˫ˆˎˁˇːʟ"));
   public static String const_W6vhItAajaLWy66 = GqyuTblvmV(YXJgvxjyMa("ʯʽʡ˦ʦʪʼ"));
   public static String const_BcOTPNvjYVnneub = sI37PrCTS7(YXJgvxjyMa("ļĊăĊČěŏĿĎČĄĊěĜ"));
   public static String const_vbNFG8hbYiwCFeF = JziKSK5d9S(YXJgvxjyMa("ϩϹϩϹϩϹϫϬϬ"));
   public static double const_DAVDCJlua278ib4 = Double.longBitsToDouble(
      9085711258762163322L ^ 6672082865429962641L ^ -4154999109170994943L ^ -6568337227994559766L
   );
   public static String const_Qzas20t82G1kCxI = uns4j0KooC(YXJgvxjyMa("ǭǑǜƙǚǖǕǖǋƙǎǑǜǗƙǘƙǉǕǘǀǜǋƙǐǊƙǎǐǍǑǐǗƙƈƉƙǛǕǖǚǒǊƙǖǟƙǀǖǌƗ"));
   public static String const_Zl2mjcACxeVtpOz = teVABRbBJD(YXJgvxjyMa("插闵瀄艪"));
   public static int const_nHAeBzV94oZNByl = (int)((int)(-1353591097 ^ -96706496) ^ ((int)332463817L ^ 1186810386));
}
