package meteordevelopment.meteorclient.systems.modules.movement.speed;

import io.netty.handler.codec.socksx.v5.DefaultSocks5CommandResponse$ConstantPool;
import javassist.bytecode.InnerClassesAttribute$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent;
import meteordevelopment.meteorclient.gui.utils.CharFilter$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.player.PotionSpoof$ConstantPool;
import meteordevelopment.orbit.EventPriority$ConstantPool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;

public class SpeedMode {
   protected final MinecraftClient mc;
   protected final Speed settings = Modules.get().get(Speed.class);
   private final SpeedModes type;
   protected int stage;
   protected double distance;
   protected double speed;

   public SpeedMode(SpeedModes type) {
      this.mc = MinecraftClient.method_1551();
      this.type = type;
      this.reset();
   }

   public void onTick() {
   }

   public void onMove(PlayerMoveEvent event) {
   }

   public void onRubberband() {
      this.reset();
   }

   public void onActivate() {
   }

   public void onDeactivate() {
   }

   protected double getDefaultSpeed() {
      double defaultSpeed = PotionSpoof$ConstantPool.const_jesMaaTBdFw6DJi;
      if (this.mc.field_1724.method_6059(StatusEffects.field_5904)) {
         int amplifier = this.mc.field_1724.method_6112(StatusEffects.field_5904).method_5578();
         defaultSpeed *= 1.0 + CharFilter$ConstantPool.const_oVQKkynBy76Tvuj * (amplifier + 1);
      }

      if (this.mc.field_1724.method_6059(StatusEffects.field_5909)) {
         int amplifier = this.mc.field_1724.method_6112(StatusEffects.field_5909).method_5578();
         defaultSpeed /= 1.0 + DefaultSocks5CommandResponse$ConstantPool.const_bmDCBFy0hkMCjUA * (amplifier + 1);
      }

      return defaultSpeed;
   }

   protected void reset() {
      this.stage = 0;
      this.distance = 0.0;
      this.speed = EventPriority$ConstantPool.const_ZPATova0gEKBwO1;
   }

   protected double getHop(double height) {
      StatusEffectInstance jumpBoost = this.mc.field_1724.method_6059(StatusEffects.field_5913)
         ? this.mc.field_1724.method_6112(StatusEffects.field_5913)
         : null;
      if (jumpBoost != null) {
         height += (jumpBoost.method_5578() + 1) * InnerClassesAttribute$ConstantPool.const_21Mh9JyAuoTGJkX;
      }

      return height;
   }

   public String getHudString() {
      return this.type.name();
   }
}
