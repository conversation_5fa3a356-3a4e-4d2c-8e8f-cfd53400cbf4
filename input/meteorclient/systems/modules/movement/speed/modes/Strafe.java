package meteordevelopment.meteorclient.systems.modules.movement.speed.modes;

import io.netty.handler.proxy.ProxyHandler$1$ConstantPool;
import javassist.compiler.AccessorMaker$ConstantPool;
import javassist.expr.NewArray$ProceedForArray$ConstantPool;
import meteordevelopment.discordipc.DiscordIPC$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.WaypointCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.DropItemsEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent;
import meteordevelopment.meteorclient.gui.widgets.containers.WSection$WHeader$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WWindow$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.ILivingEntityRenderer$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.settings.ProvidedStringSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.movement.Anchor;
import meteordevelopment.meteorclient.systems.modules.movement.speed.SpeedMode;
import meteordevelopment.meteorclient.systems.modules.movement.speed.SpeedModes;
import meteordevelopment.meteorclient.systems.proxies.Proxy$Builder$ConstantPool;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.orbit.listeners.IListener$ConstantPool;
import org.joml.Vector2d;
import org.reflections.vfs.UrlTypeVFS$ConstantPool;

public class Strafe extends SpeedMode {
   private long timer = 0L;

   public Strafe() {
      super(SpeedModes.Strafe);
   }

   @Override
   public void onMove(PlayerMoveEvent event) {
      switch (this.stage) {
         case 0:
            if (PlayerUtils.isMoving()) {
               this.stage++;
               this.speed = AccessorMaker$ConstantPool.const_JPDa0egaJrYB84q * this.getDefaultSpeed() - WWindow$ConstantPool.const_2YgYCNeVSW9bQ8O;
            }
         case 1:
            if (PlayerUtils.isMoving() && this.mc.field_1724.method_24828()) {
               ((IVec3d)event.movement).setY(this.getHop(NewArray$ProceedForArray$ConstantPool.const_LJJitS3nYq7PDqD));
               this.speed = this.speed * this.settings.ncpSpeed.get();
               this.stage++;
            }
            break;
         case 2:
            this.speed = this.distance - ILivingEntityRenderer$ConstantPool.const_vH2GqTAu7NQU6tQ * (this.distance - this.getDefaultSpeed());
            this.stage++;
            break;
         case 3:
            if (!this.mc.field_1687.method_18026(this.mc.field_1724.method_5829().method_989(0.0, this.mc.field_1724.method_18798().field_1351, 0.0))
               || this.mc.field_1724.field_5992 && this.stage > 0) {
               this.stage = 0;
            }

            this.speed = this.distance - this.distance / WaypointCommand$ConstantPool.const_7Zi4w9w7Oaen8Wj;
      }

      this.speed = Math.max(this.speed, this.getDefaultSpeed());
      if (this.settings.ncpSpeedLimit.get()) {
         if (System.currentTimeMillis() - this.timer > ProvidedStringSetting$ConstantPool.const_5WvAyBI8D6o0pWD) {
            this.timer = System.currentTimeMillis();
         }

         this.speed = Math.min(
            this.speed,
            System.currentTimeMillis() - this.timer > ProxyHandler$1$ConstantPool.const_8u7JOwtKIQ7kVrk
               ? Proxy$Builder$ConstantPool.const_7iOvYOjLyapCiC7
               : UrlTypeVFS$ConstantPool.const_egJS9vemehKJd1j
         );
      }

      Vector2d change = this.transformStrafe(this.speed);
      double velX = change.x;
      double velZ = change.y;
      Anchor anchor = Modules.get().get(Anchor.class);
      if (anchor.isActive() && anchor.controlMovement) {
         velX = anchor.deltaX;
         velZ = anchor.deltaZ;
      }

      ((IVec3d)event.movement).setXZ(velX, velZ);
   }

   private Vector2d transformStrafe(double speed) {
      float forward = this.mc.field_1724.field_3913.field_3905;
      float side = this.mc.field_1724.field_3913.field_3907;
      float yaw = this.mc.field_1724.field_5982
         + (this.mc.field_1724.method_36454() - this.mc.field_1724.field_5982) * this.mc.method_60646().method_60637(true);
      if (forward == 0.0F && side == 0.0F) {
         return new Vector2d(0.0, 0.0);
      } else {
         if (forward != 0.0F) {
            if (side >= 1.0F) {
               yaw += forward > 0.0F ? -45 : 45;
               side = 0.0F;
            } else if (side <= IListener$ConstantPool.const_YFAJbA27nSfqA0T) {
               yaw += forward > 0.0F ? 45 : -45;
               side = 0.0F;
            }

            if (forward > 0.0F) {
               forward = 1.0F;
            } else if (forward < 0.0F) {
               forward = WSection$WHeader$ConstantPool.const_0QbPJkDaCTEDoJI;
            }
         }

         double mx = Math.cos(Math.toRadians(yaw + DiscordIPC$ConstantPool.const_9NUDlcIFVQTBy5O));
         double mz = Math.sin(Math.toRadians(yaw + DropItemsEvent$ConstantPool.const_G9V4Fdii6gkQ6jr));
         double velX = forward * speed * mx + side * speed * mz;
         double velZ = forward * speed * mz - side * speed * mx;
         return new Vector2d(velX, velZ);
      }
   }

   @Override
   public void onTick() {
      this.distance = Math.sqrt(
         (this.mc.field_1724.method_23317() - this.mc.field_1724.field_6014) * (this.mc.field_1724.method_23317() - this.mc.field_1724.field_6014)
            + (this.mc.field_1724.method_23321() - this.mc.field_1724.field_5969) * (this.mc.field_1724.method_23321() - this.mc.field_1724.field_5969)
      );
   }
}
