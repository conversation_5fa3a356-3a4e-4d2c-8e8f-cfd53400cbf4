package meteordevelopment.meteorclient.systems.modules.movement.speed.modes;

public final class Vanilla$ConstantPool {
   public static String const_fvmvbZS7eLG8FbL = 2pMtHFbADC(fB9sWmxMFa("ͩͥͯ͊ͯͯ\u0379͟Ͳͻͮ"));
   public static int const_wvqYOaKuj9iNil8 = (int)(-5343333180591138667L ^ -5343333180301046480L) ^ (int)(186269117 ^ 449686192);
   public static String const_kf0KyDlLlZKVN7w = bmDfeS4l7v(fB9sWmxMFa("PYDxW[S"));
   public static int const_TXCqaTPOIDQzdZd = (int)((int)-129899908L ^ -1108098633 ^ (int)(1901601186 ^ 887752308));
   public static int const_Khy9L6Ju7DAOuDt = (int)((int)(1620544228 ^ 926561710) ^ (int)(862647937 ^ 1690782047));
   public static String const_3d7iAAW9fEPSaOA = JqZjYlMzh7(fB9sWmxMFa("շզճկՄղճըաաՁզդճըյ"));
   public static String const_eIj5G5ELugSz7Ba = dQLYOMSN2y(fB9sWmxMFa("\u0604ظصٰشصؼرةٰعؾٰؤعسػأٰزصؤاصصؾٰأؼؿؤٰؽؿئصؽصؾؤأپ"));
   public static double const_0s854AT2vD9loVU = Double.longBitsToDouble(
      -2330187815224068327L ^ -6515837499739969581L ^ 6394506163632785335L ^ 7120687638831732605L
   );
   public static String const_yWAkiTFqCwrhE9E = pegApAywlv(fB9sWmxMFa("իՍ՛ՍԞՍՎ՛՛՚ԞՑՐՒՇԞՉՖ՛ՐԞՍՊ՟Ր՚\u0557ՐՙԞՑՐԞ՟Ԟ՜ՒՑ՝ՕԐ"));
   public static String const_Dqo2A3DYpgYB0rS = HA8l60k3dn(fB9sWmxMFa("掿褳彫"));
   public static String const_mL3YX9rAliy5JxD = 9oEgAjvVvb(
      fB9sWmxMFa(
         "\u05fc׃\u05ceן׃\u05ceי\u058bןׄ\u058bט׃ׄל\u058b׃ׇׄ\u05ceט\u058bן׃\u05caן\u058b׃\u05caם\u05ce\u058bל\u05ce\u05c9ט\u058bׂׅטׂ\u05cf\u05ce\u058bׄ\u05cd\u058bן׃\u05ce׆օ"
      )
   );
   public static String const_miWxjvkKNTaTGej = dIsGg2ObkB(fB9sWmxMFa("ڟ"));
   public static String const_JKYxRN7d7VAzblq = 4FR6lkOK0v(fB9sWmxMFa("ԣԟԒԊԖԁԺԝԇԖԁԒԐԇԶԝԇԚԇԊ\u0530ՁԠԣԒԐԘԖԇ"));
   public static String const_KYDGi9UmTkg7TMK = 4yqNqDdOXD(fB9sWmxMFa("֏ֲָ֮֨֯־ָ֍ּ־ֶ֎ֳָֹ֎ׯ֞֍ּ־ֶָ֩"));
}
