package meteordevelopment.meteorclient.systems.modules.movement.speed.modes;

import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.settings.StatusEffectAmplifierMapSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.movement.Anchor;
import meteordevelopment.meteorclient.systems.modules.movement.speed.SpeedMode;
import meteordevelopment.meteorclient.systems.modules.movement.speed.SpeedModes;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.util.math.Vec3d;

public class Vanilla extends SpeedMode {
   public Vanilla() {
      super(SpeedModes.Vanilla);
   }

   @Override
   public void onMove(PlayerMoveEvent event) {
      Vec3d vel = PlayerUtils.getHorizontalVelocity(this.settings.vanillaSpeed.get());
      double velX = vel.method_10216();
      double velZ = vel.method_10215();
      if (this.mc.field_1724.method_6059(StatusEffects.field_5904)) {
         double value = (this.mc.field_1724.method_6112(StatusEffects.field_5904).method_5578() + 1)
            * StatusEffectAmplifierMapSetting$ConstantPool.const_BWL1wJvBqbraFeh;
         velX += velX * value;
         velZ += velZ * value;
      }

      Anchor anchor = Modules.get().get(Anchor.class);
      if (anchor.isActive() && anchor.controlMovement) {
         velX = anchor.deltaX;
         velZ = anchor.deltaZ;
      }

      ((IVec3d)event.movement).set(velX, event.movement.field_1351, velZ);
   }
}
