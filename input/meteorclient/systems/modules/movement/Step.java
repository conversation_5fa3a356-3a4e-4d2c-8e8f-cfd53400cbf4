package meteordevelopment.meteorclient.systems.modules.movement;

import com.google.common.collect.Streams;
import java.util.OptionalDouble;
import javassist.URLClassPath$ConstantPool;
import javassist.expr.Instanceof$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.screens.settings.SoundEventListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorTriangle$ConstantPool;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.misc.HotBarSwap$ChangeMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.HotBarSwap$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.Notebot$PlayingMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.ServerSpoof$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.DamageUtils;
import meteordevelopment.meteorclient.utils.misc.IChangeable$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralPacketBuilder$ConstantPool;
import meteordevelopment.meteorclient.utils.render.ByteTexture$Filter$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.decoration.EndCrystalEntity;

public class Step extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   public final Setting<Double> height = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(HotBarSwap$ConstantPool.const_S7EBKbjwNlQgVX2))))
            .description(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(HotBarSwap$ChangeMode$ConstantPool.const_SxQCW9HhnPD4yJ6))))
            .defaultValue(1.0)
            .min(0.0)
            .build()
      );
   private final Setting<Step.ActiveWhen> activeWhen = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(URLClassPath$ConstantPool.const_1ISWTDiJxF9VcM4)))))
                  .description(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(Notebot$PlayingMode$ConstantPool.const_hFueNQA0DwoJ1Ag)))))
               .defaultValue(Step.ActiveWhen.Always))
            .build()
      );
   private final Setting<Boolean> safeStep = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(Instanceof$ConstantPool.const_1WCkyjovh2aoIOM))))
            .description(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(SoundEventListSettingScreen$ConstantPool.const_CT9YrFnk8jL2N8i))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Integer> stepHealth = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(ServerSpoof$ConstantPool.const_AjhcitOvJTB77pw))))
            .description(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(NoSlow$WebMode$ConstantPool.const_EnVDTtnSNE9yioM))))
            .defaultValue(5)
            .range(1, 36)
            .sliderRange(1, 36)
            .visible(this.safeStep::get)
            .build()
      );
   private float prevStepHeight;
   private boolean prevPathManagerStep;

   public Step() {
      super(
         Categories.Movement,
         saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(IChangeable$ConstantPool.const_H0QnNQ1ZtPwFme4))),
         new StringBuilder(saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(WMeteorTriangle$ConstantPool.const_yIzQF7QDXSJbQNN)))),
         saZ7y9YLlv(IBoza6QeW1(G4Q1FVxOLS(ByteTexture$Filter$ConstantPool.const_ljDkeJr4Bx19lOi)))
      );
   }

   @Override
   public void onActivate() {
      this.prevStepHeight = mc.field_1724.method_49476();
      this.prevPathManagerStep = PathManagers.get().getSettings().getStep().get();
      PathManagers.get().getSettings().getStep().set(true);
   }

   @EventHandler
   private void onTick(TickEvent.Post event) {
      boolean work = this.activeWhen.get() == Step.ActiveWhen.Always
         || this.activeWhen.get() == Step.ActiveWhen.Sneaking && mc.field_1724.method_5715()
         || this.activeWhen.get() == Step.ActiveWhen.NotSneaking && !mc.field_1724.method_5715();
      mc.field_1724.method_5857(mc.field_1724.method_5829().method_989(0.0, 1.0, 0.0));
      if (!work
         || this.safeStep.get()
            && (!(this.getHealth() > this.stepHealth.get().intValue()) || !(this.getHealth() - this.getExplosionDamage() > this.stepHealth.get().intValue()))) {
         mc.field_1724.method_5996(EntityAttributes.field_47761).method_6192(this.prevStepHeight);
      } else {
         mc.field_1724.method_5996(EntityAttributes.field_47761).method_6192(this.height.get());
      }

      mc.field_1724.method_5857(mc.field_1724.method_5829().method_989(0.0, AstralPacketBuilder$ConstantPool.const_RtVijDKNVHVDdCM, 0.0));
   }

   @Override
   public void onDeactivate() {
      mc.field_1724.method_5996(EntityAttributes.field_47761).method_6192(this.prevStepHeight);
      PathManagers.get().getSettings().getStep().set(this.prevPathManagerStep);
   }

   private float getHealth() {
      return mc.field_1724.method_6032() + mc.field_1724.method_6067();
   }

   private double getExplosionDamage() {
      OptionalDouble crystalDamage = Streams.stream(mc.field_1687.method_18112())
         .filter(entity -> entity instanceof EndCrystalEntity)
         .filter(Entity::method_5805)
         .mapToDouble(entity -> DamageUtils.crystalDamage(mc.field_1724, entity.method_19538()))
         .max();
      return crystalDamage.orElse(0.0);
   }

   public static enum ActiveWhen {
      Always,
      Sneaking,
      NotSneaking;
   }
}
