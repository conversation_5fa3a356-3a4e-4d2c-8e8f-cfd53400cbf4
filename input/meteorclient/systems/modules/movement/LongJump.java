package meteordevelopment.meteorclient.systems.modules.movement;

import de.florianmichael.waybackauthlib.WaybackAuthLib$Response$ConstantPool;
import io.netty.handler.codec.socks.SocksCommonUtils$ConstantPool;
import io.netty.handler.codec.socks.SocksMessageType$ConstantPool;
import io.netty.handler.codec.socksx.SocksVersion$ConstantPool;
import io.netty.handler.codec.socksx.v5.DefaultSocks5PasswordAuthRequest$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5AddressEncoder$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthRequest$ConstantPool;
import javassist.CtConstructor$ConstantPool;
import javassist.CtField$DoubleInitializer$ConstantPool;
import javassist.CtField$NewInitializer$ConstantPool;
import javassist.CtField$ParamInitializer$ConstantPool;
import javassist.CtField$PtreeInitializer$ConstantPool;
import javassist.CtMethod$StringConstParameter$ConstantPool;
import javassist.CtNewMethod$ConstantPool;
import javassist.CtPrimitiveType$ConstantPool;
import javassist.NotFoundException$ConstantPool;
import javassist.SerialVersionUID$2$ConstantPool;
import javassist.bytecode.AttributeInfo$ConstantPool;
import javassist.bytecode.Bytecode$ConstantPool;
import javassist.bytecode.ClassFile$ConstantPool;
import javassist.bytecode.CodeIterator$AlignmentException$ConstantPool;
import javassist.bytecode.CodeIterator$Jump32$ConstantPool;
import javassist.bytecode.Descriptor$PrettyPrinter$ConstantPool;
import javassist.bytecode.FieldInfo$ConstantPool;
import javassist.bytecode.MethodrefInfo$ConstantPool;
import javassist.bytecode.StackMap$SwitchShifter$ConstantPool;
import javassist.bytecode.TypeAnnotationsAttribute$Copier$ConstantPool;
import javassist.bytecode.Utf8Info$ConstantPool;
import javassist.bytecode.analysis.IntQueue$Entry$ConstantPool;
import javassist.bytecode.annotation.FloatMemberValue$ConstantPool;
import javassist.compiler.CodeGen$ReturnHook$ConstantPool;
import javassist.compiler.MemberCodeGen$JsrHook2$ConstantPool;
import javassist.compiler.NoFieldException$ConstantPool;
import javassist.compiler.ast.InstanceOfExpr$ConstantPool;
import javassist.convert.TransformCall$ConstantPool;
import javassist.convert.Transformer$ConstantPool;
import javassist.runtime.DotClass$ConstantPool;
import javassist.scopedpool.SoftValueHashMap$SoftValueRef$ConstantPool;
import javassist.tools.reflect.Loader$ConstantPool;
import javassist.tools.rmi.ObjectNotFoundException$ConstantPool;
import javassist.util.proxy.DefinePackageHelper$Java7$ConstantPool;
import javassist.util.proxy.ProxyFactory$1$ConstantPool;
import javax.annotation.RegEx$ConstantPool;
import javax.annotation.meta.When$ConstantPool;
import meteordevelopment.discordipc.DiscordIPC$ConstantPool;
import meteordevelopment.meteorclient.Main$OperatingSystem$ConstantPool;
import meteordevelopment.meteorclient.commands.Command$ConstantPool;
import meteordevelopment.meteorclient.commands.arguments.SettingArgumentType$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.FovCommand$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.ResetCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.JumpVelocityMultiplierEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent;
import meteordevelopment.meteorclient.events.entity.player.PlayerMoveEvent$ConstantPool;
import meteordevelopment.meteorclient.events.game.ResolutionChangedEvent$ConstantPool;
import meteordevelopment.meteorclient.events.meteor.MouseButtonEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.packets.PacketEvent$Receive$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketReceiveAlienEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.RenderBlockEntityEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.BlockUpdateEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.ChunkDataEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.WidgetScreen$WFullScreenRoot$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.CommitsScreen$Response$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.ItemSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WTooltip$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.input.WIntEdit$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.pressable.WFavorite$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.renderer.Renderer3D$ConstantPool;
import meteordevelopment.meteorclient.renderer.Texture$Format$ConstantPool;
import meteordevelopment.meteorclient.renderer.text.TextRenderer$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EntityTypeListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.KeybindSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.Setting$SettingBuilder$ConstantPool;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.SoundEventListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.AllMod$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.TexturesJson$ConstantPool;
import meteordevelopment.meteorclient.systems.friends.Friend$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.HudElementInfo$Preset$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.CompassHud$Direction$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.MeteorTextHud$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Module$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.AntiAnchor$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoCity$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoWeapon$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AttackSwitchHammer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.GrimPacketMine$SwapMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.TridentExp$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.AutoLog$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.BookBot$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.NameProtect$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.Notebot$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.PacketCanceller$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.ServerSpoof$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.speed.SpeedModes$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.speed.modes.Vanilla$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoClicker$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.Rotation$LockMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.SpeedMine$ListMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.BetterTab$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ESP$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Freecam$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.HoleESP$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ItemPhysics$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ItemPhysics$ModelInfo$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.PopChams$GhostPlayer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.blockesp.ESPGroup$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.Sphere2dMarker$Block$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Excavator$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$RestockTask$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$StraightBlockPosProvider$4$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Nuker$Shape$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.PacketMine$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Timer;
import meteordevelopment.meteorclient.systems.modules.world.VeinMiner$ConstantPool;
import meteordevelopment.meteorclient.systems.proxies.Proxies$ConstantPool;
import meteordevelopment.meteorclient.systems.waypoints.Waypoints$WaypointIterator$ConstantPool;
import meteordevelopment.meteorclient.utils.PreInit$ConstantPool;
import meteordevelopment.meteorclient.utils.ReflectInit$ConstantPool;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.entity.DamageUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.ProjectileEntitySimulator$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.SortPriority$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.ByteCountDataOutput$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.ISerializable$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.text.MeteorClickEvent$ConstantPool;
import meteordevelopment.meteorclient.utils.network.OnlinePlayers$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralPacket$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralPacketBuilder$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralPayloadType$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.song.Note$ConstantPool;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.TitleScreenCredits$ConstantPool;
import meteordevelopment.meteorclient.utils.render.MeshVertexConsumerProvider$ConstantPool;
import meteordevelopment.meteorclient.utils.render.MeteorToast$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.RainbowColors$ConstantPool;
import meteordevelopment.meteorclient.utils.render.postprocess.PostProcessShaders$ConstantPool;
import meteordevelopment.meteorclient.utils.render.prompts.OkPrompt$ConstantPool;
import meteordevelopment.meteorclient.utils.tooltip.MeteorTooltipData$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockUtilGrim$1$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockUtils$MobSpawn$ConstantPool;
import meteordevelopment.meteorclient.utils.world.Dimension$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.compiler.Expr$Binary$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Bool$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Group$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$String$ConstantPool;
import meteordevelopment.starscript.value.Value$VString$ConstantPool;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.network.packet.s2c.play.PlayerPositionLookS2CPacket;
import org.reflections.Reflections$ConstantPool;
import org.reflections.vfs.JarInputDir$ConstantPool;
import org.reflections.vfs.Vfs$DefaultUrlTypes$3$ConstantPool;
import org.reflections.vfs.Vfs$File$ConstantPool;

public class LongJump extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   public final Setting<LongJump.JumpMode> jumpMode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(PacketMine$ConstantPool.const_kWL7Opq10bVW6dr)))))
                  .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(DefinePackageHelper$Java7$ConstantPool.const_ABl0fl2iVSEOZVA)))))
               .defaultValue(LongJump.JumpMode.Vanilla))
            .build()
      );
   private final Setting<Double> vanillaBoostFactor = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(AntiAnchor$ConstantPool.const_CCWDlqrYLbVFqrz))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(TypeAnnotationsAttribute$Copier$ConstantPool.const_aB2OOlogW1nVta6))))
            .visible(() -> this.jumpMode.get() == LongJump.JumpMode.Vanilla)
            .defaultValue(TexturesJson$ConstantPool.const_4n7vjoOiSbq2Ji8)
            .min(0.0)
            .sliderMax(SocksVersion$ConstantPool.const_IkIPSyszmhaNgnW)
            .build()
      );
   private final Setting<Double> burstInitialSpeed = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(BlockUtils$MobSpawn$ConstantPool.const_Vb9Ld4EVM10aAbo))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(Dimension$ConstantPool.const_NO7TN1B9j8T2ZMN))))
            .visible(() -> this.jumpMode.get() == LongJump.JumpMode.Burst)
            .defaultValue(TextRenderer$ConstantPool.const_rOQG4Oin4nSzSAJ)
            .min(0.0)
            .sliderMax(CodeGen$ReturnHook$ConstantPool.const_r4SODe5GOEsiAS9)
            .build()
      );
   private final Setting<Double> burstBoostFactor = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(IntQueue$Entry$ConstantPool.const_nIRTR8YM10QI7Ry))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(MeteorGuiTheme$ConstantPool.const_ZPS4cYejCbVmood))))
            .visible(() -> this.jumpMode.get() == LongJump.JumpMode.Burst)
            .defaultValue(GrimPacketMine$SwapMode$ConstantPool.const_BoL6ALwendoQ604)
            .min(0.0)
            .sliderMax(Expr$Bool$ConstantPool.const_Wax7dpwIdyBOv4M)
            .build()
      );
   private final Setting<Boolean> onlyOnGround = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(AstralPacket$ConstantPool.const_gOO4xeBEu6DY8h3))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(MeteorTextHud$ConstantPool.const_iBKWsbHLVOYIcWF))))
            .visible(() -> this.jumpMode.get() == LongJump.JumpMode.Burst)
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> onJump = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(HighwayBuilder$StraightBlockPosProvider$4$ConstantPool.const_vnl4IYiLbKlNwR8))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(SerialVersionUID$2$ConstantPool.const_AwXyWDsLbLpJqYI))))
            .visible(() -> this.jumpMode.get() == LongJump.JumpMode.Burst)
            .defaultValue(false)
            .build()
      );
   private final Setting<Double> glideMultiplier = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(VeinMiner$ConstantPool.const_mGSj5PzCV45h6DP))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(CompassHud$Direction$ConstantPool.const_DInggVecGCGwDph))))
            .visible(() -> this.jumpMode.get() == LongJump.JumpMode.Glide)
            .defaultValue(1.0)
            .min(0.0)
            .sliderMax(SocksVersion$ConstantPool.const_eUmcdlUAYTYfPb1)
            .build()
      );
   public final Setting<Double> timer = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(Expr$Binary$ConstantPool.const_KrAPaDeOQq2lJvQ))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(FovCommand$ConstantPool.const_FlGLniwerYQBI6y))))
            .defaultValue(1.0)
            .min(ESPGroup$ConstantPool.const_Bl3iwqGzUmXoAdr)
            .sliderMin(JarInputDir$ConstantPool.const_d0RqbAbdeozoSVk)
            .build()
      );
   private final Setting<Boolean> autoDisable = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(PacketEvent$Receive$ConstantPool.const_NB1fJKqc6ScMwDa))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(PacketCanceller$ConstantPool.const_98jXQvUIvl9gbBQ))))
            .visible(() -> this.jumpMode.get() != LongJump.JumpMode.Vanilla)
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> disableOnRubberband = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(KeybindSetting$Builder$ConstantPool.const_JZEi4b6o266S3tv))))
            .description(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(Command$ConstantPool.const_wqc0A462FGr2cmb))))
            .defaultValue(true)
            .build()
      );
   private int stage;
   private double moveSpeed;
   private boolean jumping = false;
   private int airTicks;
   private int groundTicks;
   private boolean jumped = false;

   public LongJump() {
      super(
         Categories.Movement,
         Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(CommitsScreen$Response$ConstantPool.const_rNg4fnNWetrFDR5))),
         new StringBuilder(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(DiscordIPC$ConstantPool.const_eskW1uwUGaF04nn)))),
         Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(CtConstructor$ConstantPool.const_rAmcUH6oeB5De0W)))
      );
   }

   @Override
   public void onActivate() {
      this.stage = 0;
      this.jumping = false;
      this.airTicks = 0;
      this.groundTicks = -5;
   }

   @Override
   public void onDeactivate() {
      Modules.get().get(Timer.class).setOverride(1.0);
   }

   @EventHandler
   private void onPacketReceive(PacketEvent.Receive event) {
      if (event.packet instanceof PlayerPositionLookS2CPacket && this.disableOnRubberband.get()) {
         this.info(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(SpeedMine$ListMode$ConstantPool.const_IJIZjyrICsbhOoa))));
         this.toggle();
      }
   }

   @EventHandler
   private void onPlayerMove(PlayerMoveEvent event) {
      if (this.timer.get() != 1.0) {
         Modules.get().get(Timer.class).setOverride(PlayerUtils.isMoving() ? this.timer.get() : 1.0);
      }

      switch ((LongJump.JumpMode)this.jumpMode.get()) {
         case Vanilla:
            if (PlayerUtils.isMoving() && mc.field_1690.field_1903.method_1434()) {
               double dir = this.getDir();
               double xDir = Math.cos(Math.toRadians(dir + Vanilla$ConstantPool.const_0s854AT2vD9loVU));
               double zDir = Math.sin(Math.toRadians(dir + ProjectileEntitySimulator$ConstantPool.const_AxqWzrOQ1ASKmrI));
               if (!mc.field_1687.method_18026(mc.field_1724.method_5829().method_989(0.0, mc.field_1724.method_18798().field_1351, 0.0))
                  || mc.field_1724.field_5992) {
                  ((IVec3d)event.movement)
                     .setXZ(xDir * Excavator$ConstantPool.const_JDnfjii7TCDWNG8, zDir * TitleScreenCredits$ConstantPool.const_YHWt1QndU8vhYF4);
               }

               if (event.movement.method_10214() == SocksMessageType$ConstantPool.const_QeVrrD6xH2DBqDe) {
                  ((IVec3d)event.movement).setXZ(xDir * this.vanillaBoostFactor.get(), zDir * this.vanillaBoostFactor.get());
               }
            }
            break;
         case Burst:
            if (this.stage != 0 && !mc.field_1724.method_24828() && this.autoDisable.get()) {
               this.jumping = true;
            }

            if (this.jumping && mc.field_1724.method_23318() - (int)mc.field_1724.method_23318() < ByteCountDataOutput$ConstantPool.const_Nt5v3Kos777eDtp) {
               this.jumping = false;
               this.toggle();
               this.info(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(Module$ConstantPool.const_B7a2OnVb23bJD9d))));
            }

            if (this.onlyOnGround.get() && !mc.field_1724.method_24828() && this.stage == 0) {
               return;
            }

            double xDist = mc.field_1724.method_23317() - mc.field_1724.field_6014;
            double zDist = mc.field_1724.method_23321() - mc.field_1724.field_5969;
            double lastDist = Math.sqrt(xDist * xDist + zDist * zDist);
            if (PlayerUtils.isMoving()
               && (!this.onJump.get() || mc.field_1690.field_1903.method_1434())
               && !mc.field_1724.method_5771()
               && !mc.field_1724.method_5799()) {
               if (this.stage == 0) {
                  this.moveSpeed = this.getMoveSpeed() * this.burstInitialSpeed.get();
               } else if (this.stage == 1) {
                  ((IVec3d)event.movement).setY(ItemPhysics$ConstantPool.const_odVO6ob2Vu9gTgQ);
                  this.moveSpeed = this.moveSpeed * this.burstBoostFactor.get();
               } else if (this.stage == 2) {
                  double difference = lastDist - this.getMoveSpeed();
                  this.moveSpeed = lastDist - difference;
               } else {
                  this.moveSpeed = lastDist - lastDist / BlockUpdateEvent$ConstantPool.const_lZyLifhy4XbBedB;
               }

               this.setMoveSpeed(event, this.moveSpeed = Math.max(this.getMoveSpeed(), this.moveSpeed));
               if (!mc.field_1724.field_5992
                  && !mc.field_1687.method_18026(mc.field_1724.method_5829().method_989(0.0, mc.field_1724.method_18798().field_1351, 0.0))
                  && !mc.field_1687.method_18026(mc.field_1724.method_5829().method_989(0.0, Vfs$File$ConstantPool.const_yWTV2go6ngtw6HO, 0.0))) {
                  ((IVec3d)event.movement).setY(Proxies$ConstantPool.const_efLhC2T3cjvUjv3);
               }

               this.stage++;
            }
      }
   }

   @EventHandler
   private void onTick(TickEvent.Pre event) {
      if (Utils.canUpdate() && this.jumpMode.get() == LongJump.JumpMode.Glide) {
         if (!PlayerUtils.isMoving()) {
            return;
         }

         float yaw = mc.field_1724.method_36454() + Value$VString$ConstantPool.const_NB3QWgzDzavFeA8;
         double forward = mc.field_1724.field_6250 != 0.0F ? (mc.field_1724.field_6250 > 0.0F ? 1 : -1) : 0;
         float[] motion = new float[]{
            ItemPhysics$ModelInfo$ConstantPool.const_dFVGqm9nQ9EPSoD,
            Utf8Info$ConstantPool.const_9nJTl1UBwOiNYyW,
            BlockUtilGrim$1$ConstantPool.const_UoShFpOkoA2jtim,
            ResolutionChangedEvent$ConstantPool.const_vfB9JO1IJrkBrb7,
            PreInit$ConstantPool.const_JriyeRlvdZDrEVm,
            RenderBlockEntityEvent$ConstantPool.const_jkeFAriBDlNEJkQ,
            FieldInfo$ConstantPool.const_WfvhgGIW5X8xbOe,
            Transformer$ConstantPool.const_8e2wVCYGMbFDm6k,
            BetterTab$ConstantPool.const_3alJlDcYScyDQP1,
            MeshVertexConsumerProvider$ConstantPool.const_iB63PaZzvrYtLck,
            Rotation$LockMode$ConstantPool.const_7XeloSOdlXmeB2Y,
            OkPrompt$ConstantPool.const_rByfRNQpLLa6etH,
            MeteorClickEvent$ConstantPool.const_G2IgWsAI8ubmnyF,
            CodeIterator$Jump32$ConstantPool.const_CR1FmWBDYuv376l,
            Expr$Group$ConstantPool.const_ND6ghqVwNLSZT5B,
            CtField$DoubleInitializer$ConstantPool.const_e2Iybknr0yx8drO,
            ProjectileEntitySimulator$ConstantPool.const_gD7EBWhGePgAIPJ,
            CtNewMethod$ConstantPool.const_mDJyQe3gLLTJRVF,
            Vfs$DefaultUrlTypes$3$ConstantPool.const_icoUlwbOy6jTY11,
            AttackSwitchHammer$ConstantPool.const_70I4g41wI2wqaZS,
            TridentBoost$ConstantPool.const_7TkNMuTubPzNiUg,
            WIntEdit$ConstantPool.const_I3Q9SXJJ1QOT4fS,
            ProxyFactory$1$ConstantPool.const_f2Eyyg7Q21GeV7k,
            MemberCodeGen$JsrHook2$ConstantPool.const_BP1tWWjIQZLIVJy,
            NoFieldException$ConstantPool.const_L7EjVATbW8p4cAu,
            MeteorTooltipData$ConstantPool.const_6K7f7Gu4yutitVF,
            CtField$ParamInitializer$ConstantPool.const_AnNS2Uu2zlFLDbG,
            RainbowColors$ConstantPool.const_ZtWeijI7mgB60x4,
            SortPriority$ConstantPool.const_y538atNDok3Iwie,
            Socks5PasswordAuthRequest$ConstantPool.const_mwb7FN24GMh1U1y,
            ReflectInit$ConstantPool.const_re415KS8R1g6IMG,
            NameProtect$ConstantPool.const_73hgvEY0luwmtlw,
            WidgetScreen$WFullScreenRoot$ConstantPool.const_vDvCwgjOY6Brpno,
            CtField$NewInitializer$ConstantPool.const_Vdj2dO9HJQlv7Sz,
            MethodrefInfo$ConstantPool.const_oAsjEbxelZt6iVn,
            HighwayBuilder$RestockTask$ConstantPool.const_SkrvpOqLVzfRo5D,
            MeteorToast$ConstantPool.const_SGcqgoJOerFWL2B,
            ItemSettingScreen$ConstantPool.const_YBbQYgM2MOB6ILw,
            DotClass$ConstantPool.const_lOoSAdljIxfr1J4,
            CtField$PtreeInitializer$ConstantPool.const_24tGZyCq3rTQa2W,
            Expr$String$ConstantPool.const_13XO0J6vvQU1hIw,
            CtMethod$StringConstParameter$ConstantPool.const_uawyXBtySjZBFiN,
            Bytecode$ConstantPool.const_8UwABWITg1Z0Qwv,
            NotFoundException$ConstantPool.const_eBMyki5rugQyPtp,
            Step$ConstantPool.const_tmlvDYT77kuxF1J,
            Setting$SettingBuilder$ConstantPool.const_ADPcYwtlatNY6OE,
            HudElementInfo$Preset$ConstantPool.const_LVnYJAIty7WR9Y0,
            AutoCity$ConstantPool.const_kWZErpJZoAdHCd2,
            SpeedModes$ConstantPool.const_nDVVBMFQ0V3TsO4,
            Friend$ConstantPool.const_2gkMzSBPDiTxbpv,
            OnlinePlayers$ConstantPool.const_dzrA9RHAZWwAwFY,
            SoftValueHashMap$SoftValueRef$ConstantPool.const_r2eFFzeFJSoosNv,
            Socks5AddressEncoder$ConstantPool.const_NDVLvIsqDt1jYB9,
            TridentExp$Mode$ConstantPool.const_EA7EtdsxlYduzrE,
            Loader$ConstantPool.const_ALdbyuF9LNVlGYV,
            Freecam$ConstantPool.const_Y4jysQ7vdimH9qY,
            MeteorTooltipData$ConstantPool.const_LlehIykfFhVnsaB,
            InstanceOfExpr$ConstantPool.const_IiVIp9eN4h21TKg,
            Waypoints$WaypointIterator$ConstantPool.const_lLOa8PlxG5ZgAPy,
            AstralPacketBuilder$ConstantPool.const_BWpLw4VTgSQ1tIo,
            Main$OperatingSystem$ConstantPool.const_Mqb6tTB9eoWj0WO,
            Texture$Format$ConstantPool.const_kbP9Gpg8yuLqfzD,
            EntityTypeListSetting$Builder$ConstantPool.const_eiNDcSrfJ29tiO9,
            FloatMemberValue$ConstantPool.const_2PInVPFTxVLBoe6,
            ESP$Mode$ConstantPool.const_QNtKIfqb8aeGrN7,
            SoundEventListSetting$Builder$ConstantPool.const_XpuTYV5I8myuQpi,
            MouseButtonEvent$ConstantPool.const_O2Y1eBVC13GyiFF,
            ChunkDataEvent$ConstantPool.const_PvnPl8EbTSriSF2,
            AutoClicker$ConstantPool.const_RAev9F81mXvrKMa,
            WTooltip$ConstantPool.const_emaVrC7wNQFNZiy,
            CodeIterator$AlignmentException$ConstantPool.const_NqsRmJZjybv2ega,
            Notebot$ConstantPool.const_7AgblGPwqinrCy4,
            ProxyFactory$1$ConstantPool.const_RLjGtolrXJjYyJb,
            Descriptor$PrettyPrinter$ConstantPool.const_mIvGnkYCRLlVA1r,
            PacketReceiveAlienEvent$ConstantPool.const_6L2Fi22bxWe4d4F,
            ClassFile$ConstantPool.const_4WFUr4PZyaGlzY8,
            AstralPacketBuilder$ConstantPool.const_eh14TYcWwmm12Oz
         };
         float[] glide = new float[]{
            When$ConstantPool.const_TbvGcrcQoQW1AhV,
            PopChams$GhostPlayer$ConstantPool.const_g42IH1JzhyoTVBA,
            ResetCommand$ConstantPool.const_BHvA0kekJIiRJy2,
            Renderer3D$ConstantPool.const_Jg1fxgLgyrEdoo1,
            EntityControl$ConstantPool.const_ABlm6NiDIbR7N64,
            ISerializable$ConstantPool.const_adsburS58464Lep,
            SocksCommonUtils$ConstantPool.const_nrYefpGhaSIon40,
            Note$ConstantPool.const_OlCEoF6H64L7j5v,
            AstralPayloadType$ConstantPool.const_wvA6WHXvrwwSJA8
         };
         double cos = Math.cos(Math.toRadians(yaw));
         double sin = Math.sin(Math.toRadians(yaw));
         if (!mc.field_1724.field_5992 && !mc.field_1724.method_24828()) {
            this.jumped = true;
            this.airTicks++;
            this.groundTicks = -5;
            double velocityY = mc.field_1724.method_18798().field_1351;
            if (this.airTicks - 6 >= 0 && this.airTicks - 6 < glide.length) {
               this.updateY(velocityY * glide[this.airTicks - 6] * this.glideMultiplier.get());
            }

            if (velocityY < TransformCall$ConstantPool.const_VrnNFrcANSWBdAH && velocityY > AutoLog$ConstantPool.const_qtVBNgocuhTNIyS) {
               this.updateY(velocityY * NotebotUtils$ConstantPool.const_OXzLpnnmgQIv2h1 * this.glideMultiplier.get());
            } else if (velocityY < ServerSpoof$ConstantPool.const_D1qWRbmFSYDiai3 && velocityY > HoleESP$ConstantPool.const_Ain5w9Z2csewhHD) {
               this.updateY(velocityY * Reflections$ConstantPool.const_UdonP7v4sFR4icd * this.glideMultiplier.get());
            } else if (velocityY < ObjectNotFoundException$ConstantPool.const_Nclwr3bGFyHu46w
               && velocityY > PacketEvent$Receive$ConstantPool.const_rmqnLlDd4e52lSA) {
               this.updateY(velocityY * StackMap$SwitchShifter$ConstantPool.const_nlGNNYvWtoDkSia * this.glideMultiplier.get());
            }

            if (this.airTicks - 1 >= 0 && this.airTicks - 1 < motion.length) {
               mc.field_1724
                  .method_18800(
                     forward * motion[this.airTicks - 1] * NoFall$ConstantPool.const_qqKu7aSnGEekFO2 * cos * this.glideMultiplier.get(),
                     mc.field_1724.method_18798().field_1351,
                     forward * motion[this.airTicks - 1] * Sphere2dMarker$Block$ConstantPool.const_h0pvOHDiohoE5IO * sin * this.glideMultiplier.get()
                  );
            } else {
               mc.field_1724.method_18800(0.0, mc.field_1724.method_18798().field_1351, 0.0);
            }
         } else {
            if (this.autoDisable.get() && this.jumped) {
               this.jumped = false;
               this.toggle();
               this.info(Zy4LiIjvXj(wd9WvHgkkP(OeZk4I7Lwr(Nuker$Shape$ConstantPool.const_RKSSIieT2st7DwX))));
            }

            this.airTicks = 0;
            this.groundTicks++;
            if (this.groundTicks <= 2) {
               mc.field_1724
                  .method_18800(
                     forward * PlayerMoveEvent$ConstantPool.const_csWioOsvwtINiSp * cos * this.glideMultiplier.get(),
                     mc.field_1724.method_18798().field_1351,
                     forward * WFavorite$ConstantPool.const_Nod9bolDTqOb1yP * sin * this.glideMultiplier.get()
                  );
            } else {
               mc.field_1724
                  .method_18800(
                     forward * WaybackAuthLib$Response$ConstantPool.const_tR9XCmdqWIT8DTY * cos * this.glideMultiplier.get(),
                     AllMod$ConstantPool.const_0FrNPwGgmtDipG0,
                     forward * RegEx$ConstantPool.const_X2SLarWabLYbypX * sin * this.glideMultiplier.get()
                  );
            }
         }
      }
   }

   private void updateY(double amount) {
      mc.field_1724.method_18800(mc.field_1724.method_18798().field_1352, amount, mc.field_1724.method_18798().field_1350);
   }

   private double getDir() {
      double dir = 0.0;
      if (Utils.canUpdate()) {
         dir = mc.field_1724.method_36454() + (mc.field_1724.field_6250 < 0.0F ? 180 : 0);
         if (mc.field_1724.field_6212 > 0.0F) {
            dir += CtPrimitiveType$ConstantPool.const_SBXeqeyJaIatTTa
               * (
                  mc.field_1724.field_6250 < 0.0F
                     ? AutoWeapon$ConstantPool.const_J7o5IDedstLdTYQ
                     : (mc.field_1724.field_6250 > 0.0F ? Sneak$ConstantPool.const_uxrB7wG0aSIY4ry : 1.0F)
               );
         } else if (mc.field_1724.field_6212 < 0.0F) {
            dir += PostProcessShaders$ConstantPool.const_2dWyvQ3YJIOrlhS
               * (
                  mc.field_1724.field_6250 < 0.0F
                     ? SettingArgumentType$ConstantPool.const_youR674J2NnpsWZ
                     : (mc.field_1724.field_6250 > 0.0F ? DamageUtils$ConstantPool.const_TjvE8r6kYCJNeLb : 1.0F)
               );
         }
      }

      return dir;
   }

   private double getMoveSpeed() {
      double base = AttributeInfo$ConstantPool.const_UFJ7tkfTA2eYayO;
      if (mc.field_1724.method_6059(StatusEffects.field_5904)) {
         base *= 1.0 + BookBot$ConstantPool.const_pnwDm514VVYqJJl * (mc.field_1724.method_6112(StatusEffects.field_5904).method_5578() + 1);
      }

      return base;
   }

   private void setMoveSpeed(PlayerMoveEvent event, double speed) {
      double forward = mc.field_1724.field_6250;
      double strafe = mc.field_1724.field_6212;
      float yaw = mc.field_1724.method_36454();
      if (!PlayerUtils.isMoving()) {
         ((IVec3d)event.movement).setXZ(0.0, 0.0);
      } else {
         if (forward != 0.0) {
            if (strafe > 0.0) {
               yaw += forward > 0.0 ? -45 : 45;
            } else if (strafe < 0.0) {
               yaw += forward > 0.0 ? 45 : -45;
            }
         }

         strafe = 0.0;
         if (forward > 0.0) {
            forward = 1.0;
         } else if (forward < 0.0) {
            forward = Proxies$ConstantPool.const_rdupqEQoNebnLtq;
         }
      }

      double cos = Math.cos(Math.toRadians(yaw + DefaultSocks5PasswordAuthRequest$ConstantPool.const_JCQqOI5WTWQnZ66));
      double sin = Math.sin(Math.toRadians(yaw + JumpVelocityMultiplierEvent$ConstantPool.const_byk1YEFFe20mrB6));
      ((IVec3d)event.movement).setXZ(forward * speed * cos + strafe * speed * sin, forward * speed * sin + strafe * speed * cos);
   }

   public static enum JumpMode {
      Vanilla,
      Burst,
      Glide;
   }
}
