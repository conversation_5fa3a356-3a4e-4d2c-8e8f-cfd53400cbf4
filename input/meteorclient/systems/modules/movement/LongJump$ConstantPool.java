package meteordevelopment.meteorclient.systems.modules.movement;

public final class LongJump$ConstantPool {
   public static String const_hXj0QrGNryEV0Fw = lVRx1aaIXc(dA6uFHV07N("йЯЩЮезѷЩйлжп"));
   public static double const_SsrJtC2pUr7oSY9 = Double.longBitsToDouble(
      1409429967417372835L ^ -1808591573249836550L ^ 7500951078387307656L ^ -2490101512785257007L
   );
   public static String const_taB4osS2qlH35Zr = RqnpGQPBGF(dA6uFHV07N("\u07beިޡޫߠ\u07baިޯ"));
   public static String const_ayn4bm1gkPLiA6o = vEytwYWedk(dA6uFHV07N("˵ˁˀ˛˙˕ˀ˝˗˕˘˘ˍʔ˞ˁ˙˄ˇʔ˒˛ˆʔˍ˛ˁʚ"));
   public static String const_noG41QNFnidPWnw = 1Oi1K2qMqa(dA6uFHV07N("ֳֶָֻֿ֞֩֩\u05faִֿ֨־ֳִֽֿ֨\u05faֵּ\u05faֱֵֶָֹ\u05f7ֱָֻֿ֨\u05faֵֶֻֿ֣֬֨״"));
   public static String const_kuDCSAmQKN3od1D = ipWllJoqOr(dA6uFHV07N("ɛɽɶɬɽɪ"));
   public static String const_9tv7WPD3v7lVAmV = VYeDtwRdSU(
      dA6uFHV07N(
         "ВѤѳѶѤЗАлАпБѧѥѱѥѳѤѤѳѶѼѦѤѳѶАШаЗѽѡѵБАШаѽАШАШаѾЗѼѡѸБАШаѾѹЗѼѡѹБѥѤѳѶАѢѤѳѭѶѥаѥѥзѸбѥѶѤѳѭѼѥѤАШзѽѠѸбаЗѽѡѹБАШзѸбаѺЗѼѡѸБАШзѿбаѺѹЗѼѡѸБАШзѾбаѺѹѹЗѼѡѾБАШаѺѹѹѿЗѼѡѹБѥѤѳХѶЌѤпУЯЧпЗѸѹБѥѥѳѨ"
      )
   );
   public static String const_sdQS1UeryTagiT4 = 4GnoHaWaVO(dA6uFHV07N("ȢȎȔȍȅɁȏȎȕɁȇȄȕȂȉɁȓȄȑȎȒȈȕȎȓȘɁȈȏȇȎȓȌȀȕȈȎȏɁȇȎȓɁȀȅȅȎȏɁɆɄȒɆɏ"));
}
