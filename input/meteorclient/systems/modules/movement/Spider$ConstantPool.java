package meteordevelopment.meteorclient.systems.modules.movement;

public final class Spider$ConstantPool {
   public static int const_9UUWHjleOaFWKdi = (int)(7663161782362904906L ^ 7663161782068932578L) ^ (int)-200607777L ^ -426999519;
   public static int const_rj7IEcKyOuILDZI = (int)(6206726210657733846L ^ -6206726211272474440L) ^ (int)(-147379920 ^ 1435821515);
   public static String const_SsyJVYpJtNv9at0 = hTZiAFY9tR(VpD0mwrYxL("żŷŰźŭ"));
   public static double const_ta4oPNIVdBqbI4B = Double.longBitsToDouble(
      -3279074194039934924L ^ 4250257267570999069L ^ -3652613968423909347L ^ 7334921101324617524L
   );
   public static String const_1DyuvGlkvkbbqTl = 62McR2Vecm(VpD0mwrYxL("ӵӅӇӊӃ҆ӉӀ҆ӒӎӃ҆ӏӒӃӋ҈"));
   public static String const_VlruqzeASgfE1l9 = gtlARpTLvt(VpD0mwrYxL("ܲ܍ܑ܋ܖ܋܍܌"));
   public static long const_GLSWxtAnJzd6me5 = 3698128966619112462L ^ -5765713005470113432L ^ 255053502553815106L ^ -6979356845378831628L;
   public static String const_roIIHFwlFThOz5H = I4VAE6EDn1(VpD0mwrYxL("ǴǲǵǵǨǲǩǣƪǳǾǷǢ"));
   public static int const_kY4lVnX1nyLL6tN = (int)((int)-1566415255L ^ 1535772398 ^ (int)(106621895 ^ -9269564));
}
