package meteordevelopment.meteorclient.systems.modules.movement;

import com.google.common.collect.Streams;
import io.netty.handler.codec.socks.SocksAuthScheme$ConstantPool;
import io.netty.handler.codec.socks.SocksCommonUtils$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthRequestDecoder$ConstantPool;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javassist.CtClassType$ConstantPool;
import javassist.bytecode.Descriptor$ConstantPool;
import javassist.bytecode.Mnemonic$ConstantPool;
import javassist.bytecode.analysis.Frame$ConstantPool;
import javassist.bytecode.analysis.Type$ConstantPool;
import javassist.compiler.MemberResolver$ConstantPool;
import javassist.expr.Handler$ConstantPool;
import javassist.runtime.DotClass$ConstantPool;
import javassist.tools.web.BadHttpRequest$ConstantPool;
import javassist.util.proxy.Proxy$ConstantPool;
import javassist.util.proxy.SecurityActions$3$ConstantPool;
import meteordevelopment.meteorclient.addons.AddonManager$ConstantPool;
import meteordevelopment.meteorclient.asm.transformers.CanvasWorldRendererTransformer$ConstantPool;
import meteordevelopment.meteorclient.commands.arguments.CompoundNbtTagArgumentType$ConstantPool;
import meteordevelopment.meteorclient.commands.arguments.DirectionArgumentType$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.MacroCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.CanWalkOnFluidEvent;
import meteordevelopment.meteorclient.events.entity.player.PushEntityEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.RenderAfterWorldEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.CollisionShapeEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.tabs.builtin.FriendsTab$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.GuiTab$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorPlus$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorTriangle$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WContainer$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.input.WDropdown$ConstantPool;
import meteordevelopment.meteorclient.mixin.LivingEntityAccessor;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$XblXstsResponse$DisplayClaims$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.screens.HudEditorScreen$SelectionBox$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.AutoWeb$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.InfinityMiner$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.EntityUtils;
import meteordevelopment.meteorclient.utils.misc.HorizontalDirection$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.decoder.SongDecoder$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.compiler.Parser$1$ConstantPool;
import meteordevelopment.starscript.compiler.Parser$ParseException$ConstantPool;
import meteordevelopment.starscript.utils.Stack$ConstantPool;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.fluid.Fluids;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.Full;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.PositionAndOnGround;
import net.minecraft.registry.tag.FluidTags;
import net.minecraft.state.property.Properties;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.BlockPos.Mutable;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.util.shape.VoxelShapes;
import net.minecraft.world.GameMode;

public class Jesus extends Module {
   private final SettingGroup sgGeneral = this.settings.createGroup(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(WContainer$ConstantPool.const_mi3lvcGYNm5K9ku))));
   private final SettingGroup sgWater = this.settings
      .createGroup(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(CanvasWorldRendererTransformer$ConstantPool.const_MJblJ0Wrs11GV2n))));
   private final SettingGroup sgLava = this.settings
      .createGroup(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(CompoundNbtTagArgumentType$ConstantPool.const_yliFmYje0cFSdqk))));
   private final Setting<Boolean> powderSnow = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(DirectionArgumentType$ConstantPool.const_4dbnWoDlg8CO1CO))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(WDropdown$ConstantPool.const_Ac17om7vYzwOgQN))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Jesus.Mode> waterMode = this.sgWater
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Parser$ParseException$ConstantPool.const_7YdmyJD4FhcamVm)))))
                  .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(MacroCommand$ConstantPool.const_AdeaF64xDYWGWV1)))))
               .defaultValue(Jesus.Mode.Solid))
            .build()
      );
   private final Setting<Boolean> dipIfBurning = this.sgWater
      .add(
         new BoolSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(WMeteorPlus$ConstantPool.const_1bBgNwldCJUzxM9))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Socks5PasswordAuthRequestDecoder$ConstantPool.const_aDjsH1hierylqll))))
            .defaultValue(true)
            .visible(() -> this.waterMode.get() == Jesus.Mode.Solid)
            .build()
      );
   private final Setting<Boolean> dipOnSneakWater = this.sgWater
      .add(
         new BoolSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(SocksAuthScheme$ConstantPool.const_lxTWLJGujFtaDqn))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(MemberResolver$ConstantPool.const_9dYYa7z2iFFLKO9))))
            .defaultValue(true)
            .visible(() -> this.waterMode.get() == Jesus.Mode.Solid)
            .build()
      );
   private final Setting<Boolean> dipOnFallWater = this.sgWater
      .add(
         new BoolSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Stack$ConstantPool.const_r9GoVVcVyX9Smas))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(GuiTab$ConstantPool.const_N5lpg2Vc9iFBGP4))))
            .defaultValue(true)
            .visible(() -> this.waterMode.get() == Jesus.Mode.Solid)
            .build()
      );
   private final Setting<Integer> dipFallHeightWater = this.sgWater
      .add(
         new IntSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(AutoWeb$ConstantPool.const_Va3kowjjSMOW3rL))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(MicrosoftLogin$XblXstsResponse$DisplayClaims$ConstantPool.const_6TVOd5369GT4YrA))))
            .defaultValue(4)
            .range(1, 255)
            .sliderRange(3, 20)
            .visible(() -> this.waterMode.get() == Jesus.Mode.Solid && this.dipOnFallWater.get())
            .build()
      );
   private final Setting<Jesus.Mode> lavaMode = this.sgLava
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(BadHttpRequest$ConstantPool.const_QNnsawxVBSeepGa)))))
                  .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Proxy$ConstantPool.const_7BOAX4SogPS10po)))))
               .defaultValue(Jesus.Mode.Solid))
            .build()
      );
   private final Setting<Boolean> dipIfFireResistant = this.sgLava
      .add(
         new BoolSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Handler$ConstantPool.const_RQ0xn3qFTb05jAF))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Type$ConstantPool.const_opittOUNnubWb20))))
            .defaultValue(true)
            .visible(() -> this.lavaMode.get() == Jesus.Mode.Solid)
            .build()
      );
   private final Setting<Boolean> dipOnSneakLava = this.sgLava
      .add(
         new BoolSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Mnemonic$ConstantPool.const_UAigyLSYLrkjNSl))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(WMeteorPlus$ConstantPool.const_SOPqcLysj3aXBG5))))
            .defaultValue(true)
            .visible(() -> this.lavaMode.get() == Jesus.Mode.Solid)
            .build()
      );
   private final Setting<Boolean> dipOnFallLava = this.sgLava
      .add(
         new BoolSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Frame$ConstantPool.const_ri2lrbGnDXRanJt))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(SongDecoder$ConstantPool.const_AMJl1z9YXJTaING))))
            .defaultValue(true)
            .visible(() -> this.lavaMode.get() == Jesus.Mode.Solid)
            .build()
      );
   private final Setting<Integer> dipFallHeightLava = this.sgLava
      .add(
         new IntSetting.Builder()
            .name(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(PushEntityEvent$ConstantPool.const_AuvKIoeihE4fuYL))))
            .description(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(WMeteorTriangle$ConstantPool.const_rIOqfQBiBLNyi2y))))
            .defaultValue(4)
            .range(1, 255)
            .sliderRange(3, 20)
            .visible(() -> this.lavaMode.get() == Jesus.Mode.Solid && this.dipOnFallLava.get())
            .build()
      );
   private final Mutable blockPos = new Mutable();
   private int tickTimer = 10;
   private int packetTimer = 0;
   private boolean prePathManagerWalkOnWater;
   private boolean prePathManagerWalkOnLava;
   public boolean isInBubbleColumn = false;

   public Jesus() {
      super(
         Categories.Movement,
         orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(Parser$1$ConstantPool.const_aiJKaAkOmu0q7F7))),
         new StringBuilder(orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(HighJump$ConstantPool.const_Q2vigOwsr1jwyUa)))),
         orUuyGNiVj(4NYTDgZFOb(nY6rrjp9AI(HudEditorScreen$SelectionBox$ConstantPool.const_2eoTi9A43Fjv5ng)))
      );
   }

   @Override
   public void onActivate() {
      this.prePathManagerWalkOnWater = PathManagers.get().getSettings().getWalkOnWater().get();
      this.prePathManagerWalkOnLava = PathManagers.get().getSettings().getWalkOnLava().get();
      PathManagers.get().getSettings().getWalkOnWater().set(this.waterMode.get() == Jesus.Mode.Solid);
      PathManagers.get().getSettings().getWalkOnLava().set(this.lavaMode.get() == Jesus.Mode.Solid);
   }

   @Override
   public void onDeactivate() {
      PathManagers.get().getSettings().getWalkOnWater().set(this.prePathManagerWalkOnWater);
      PathManagers.get().getSettings().getWalkOnLava().set(this.prePathManagerWalkOnLava);
   }

   @EventHandler
   private void onTick(TickEvent.Post event) {
      boolean bubbleColumn = this.isInBubbleColumn;
      this.isInBubbleColumn = false;
      if (this.waterMode.get() == Jesus.Mode.Bob && mc.field_1724.method_5799() || this.lavaMode.get() == Jesus.Mode.Bob && mc.field_1724.method_5771()) {
         double fluidHeight;
         if (mc.field_1724.method_5771()) {
            fluidHeight = mc.field_1724.method_5861(FluidTags.field_15518);
         } else {
            fluidHeight = mc.field_1724.method_5861(FluidTags.field_15517);
         }

         double swimHeight = mc.field_1724.method_29241();
         if (mc.field_1724.method_5799() && fluidHeight > swimHeight) {
            ((LivingEntityAccessor)mc.field_1724).swimUpwards(FluidTags.field_15517);
         } else if (mc.field_1724.method_24828() && fluidHeight <= swimHeight && ((LivingEntityAccessor)mc.field_1724).getJumpCooldown() == 0) {
            mc.field_1724.method_6043();
            ((LivingEntityAccessor)mc.field_1724).setJumpCooldown(10);
         } else {
            ((LivingEntityAccessor)mc.field_1724).swimUpwards(FluidTags.field_15518);
         }
      }

      if (!mc.field_1724.method_5799() || this.waterShouldBeSolid()) {
         if (!mc.field_1724.method_20232()) {
            if (!mc.field_1724.method_5771() || this.lavaShouldBeSolid()) {
               if (bubbleColumn) {
                  if (mc.field_1690.field_1903.method_1434()
                     && mc.field_1724.method_18798().method_10214() < Socks5PasswordAuthRequestDecoder$ConstantPool.const_CGSj2xoTJrFSggU) {
                     ((IVec3d)mc.field_1724.method_18798()).setY(SocksCommonUtils$ConstantPool.const_amrvGnSsyuipfsQ);
                  }
               } else if (!mc.field_1724.method_5799() && !mc.field_1724.method_5771()) {
                  BlockState blockBelowState = mc.field_1687.method_8320(mc.field_1724.method_24515().method_10074());
                  boolean waterLogger = false;

                  try {
                     waterLogger = (Boolean)blockBelowState.method_11654(Properties.field_12508);
                  } catch (Exception var7) {
                  }

                  if (this.tickTimer == 0) {
                     ((IVec3d)mc.field_1724.method_18798()).setY(SecurityActions$3$ConstantPool.const_Po2j6FA1QQqYsGG);
                  } else if (this.tickTimer == 1
                     && (blockBelowState == Blocks.field_10382.method_9564() || blockBelowState == Blocks.field_10164.method_9564() || waterLogger)) {
                     ((IVec3d)mc.field_1724.method_18798()).setY(0.0);
                  }

                  this.tickTimer++;
               } else {
                  ((IVec3d)mc.field_1724.method_18798()).setY(InfinityMiner$ConstantPool.const_PlMlFJbSYUvdgNp);
                  this.tickTimer = 0;
               }
            }
         }
      }
   }

   @EventHandler
   private void onCanWalkOnFluid(CanWalkOnFluidEvent event) {
      if (mc.field_1724 == null || !mc.field_1724.method_20232()) {
         if ((event.fluidState.method_15772() == Fluids.field_15910 || event.fluidState.method_15772() == Fluids.field_15909) && this.waterShouldBeSolid()) {
            event.walkOnFluid = true;
         } else if ((event.fluidState.method_15772() == Fluids.field_15908 || event.fluidState.method_15772() == Fluids.field_15907)
            && this.lavaShouldBeSolid()) {
            event.walkOnFluid = true;
         }
      }
   }

   @EventHandler
   private void onFluidCollisionShape(CollisionShapeEvent event) {
      if (!event.state.method_26227().method_15769()) {
         int var10000 = event.state.method_26204() == Blocks.field_10382 ? 1 : 0;
         int var10002 = event.state.method_26227().method_15772() == Fluids.field_15910 ? 1 : 0;
         if (var10002 + (-var10000 + -1 + (~(var10002 - 1) + -1 & ~(-var10000 + -1)) - (-var10000 + -1)) != 0
            && !mc.field_1724.method_5799()
            && this.waterShouldBeSolid()
            && event.pos.method_10264() <= mc.field_1724.method_23318() - 1.0) {
            event.shape = VoxelShapes.method_1077();
         } else if (event.state.method_26204() == Blocks.field_10164
            && !mc.field_1724.method_5771()
            && this.lavaShouldBeSolid()
            && (!this.lavaIsSafe() || event.pos.method_10264() <= mc.field_1724.method_23318() - 1.0)) {
            event.shape = VoxelShapes.method_1077();
         }
      }
   }

   @EventHandler
   private void onSendPacket(PacketEvent.Send event) {
      if (event.packet instanceof PlayerMoveC2SPacket packet) {
         if (!mc.field_1724.method_5799() || this.waterShouldBeSolid()) {
            if (!mc.field_1724.method_5771() || this.lavaShouldBeSolid()) {
               if (packet instanceof PositionAndOnGround || packet instanceof Full) {
                  if (!mc.field_1724.method_5799()
                     && !mc.field_1724.method_5771()
                     && !(mc.field_1724.field_6017 > RenderAfterWorldEvent$ConstantPool.const_9MRDyY2GTiKlNzp)
                     && this.isOverLiquid()) {
                     if (mc.field_1724.field_3913.field_3905 == 0.0F && mc.field_1724.field_3913.field_3907 == 0.0F) {
                        event.cancel();
                     } else if (this.packetTimer++ >= 4) {
                        this.packetTimer = 0;
                        event.cancel();
                        double x = packet.method_12269(0.0);
                        double y = packet.method_12268(0.0) + CtClassType$ConstantPool.const_BaAW2WZJVLmVSTZ;
                        double z = packet.method_12274(0.0);
                        Packet<?> newPacket;
                        if (packet instanceof PositionAndOnGround) {
                           newPacket = new PositionAndOnGround(x, y, z, true);
                        } else {
                           newPacket = new Full(x, y, z, packet.method_12271(0.0F), packet.method_12270(0.0F), true);
                        }

                        mc.method_1562().method_48296().method_10743(newPacket);
                     }
                  }
               }
            }
         }
      }
   }

   private boolean waterShouldBeSolid() {
      if (EntityUtils.getGameMode(mc.field_1724) != GameMode.field_9219 && !mc.field_1724.method_31549().field_7479) {
         if (mc.field_1724.method_5854() != null) {
            EntityType<?> vehicle = mc.field_1724.method_5854().method_5864();
            if (vehicle == EntityType.field_6121 || vehicle == EntityType.field_38096) {
               return false;
            }
         }

         if (Modules.get().get(Flight.class).isActive()) {
            return false;
         } else if (this.dipIfBurning.get() && mc.field_1724.method_5809()) {
            return false;
         } else if (this.dipOnSneakWater.get() && mc.field_1690.field_1832.method_1434()) {
            return false;
         } else {
            return this.dipOnFallWater.get() && mc.field_1724.field_6017 > this.dipFallHeightWater.get().intValue()
               ? false
               : this.waterMode.get() == Jesus.Mode.Solid;
         }
      } else {
         return false;
      }
   }

   private boolean lavaShouldBeSolid() {
      if (EntityUtils.getGameMode(mc.field_1724) != GameMode.field_9219 && !mc.field_1724.method_31549().field_7479) {
         if (!this.lavaIsSafe() && this.lavaMode.get() == Jesus.Mode.Solid) {
            return true;
         } else if (this.dipOnSneakLava.get() && mc.field_1690.field_1832.method_1434()) {
            return false;
         } else {
            return this.dipOnFallLava.get() && mc.field_1724.field_6017 > this.dipFallHeightLava.get().intValue()
               ? false
               : this.lavaMode.get() == Jesus.Mode.Solid;
         }
      } else {
         return false;
      }
   }

   private boolean lavaIsSafe() {
      return !this.dipIfFireResistant.get()
         ? false
         : mc.field_1724.method_6059(StatusEffects.field_5918)
            && mc.field_1724.method_6112(StatusEffects.field_5918).method_5584()
               > DotClass$ConstantPool.const_4GSFLhtAvowVJGU * mc.field_1724.method_45325(EntityAttributes.field_51579);
   }

   private boolean isOverLiquid() {
      boolean foundLiquid = false;
      boolean foundSolid = false;

      for (Box bb : (List)Streams.stream(
            mc.field_1687.method_20812(mc.field_1724, mc.field_1724.method_5829().method_989(0.0, Descriptor$ConstantPool.const_3LwcrAGavq4OLkH, 0.0))
         )
         .map(VoxelShape::method_1107)
         .collect(Collectors.toCollection(ArrayList::new))) {
         this.blockPos
            .method_10102(
               MathHelper.method_16436(FriendsTab$ConstantPool.const_A2PaWOmylouLWgn, bb.field_1323, bb.field_1320),
               MathHelper.method_16436(HorizontalDirection$ConstantPool.const_24qIwRIJwiNpX9K, bb.field_1322, bb.field_1325),
               MathHelper.method_16436(AddonManager$ConstantPool.const_OinVHXFgbfvOSbb, bb.field_1321, bb.field_1324)
            );
         BlockState blockState = mc.field_1687.method_8320(this.blockPos);
         int var10000 = blockState.method_26204() == Blocks.field_10382 ? 1 : 0;
         int var10002 = blockState.method_26227().method_15772() == Fluids.field_15910 ? 1 : 0;
         if (var10002 + (-var10000 + -1 + (~(var10002 - 1) + -1 & ~(-var10000 + -1)) - (-var10000 + -1)) != 0
            || blockState.method_26204() == Blocks.field_10164) {
            foundLiquid = true;
         } else if (!blockState.method_26215()) {
            foundSolid = true;
         }
      }

      return foundLiquid && !foundSolid;
   }

   public boolean canWalkOnPowderSnow() {
      return this.isActive() && this.powderSnow.get();
   }

   public static enum Mode {
      Solid,
      Bob,
      Ignore;
   }
}
