package meteordevelopment.meteorclient.systems.modules.movement;

public final class LongJump$JumpMode$ConstantPool {
   public static String const_xl2gtsi6t7f9don = 3VUTVefZxf(uUNywfmsBK("̾̿͌"));
   public static String const_dTnvLygpwzlrJ21 = QSGRFrabyw(uUNywfmsBK("ΨΩβϦΧϦΫΣβήΩ\u03a2"));
   public static String const_G4G9JfnE3ElNsH3 = EbvrgteVuJ(uUNywfmsBK("ЂОПЅіПЅіЗіЕЙИЅЂЄЃЕЂЙЄіЕЗККјііеЗККіБГЂеЙИЅЂЄЃЕЂЙЄўџј"));
   public static String const_iavAvmQJYfCSfvf = yv9aQFqYrs(uUNywfmsBK("ąĈěĎČĶĀĄĈĎČ"));
   public static String const_rIY3wLqFsgbFaCg = hIlY5AsGdt(uUNywfmsBK("\u000f9#v;#%\"v43v?8v5$37\"? 3v;923v\"9v#%3v\">?%x"));
   public static double const_nMgGIXLjYoYdUJj = Double.longBitsToDouble(
      -2612365146180243437L ^ 8553048002502799390L ^ 7475384398898605719L ^ -8450649488212196478L
   );
   public static double const_5JCk5avLHQX1ogW = Double.longBitsToDouble(
      -5836956944976686649L ^ -4664641569764096676L ^ -3834691475786018556L ^ -7270292171736693345L
   );
   public static int const_4nvqDGFxanCDVdK = (int)((int)(196817634 ^ -1964578723) ^ ((int)-1487087848L ^ 637601926));
   public static int const_GhrEnlEa38gLWow = (int)((int)(260688156 ^ 2076524354) ^ (int)(670397657 ^ 1404667394));
   public static String const_GO9dgbvF37E9VPt = ydxtAsgCQx(uUNywfmsBK("۩۲ۻ۪ۿڷ۷۵۾ۿ"));
   public static String const_nZgGE6OE2EnYLFL = BUVJJujGtS(uUNywfmsBK("߿ߝߊߗߑߐߍ"));
   public static String const_5O35ry4o5hIVAS6 = vXSTTVmx69(uUNywfmsBK("ߝߜߕߘ߀"));
   public static int const_rYIG4YpKegerxbD = (int)((int)(-1014509850 ^ 1107670860) ^ ((int)-2145608069L ^ 27190774));
   public static String const_eaeI7lDNjbEUaTx = B171BABQDB(uUNywfmsBK("\u008f³¾¦º\u00ad\u0092°©º\u009cí\u008c\u008f¾¼´º«ñ\u0099ª³³"));
}
