package meteordevelopment.meteorclient.systems.modules.movement;

import io.netty.handler.codec.socksx.v4.Socks4CommandType$ConstantPool;
import io.netty.handler.proxy.ProxyHandler$ConstantPool;
import javassist.ByteArrayClassPath$BytecodeURLConnection$ConstantPool;
import javassist.bytecode.CodeIterator$Jump32$ConstantPool;
import javassist.bytecode.SignatureAttribute$Type$ConstantPool;
import javassist.bytecode.annotation.Annotation$Pair$ConstantPool;
import javassist.compiler.ast.CallExpr$ConstantPool;
import javassist.util.proxy.DefineClassHelper$Helper$ConstantPool;
import javassist.util.proxy.RuntimeSupport$ConstantPool;
import javassist.util.proxy.SecurityActions$ConstantPool;
import javax.annotation.Nonnull$Checker$ConstantPool;
import javax.annotation.ParametersAreNullableByDefault$ConstantPool;
import meteordevelopment.meteorclient.Main$OperatingSystem$1$ConstantPool;
import meteordevelopment.meteorclient.commands.Commands$ConstantPool;
import meteordevelopment.meteorclient.commands.arguments.SettingValueArgumentType$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.CanWalkOnFluidEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PushFluidsEvent$ConstantPool;
import meteordevelopment.meteorclient.events.meteor.MouseButtonEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.RenderItemEntityEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.screens.MarkerScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.WMeteorSection$WHeaderTriangle$ConstantPool;
import meteordevelopment.meteorclient.mixin.EntityVelocityUpdateS2CPacketAccessor;
import meteordevelopment.meteorclient.mixininterface.IChatHudLineVisible$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.hud.elements.HoleHud$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.screens.AddHudElementScreen$Item$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.ArrowDodge$MoveType$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.AutoArmor$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AotuPlaceStep$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.AutoReconnect$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.SpeedMine$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ESP$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Xray$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.BaseMarker$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$8$ConstantPool;
import meteordevelopment.meteorclient.utils.PostInit$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.fakeplayer.FakePlayerManager$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.PacketUtil$ConstantPool;
import meteordevelopment.meteorclient.utils.player.TitleScreenCredits$ConstantPool;
import meteordevelopment.meteorclient.utils.render.postprocess.PostProcessShaders$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.network.packet.s2c.play.EntityVelocityUpdateS2CPacket;

public class Velocity extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   public final Setting<Boolean> knockback = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(HighwayBuilder$State$8$ConstantPool.const_B3kYLLP1iFhqUKu))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(AutoWalk$ConstantPool.const_VzNe3O77wDlSubg))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Double> knockbackHorizontal = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(AutoArmor$ConstantPool.const_SYqkvojUijblxTc))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(Nonnull$Checker$ConstantPool.const_Yl4r8zc9ay8T2OY))))
            .defaultValue(0.0)
            .sliderMax(1.0)
            .visible(this.knockback::get)
            .build()
      );
   public final Setting<Double> knockbackVertical = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(SignatureAttribute$Type$ConstantPool.const_mFYnLBlORKTDg4F))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(BaseMarker$ConstantPool.const_TTpVvWNgvoJ12dy))))
            .defaultValue(0.0)
            .sliderMax(1.0)
            .visible(this.knockback::get)
            .build()
      );
   public final Setting<Boolean> explosions = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(WMeteorSection$WHeaderTriangle$ConstantPool.const_75Divv22Z8HFLAO))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(RuntimeSupport$ConstantPool.const_BVpuuJ5PgEFPZQ6))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Double> explosionsHorizontal = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(SpeedMine$ConstantPool.const_MuOHCwtVYUKt72J))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(PostInit$ConstantPool.const_Da1ALBVBbFwIspe))))
            .defaultValue(0.0)
            .sliderMax(1.0)
            .visible(this.explosions::get)
            .build()
      );
   public final Setting<Double> explosionsVertical = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(HoleHud$ConstantPool.const_6Ue9a9Qb3f9HrGV))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(ProxyHandler$ConstantPool.const_Pd3GYTsVpYrn69l))))
            .defaultValue(0.0)
            .sliderMax(1.0)
            .visible(this.explosions::get)
            .build()
      );
   public final Setting<Boolean> liquids = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(ESP$ConstantPool.const_bLdIFHS6D1l9dgO))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(AutoWalk$ConstantPool.const_0pjVXGesiPTOwNT))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Double> liquidsHorizontal = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(Socks4CommandType$ConstantPool.const_B7TY39rwraqUVuL))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(SecurityActions$ConstantPool.const_n9lrlY9Yrnv2jqN))))
            .defaultValue(0.0)
            .sliderMax(1.0)
            .visible(this.liquids::get)
            .build()
      );
   public final Setting<Double> liquidsVertical = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(MouseButtonEvent$ConstantPool.const_y91WoBLeP8IObp9))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(ByteArrayClassPath$BytecodeURLConnection$ConstantPool.const_KDAPZI9DDk2zhVF))))
            .defaultValue(0.0)
            .sliderMax(1.0)
            .visible(this.liquids::get)
            .build()
      );
   public final Setting<Boolean> entityPush = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(DefineClassHelper$Helper$ConstantPool.const_iDJLVoNHfnLqmJE))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(MarkerScreen$ConstantPool.const_7qvr6wGNQnXwvT3))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Double> entityPushAmount = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(AutoReconnect$ConstantPool.const_dQFjwTQrBLpg2Rr))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(CallExpr$ConstantPool.const_jAwPvRsSFSP73DQ))))
            .defaultValue(0.0)
            .sliderMax(1.0)
            .visible(this.entityPush::get)
            .build()
      );
   public final Setting<Boolean> blocks = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(Xray$ConstantPool.const_9eTwvhctBrOpQYw))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(CanWalkOnFluidEvent$ConstantPool.const_44tfKN2RUNBCDjI))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Boolean> sinking = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(CodeIterator$Jump32$ConstantPool.const_4ia78jOLeg5x7VJ))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(Main$OperatingSystem$1$ConstantPool.const_frLXlbq6ufy0jRb))))
            .defaultValue(false)
            .build()
      );
   public final Setting<Boolean> fishing = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(Annotation$Pair$ConstantPool.const_tobEjhSYJJ4g9YW))))
            .description(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(Commands$ConstantPool.const_SY7d7376jahSjou))))
            .defaultValue(false)
            .build()
      );

   public Velocity() {
      super(
         Categories.Movement,
         r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(SettingValueArgumentType$ConstantPool.const_uQ5nIhyb146bxrZ))),
         new StringBuilder(r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(FakePlayerManager$ConstantPool.const_jw6mil9PQTqirFe)))),
         r0AwvP2GlK(1wWvNEiQCL(BtzNUTvY8a(PushFluidsEvent$ConstantPool.const_o0wpyi24Fc0gFC1)))
      );
   }

   @EventHandler
   private void onTick(TickEvent.Post event) {
      if (this.sinking.get()) {
         if (!mc.field_1690.field_1903.method_1434() && !mc.field_1690.field_1832.method_1434()) {
            if ((mc.field_1724.method_5799() || mc.field_1724.method_5771()) && mc.field_1724.method_18798().field_1351 < 0.0) {
               ((IVec3d)mc.field_1724.method_18798()).setY(0.0);
            }
         }
      }
   }

   @EventHandler
   private void onPacketReceive(PacketEvent.Receive event) {
      if (this.knockback.get() && event.packet instanceof EntityVelocityUpdateS2CPacket packet && packet.method_11818() == mc.field_1724.method_5628()) {
         double velX = (packet.method_11815() / IChatHudLineVisible$ConstantPool.const_IxFqXYz1Bly3kTI - mc.field_1724.method_18798().field_1352)
            * this.knockbackHorizontal.get();
         double velY = (packet.method_11816() / TitleScreenCredits$ConstantPool.const_54BNZGPvGDkyp6a - mc.field_1724.method_18798().field_1351)
            * this.knockbackVertical.get();
         double velZ = (packet.method_11819() / AotuPlaceStep$ConstantPool.const_cbdgGFNDZTyaVe7 - mc.field_1724.method_18798().field_1350)
            * this.knockbackHorizontal.get();
         ((EntityVelocityUpdateS2CPacketAccessor)packet)
            .setX(
               (int)(
                  velX * AddHudElementScreen$Item$ConstantPool.const_yFNv60EOBrUSNav
                     + mc.field_1724.method_18798().field_1352 * PostProcessShaders$ConstantPool.const_05Wcal3Qo27N4oW
               )
            );
         ((EntityVelocityUpdateS2CPacketAccessor)packet)
            .setY(
               (int)(
                  velY * PacketUtil$ConstantPool.const_6Djwev0Ati9ao9k
                     + mc.field_1724.method_18798().field_1351 * ParametersAreNullableByDefault$ConstantPool.const_w9anwujN23OyjE1
               )
            );
         ((EntityVelocityUpdateS2CPacketAccessor)packet)
            .setZ(
               (int)(
                  velZ * ArrowDodge$MoveType$ConstantPool.const_usapYeiKnwn0Lbj
                     + mc.field_1724.method_18798().field_1350 * RenderItemEntityEvent$ConstantPool.const_Tt4MYUPc2N6dIna
               )
            );
      }
   }

   public double getHorizontal(Setting<Double> setting) {
      return this.isActive() ? setting.get() : 1.0;
   }

   public double getVertical(Setting<Double> setting) {
      return this.isActive() ? setting.get() : 1.0;
   }
}
