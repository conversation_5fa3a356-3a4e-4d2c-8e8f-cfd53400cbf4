package meteordevelopment.meteorclient.systems.modules.movement;

import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthResponseDecoder$ConstantPool;
import io.netty.handler.proxy.ProxyHandler$LazyChannelPromise$ConstantPool;
import javassist.bytecode.ClassFileWriter$MethodWriter$ConstantPool;
import javassist.compiler.ast.Keyword$ConstantPool;
import javassist.scopedpool.SoftValueHashMap$ConstantPool;
import javax.annotation.Nonnull$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.StoppedUsingItemEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.RenderBossBarEvent$BossIterator$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixin.ClientPlayerEntityAccessor;
import meteordevelopment.meteorclient.mixininterface.IPlayerInteractEntityC2SPacket;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.AutoAnvil$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.TridentExp$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$StaticInstaDropListener$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.CityESP$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Tracers$1$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.Producer$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket.InteractType;

public class Sprint extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   public final Setting<Sprint.Mode> mode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(Keyword$ConstantPool.const_KeGwplNnSvETrbo)))))
                  .description(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(SoftValueHashMap$ConstantPool.const_4LvqKyhtvXlwwTg)))))
               .defaultValue(Sprint.Mode.Strict))
            .build()
      );
   public final Setting<Boolean> jumpFix = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(CityESP$ConstantPool.const_DeWoZnTJv7IW6d3))))
            .description(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(RenderBossBarEvent$BossIterator$ConstantPool.const_VHiB1q2nKDUl9VH))))
            .defaultValue(true)
            .visible(() -> this.mode.get() == Sprint.Mode.Rage)
            .build()
      );
   private final Setting<Boolean> keepSprint = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(Nonnull$ConstantPool.const_E2RNonbnLckTn73))))
            .description(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(ProxyHandler$LazyChannelPromise$ConstantPool.const_rwYYeu0QESXm3Y5))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> unsprintOnHit = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(Tracers$1$ConstantPool.const_Ti6RnfgjX85nyyQ))))
            .description(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(AutoAnvil$ConstantPool.const_C6FTih45avy2omN))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> unsprintInWater = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(StoppedUsingItemEvent$ConstantPool.const_YjVTotV6i1ZBaJn))))
            .description(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(TridentExp$ConstantPool.const_NeWegZFD1e1o2sQ))))
            .defaultValue(true)
            .build()
      );

   public Sprint() {
      super(
         Categories.Movement,
         kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(Producer$ConstantPool.const_5JdY56VDVSqbRf4))),
         new StringBuilder(kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(Socks5PasswordAuthResponseDecoder$ConstantPool.const_xq0jm3iza6od9dd)))),
         kABRAHaWI7(NomNMVlyYb(uiyiVWYb1W(ElytraFly$StaticInstaDropListener$ConstantPool.const_hie0PbzbYS6w9SX)))
      );
   }

   @Override
   public void onDeactivate() {
      mc.field_1724.method_5728(false);
   }

   @EventHandler
   private void onTickMovement(TickEvent.Post event) {
      if (this.shouldSprint()) {
         mc.field_1724.method_5728(true);
      }
   }

   @EventHandler(
      priority = 100
   )
   private void onPacketSend(PacketEvent.Send event) {
      if (this.unsprintOnHit.get() && event.packet instanceof IPlayerInteractEntityC2SPacket packet && packet.getType() == InteractType.field_29172) {
         mc.method_1562()
            .method_52787(new ClientCommandC2SPacket(mc.field_1724, net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket.Mode.field_12985));
         mc.field_1724.method_5728(false);
      }
   }

   @EventHandler
   private void onPacketSent(PacketEvent.Sent event) {
      if (this.unsprintOnHit.get() && this.keepSprint.get()) {
         if (event.packet instanceof IPlayerInteractEntityC2SPacket packet && packet.getType() == InteractType.field_29172) {
            if (this.shouldSprint() && !mc.field_1724.method_5624()) {
               mc.method_1562()
                  .method_52787(new ClientCommandC2SPacket(mc.field_1724, net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket.Mode.field_12981));
               mc.field_1724.method_5728(true);
            }
         }
      }
   }

   public boolean shouldSprint() {
      if (!this.unsprintInWater.get() || !mc.field_1724.method_5799() && !mc.field_1724.method_5869()) {
         boolean strictSprint = mc.field_1724.field_6250 > ClassFileWriter$MethodWriter$ConstantPool.const_SRdjGQP2IkAwrhn
            && ((ClientPlayerEntityAccessor)mc.field_1724).invokeCanSprint()
            && (!mc.field_1724.field_5976 || mc.field_1724.field_34927)
            && (!mc.field_1724.method_5799() || mc.field_1724.method_5869());
         return this.isActive()
            && (this.mode.get() == Sprint.Mode.Rage || strictSprint)
            && (mc.field_1755 == null || Modules.get().get(GUIMove.class).sprint.get());
      } else {
         return false;
      }
   }

   public boolean rageSprint() {
      return this.isActive() && this.mode.get() == Sprint.Mode.Rage;
   }

   public boolean stopSprinting() {
      return !this.isActive() || !this.keepSprint.get();
   }

   public static enum Mode {
      Strict,
      Rage;
   }
}
