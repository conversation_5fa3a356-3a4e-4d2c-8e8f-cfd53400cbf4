package meteordevelopment.meteorclient.systems.modules.combat;

import com.google.common.util.concurrent.AtomicDouble;
import io.netty.handler.codec.socks.SocksAuthResponse$ConstantPool;
import io.netty.handler.codec.socks.SocksCmdRequest$1$ConstantPool;
import io.netty.handler.codec.socks.SocksCmdRequestDecoder$State$ConstantPool;
import io.netty.handler.codec.socks.SocksCmdStatus$ConstantPool;
import io.netty.handler.codec.socksx.v4.Socks4ClientDecoder$State$ConstantPool;
import io.netty.handler.codec.socksx.v4.Socks4ClientEncoder$ConstantPool;
import io.netty.handler.codec.socksx.v5.DefaultSocks5CommandRequest$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5AddressEncoder$1$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5CommandRequest$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5InitialRequestDecoder$State$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthRequestDecoder$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthResponseDecoder$ConstantPool;
import io.netty.handler.proxy.HttpProxyHandler$1$ConstantPool;
import io.netty.handler.proxy.HttpProxyHandler$HttpClientCodecWrapper$ConstantPool;
import it.unimi.dsi.fastutil.ints.Int2IntMap;
import it.unimi.dsi.fastutil.ints.Int2IntOpenHashMap;
import it.unimi.dsi.fastutil.ints.IntIterator;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import it.unimi.dsi.fastutil.ints.IntSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import javassist.ClassPathList$ConstantPool;
import javassist.CtArray$ConstantPool;
import javassist.CtField$PtreeInitializer$ConstantPool;
import javassist.CtMethod$ConstantPool;
import javassist.Modifier$ConstantPool;
import javassist.NotFoundException$ConstantPool;
import javassist.bytecode.AnnotationsAttribute$Walker$ConstantPool;
import javassist.bytecode.BootstrapMethodsAttribute$ConstantPool;
import javassist.bytecode.ByteStream$ConstantPool;
import javassist.bytecode.CodeIterator$If16$ConstantPool;
import javassist.bytecode.CodeIterator$Jump32$ConstantPool;
import javassist.bytecode.ConstPool$ConstantPool;
import javassist.bytecode.Descriptor$ConstantPool;
import javassist.bytecode.ExceptionsAttribute$ConstantPool;
import javassist.bytecode.SignatureAttribute$BaseType$ConstantPool;
import javassist.bytecode.SignatureAttribute$TypeVariable$ConstantPool;
import javassist.bytecode.SourceFileAttribute$ConstantPool;
import javassist.bytecode.analysis.ControlFlow$ConstantPool;
import javassist.bytecode.annotation.FloatMemberValue$ConstantPool;
import javassist.bytecode.stackmap.BasicBlock$Maker$ConstantPool;
import javassist.bytecode.stackmap.Tracer$ConstantPool;
import javassist.compiler.ast.ASTList$ConstantPool;
import javassist.compiler.ast.CondExpr$ConstantPool;
import javassist.scopedpool.ScopedClassPool$ConstantPool;
import javassist.tools.rmi.ExportedObject$ConstantPool;
import javassist.util.proxy.DefineClassHelper$Java11$ConstantPool;
import javassist.util.proxy.DefinePackageHelper$Helper$ConstantPool;
import javassist.util.proxy.MethodFilter$ConstantPool;
import javax.annotation.CheckForNull$ConstantPool;
import javax.annotation.meta.Exclusive$ConstantPool;
import javax.annotation.meta.TypeQualifierDefault$ConstantPool;
import meteordevelopment.discordipc.RichPresence$Assets$ConstantPool;
import meteordevelopment.discordipc.connection.UnixConnection$State$ConstantPool;
import meteordevelopment.meteorclient.commands.arguments.ComponentMapArgumentType$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.GiveCommand$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.SettingCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.EntityAddedEvent;
import meteordevelopment.meteorclient.events.entity.EntityRemovedEvent;
import meteordevelopment.meteorclient.events.entity.player.BreakBlockEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.InteractBlockEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.InteractItemEvent$ConstantPool;
import meteordevelopment.meteorclient.events.meteor.CustomFontChangedEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.render.Render2DEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.BlockActivateEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.DefaultSettingsWidgetFactory$WSelectedCountLabel$ConstantPool;
import meteordevelopment.meteorclient.gui.GuiTheme$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.ModuleScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.ModulesScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.ColorSettingScreen$WBrightnessQuad$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.EntityTypeListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.RegistryListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.TabScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.WindowTabScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.ProfilesTab$EditProfileScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.WMeteorSection$WHeaderTriangle$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorMinus$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.AlignmentX$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.AlignmentY$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.SettingsWidgetFactory$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.StarscriptTextBoxRenderer$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WHorizontalSeparator$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IBox;
import meteordevelopment.meteorclient.mixininterface.IRaycastContext;
import meteordevelopment.meteorclient.mixininterface.IVec3d;
import meteordevelopment.meteorclient.pathing.BaritoneUtils$ConstantPool;
import meteordevelopment.meteorclient.renderer.Shader$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.renderer.text.FontFamily$ConstantPool;
import meteordevelopment.meteorclient.renderer.text.TextRenderer;
import meteordevelopment.meteorclient.settings.BlockListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.BlockSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EntityTypeListSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.FontFaceSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.ItemListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.KeybindSetting;
import meteordevelopment.meteorclient.settings.ModuleListSetting;
import meteordevelopment.meteorclient.settings.ModuleListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.PacketListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.ParticleTypeListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.ProvidedStringSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.Setting$ConstantPool;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.SettingGroup$ConstantPool;
import meteordevelopment.meteorclient.settings.Settings$ConstantPool;
import meteordevelopment.meteorclient.settings.StatusEffectListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$GameOwnershipResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$XblXstsResponse$DisplayClaims$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.types.TheAlteningAccount$ConstantPool;
import meteordevelopment.meteorclient.systems.friends.Friends;
import meteordevelopment.meteorclient.systems.hud.HudBox$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.HudRenderer$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.ArmorHud$Orientation$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.CombatHud$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.CompassHud$Direction$ConstantPool;
import meteordevelopment.meteorclient.systems.macros.Macros$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.ggboy.AntiPhase$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoPlow$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoSign$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.DoubleAccountTP$Role$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.FlyOnFirstTime$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.LinkCutTree$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.SkyLadder$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.AntiPacketKick$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.NameProtect$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.Notebot$Stage$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AutoJump$JumpWhen$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AutoWasp$Action$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.LongJump$JumpMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.ReverseStep$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.Step$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$ChestSwapMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.speed.SpeedMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoGap$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoTool$ListMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.GhostHand$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.Rotation$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.BetterTooltips$DisplayWhen$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.LightOverlay$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.LightOverlay$Spawn$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.TimeChanger$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Tracers$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.TunnelESP$TChunk$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WaypointsModule$WIcon$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Zoom$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.Sphere2dMarker$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.AutoBrewer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$Rotation$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$StraightBlockPosProvider$5$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.LiquidFiller$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.PacketMine$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.PacketMine$MyBlock$ConstantPool;
import meteordevelopment.meteorclient.systems.profiles.Profiles$ConstantPool;
import meteordevelopment.meteorclient.systems.proxies.Proxies$ConstantPool;
import meteordevelopment.meteorclient.systems.proxies.ProxyType$ConstantPool;
import meteordevelopment.meteorclient.systems.waypoints.Waypoints$WaypointIterator$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.DamageUtils;
import meteordevelopment.meteorclient.utils.entity.EntityUtils;
import meteordevelopment.meteorclient.utils.entity.Target;
import meteordevelopment.meteorclient.utils.misc.EmptyIterator$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.FakeClientPlayer$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.Keybind;
import meteordevelopment.meteorclient.utils.misc.NbtUtils$ToKey$ConstantPool;
import meteordevelopment.meteorclient.utils.network.FailedHttpResponse$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$NotebotMode$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$OptionalInstrument$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.instrumentdetect.InstrumentDetectMode$ConstantPool;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.InventorySorter$MySlot$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InventorySorter$SlotMap$ConstantPool;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.NametagUtils;
import meteordevelopment.meteorclient.utils.render.RenderUtils;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.render.postprocess.PostProcessShaders$ConstantPool;
import meteordevelopment.meteorclient.utils.render.prompts.OkPrompt$ConstantPool;
import meteordevelopment.meteorclient.utils.tooltip.MapTooltipComponent$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockIterator;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.TickRate;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.listeners.ConsumerListener$ConstantPool;
import meteordevelopment.starscript.compiler.Compiler$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Binary$ConstantPool;
import meteordevelopment.starscript.compiler.Token$ConstantPool;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.HoeItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.item.ToolItem;
import net.minecraft.item.ToolMaterial;
import net.minecraft.item.ToolMaterials;
import net.minecraft.network.packet.c2s.play.HandSwingC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.network.packet.c2s.play.UpdateSelectedSlotC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult.Type;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos.Mutable;
import net.minecraft.world.RaycastContext;
import net.minecraft.world.RaycastContext.FluidHandling;
import net.minecraft.world.RaycastContext.ShapeType;
import org.joml.Vector3d;
import org.reflections.Configuration$ConstantPool;
import org.reflections.scanners.MemberUsageScanner$ConstantPool;
import org.reflections.scanners.MethodParameterScanner$ConstantPool;
import org.reflections.scanners.Scanners$6$ConstantPool;
import org.reflections.scanners.TypeElementsScanner$ConstantPool;
import org.reflections.vfs.JarInputFile$1$ConstantPool;
import org.reflections.vfs.JbossDir$1$ConstantPool;
import org.reflections.vfs.Vfs$File$ConstantPool;

public class CrystalAura extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgSwitch = this.settings.createGroup(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(WMeteorMinus$ConstantPool.const_JcniTfFIQqT6fcP))));
   private final SettingGroup sgPlace = this.settings
      .createGroup(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(RegistryListSettingScreen$ConstantPool.const_jAOtpIuETeZ79gp))));
   private final SettingGroup sgFacePlace = this.settings.createGroup(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ControlFlow$ConstantPool.const_74nlJW55kCI4Tkf))));
   private final SettingGroup sgBreak = this.settings
      .createGroup(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(MicrosoftLogin$XblXstsResponse$DisplayClaims$ConstantPool.const_q0N6B61et1DQSgA))));
   private final SettingGroup sgPause = this.settings
      .createGroup(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(HighwayBuilder$StraightBlockPosProvider$5$ConstantPool.const_jKiYrjyAYQfFqtG))));
   private final SettingGroup sgRender = this.settings.createGroup(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AutoGap$ConstantPool.const_AJtwNdMSm4Y2YNB))));
   private final Setting<Double> targetRange = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AutoBrewer$ConstantPool.const_y60DQWJjt4TDGyv))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(InventorySorter$MySlot$ConstantPool.const_DWnzFIfLuIi9ddk))))
            .defaultValue(StarscriptTextBoxRenderer$ConstantPool.const_V6TueZntwoBMALQ)
            .min(0.0)
            .sliderMax(BlockActivateEvent$ConstantPool.const_bvnW6W6gBTLbBi9)
            .build()
      );
   private final Setting<Boolean> predictMovement = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ColorSettingScreen$WBrightnessQuad$ConstantPool.const_lC7DVvWvVYnU3Yh))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Socks5InitialRequestDecoder$State$ConstantPool.const_9rrj4A1nqkKaO4D))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Double> minDamage = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Setting$ConstantPool.const_MpYlUCLew4tAOBv))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(PlayerUtils$ConstantPool.const_D1NdMlnBOAdTgFn))))
            .defaultValue(MethodParameterScanner$ConstantPool.const_H4hCc9Asvz1erko)
            .min(0.0)
            .build()
      );
   private final Setting<Double> maxDamage = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Token$ConstantPool.const_PMTy16d1mI9qGbY))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(CtField$PtreeInitializer$ConstantPool.const_i2DS9z45aB68wdt))))
            .defaultValue(DefaultSocks5CommandRequest$ConstantPool.const_4eIZEF9owWLDbaK)
            .range(0.0, PacketMine$MyBlock$ConstantPool.const_Y2Y2Ee4pwOUh6hq)
            .sliderMax(BlockListSetting$ConstantPool.const_Qb4nwbLM78Mw3uN)
            .build()
      );
   private final Setting<Boolean> antiSuicide = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(NameProtect$ConstantPool.const_S0Txq1fY96EjUvx))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Socks5CommandRequest$ConstantPool.const_Kk5i4gXWrnLG6AG))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> ignoreNakeds = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AnnotationsAttribute$Walker$ConstantPool.const_GOZuubOKJ8okF4A))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(MicrosoftLogin$GameOwnershipResponse$ConstantPool.const_4brXqtPaIH0OAFM))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> rotate = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ArmorHud$Orientation$ConstantPool.const_K7YIy0NSTQFjaZY))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(TypeElementsScanner$ConstantPool.const_g6OwFjh7rLe0FDW))))
            .defaultValue(true)
            .build()
      );
   private final Setting<CrystalAura.YawStepMode> yawStepMode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                        .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(UnixConnection$State$ConstantPool.const_KyBr6yJDCArrrS9)))))
                     .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(MemberUsageScanner$ConstantPool.const_UsT1Yru6iTDqLY0)))))
                  .defaultValue(CrystalAura.YawStepMode.Break))
               .visible(this.rotate::get))
            .build()
      );
   private final Setting<Double> yawSteps = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AutoPlow$ConstantPool.const_HrN631IBWJ89YOg))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Socks5PasswordAuthResponseDecoder$ConstantPool.const_4I9cT2t19BwFJQB))))
            .defaultValue(TypeQualifierDefault$ConstantPool.const_1vgoGrH4uq7JdF7)
            .range(1.0, BasicBlock$Maker$ConstantPool.const_GOjtJngz4RNdTBy)
            .visible(this.rotate::get)
            .build()
      );
   private final Setting<Set<EntityType<?>>> entities = this.sgGeneral
      .add(
         new EntityTypeListSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(HttpProxyHandler$HttpClientCodecWrapper$ConstantPool.const_binv0NQ60R4Crw4))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Rotation$ConstantPool.const_RLarFrGgvdbSGW0))))
            .onlyAttackable()
            .defaultValue(EntityType.field_6097, EntityType.field_38095, EntityType.field_6119)
            .build()
      );
   private final Setting<CrystalAura.AutoSwitchMode> autoSwitch = this.sgSwitch
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Exclusive$ConstantPool.const_Y1OJPj4UViSWSKU)))))
                  .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(HttpProxyHandler$HttpClientCodecWrapper$ConstantPool.const_Od2lrijVCokVA1w)))))
               .defaultValue(CrystalAura.AutoSwitchMode.Normal))
            .build()
      );
   private final Setting<Integer> switchDelay = this.sgSwitch
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(FailedHttpResponse$ConstantPool.const_uoswOWqjlC7UFrG))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(InventorySorter$SlotMap$ConstantPool.const_n42StMhi19QAoTu))))
            .defaultValue(0)
            .min(0)
            .build()
      );
   private final Setting<Boolean> noGapSwitch = this.sgSwitch
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(HttpProxyHandler$1$ConstantPool.const_wjBh3zMFMB9v5Bi))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ComponentMapArgumentType$ConstantPool.const_0BY1FiVVCIOGIY0))))
            .defaultValue(true)
            .visible(() -> this.autoSwitch.get() == CrystalAura.AutoSwitchMode.Normal)
            .build()
      );
   private final Setting<Boolean> noBowSwitch = this.sgSwitch
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ByteStream$ConstantPool.const_bwSNsLdbUikqwJd))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(DefineClassHelper$Java11$ConstantPool.const_rDQ0z6kGD9agP40))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> antiWeakness = this.sgSwitch
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Descriptor$ConstantPool.const_94e3PgKqS6vWVdd))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(TheAlteningAccount$ConstantPool.const_bykjqCSK4Op6SLA))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> doPlace = this.sgPlace
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Notebot$Stage$ConstantPool.const_lMGbCALJFZan3s2))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SettingCommand$ConstantPool.const_LnoIFoBGODYVy2z))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Integer> placeDelay = this.sgPlace
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SocksCmdStatus$ConstantPool.const_3r1ZnJ7WsoSGHkD))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(PostProcessShaders$ConstantPool.const_DTjQi5aZip3b7dE))))
            .defaultValue(0)
            .min(0)
            .sliderMax(20)
            .build()
      );
   private final Setting<Double> placeRange = this.sgPlace
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(RegistryListSettingScreen$ConstantPool.const_afiJS8TsodOtjDo))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(InstrumentDetectMode$ConstantPool.const_6YCTSnECvSF6L7D))))
            .defaultValue(ModuleListSetting$Builder$ConstantPool.const_TbYxw0VIfknFDZ2)
            .min(0.0)
            .sliderMax(SkyLadder$ConstantPool.const_xNicplwVjP3qdLe)
            .build()
      );
   private final Setting<Double> placeWallsRange = this.sgPlace
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(CheckForNull$ConstantPool.const_WTE4iHG7tWG3gnl))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Settings$ConstantPool.const_odgS8Va91r6V1I2))))
            .defaultValue(WindowTabScreen$ConstantPool.const_QTQAs7XRweWbMWr)
            .min(0.0)
            .sliderMax(ProvidedStringSetting$Builder$ConstantPool.const_A5ZF67ziTjNPvqx)
            .build()
      );
   private final Setting<Boolean> placement112 = this.sgPlace
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AntiAnchor$ConstantPool.const_DqzsQD5Be3F7zYq))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(MapTooltipComponent$ConstantPool.const_waaeLjwZVMViw1i))))
            .defaultValue(false)
            .build()
      );
   private final Setting<CrystalAura.SupportMode> support = this.sgPlace
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(GhostHand$ConstantPool.const_dgW1dYc03brSsg1)))))
                  .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(PostProcessShaders$ConstantPool.const_qzU9PMiayeQNXtZ)))))
               .defaultValue(CrystalAura.SupportMode.Disabled))
            .build()
      );
   private final Setting<Integer> supportDelay = this.sgPlace
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AntiAnvil$ConstantPool.const_ugRDc46o9NMZNPf))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Socks5AddressEncoder$1$ConstantPool.const_V5yoYDfeqQn1u8R))))
            .defaultValue(1)
            .min(0)
            .visible(() -> this.support.get() != CrystalAura.SupportMode.Disabled)
            .build()
      );
   private final Setting<Boolean> facePlace = this.sgFacePlace
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(FontFaceSetting$Builder$ConstantPool.const_HLGKw1gdN9M4jfM))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(BreakBlockEvent$ConstantPool.const_10bOYBNW1EGlmg8))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Double> facePlaceHealth = this.sgFacePlace
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(TimeChanger$ConstantPool.const_hwCJygGssqvATc2))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(BootstrapMethodsAttribute$ConstantPool.const_gg9Y02jco5I6D7P))))
            .defaultValue(GiveCommand$ConstantPool.const_B1li2nA4IzL1KHI)
            .min(1.0)
            .sliderMin(1.0)
            .sliderMax(Proxies$ConstantPool.const_Wi3nUPS6LLewnGF)
            .visible(this.facePlace::get)
            .build()
      );
   private final Setting<Double> facePlaceDurability = this.sgFacePlace
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(MethodFilter$ConstantPool.const_VGro9wf67vKCoYd))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ASTList$ConstantPool.const_gBTSDOF0UqEOtZs))))
            .defaultValue(HighwayBuilder$Rotation$ConstantPool.const_SALA6pzSCL1Nm6B)
            .min(1.0)
            .sliderMin(1.0)
            .sliderMax(ConstPool$ConstantPool.const_Aef79nus1WOVKyi)
            .visible(this.facePlace::get)
            .build()
      );
   private final Setting<Boolean> facePlaceArmor = this.sgFacePlace
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SocksAuthResponse$ConstantPool.const_UEl44DczJWUbKj1))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Zoom$ConstantPool.const_Kx1b1YDJIUDYeT9))))
            .defaultValue(false)
            .visible(this.facePlace::get)
            .build()
      );
   private final Setting<Keybind> forceFacePlace = this.sgFacePlace
      .add(
         new KeybindSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AlignmentY$ConstantPool.const_sO464TAD0tQ6r6v))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(FakeClientPlayer$ConstantPool.const_nDK8hyS4KYneBeB))))
            .defaultValue(Keybind.none())
            .build()
      );
   private final Setting<Boolean> doBreak = this.sgBreak
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SpeedMode$ConstantPool.const_iWJ2ge4SZkbiQWJ))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(JarInputFile$1$ConstantPool.const_CBbo9wdxQFASqpN))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Integer> breakDelay = this.sgBreak
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(WMeteorSection$WHeaderTriangle$ConstantPool.const_yv3GUNSfd5S91Vf))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(LightOverlay$ConstantPool.const_JIwk6CMwa1uLIBb))))
            .defaultValue(0)
            .min(0)
            .sliderMax(20)
            .build()
      );
   private final Setting<Boolean> smartDelay = this.sgBreak
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(CombatHud$ConstantPool.const_LqDco4SJWgqCj9Z))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AutoWasp$Action$ConstantPool.const_mX7TqrXQPNCB6zn))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Double> breakRange = this.sgBreak
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ConsumerListener$ConstantPool.const_CqHh2qJ2bTDQDoI))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(CompassHud$Direction$ConstantPool.const_VGtpfb2gYicGAo9))))
            .defaultValue(ItemListSetting$ConstantPool.const_pIPBDbYFYTOQdvG)
            .min(0.0)
            .sliderMax(CtArray$ConstantPool.const_SYjjW9bqbaOsIYz)
            .build()
      );
   private final Setting<Double> breakWallsRange = this.sgBreak
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Step$ConstantPool.const_jI6Q6Hw1lWLDBjF))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ReverseStep$ConstantPool.const_rONoIXQVAJl9EZv))))
            .defaultValue(ArmorHud$Orientation$ConstantPool.const_ZgsjQMrIeNx8YJS)
            .min(0.0)
            .sliderMax(Tracers$ConstantPool.const_5VOrzbg1A6ArTMI)
            .build()
      );
   private final Setting<Boolean> onlyBreakOwn = this.sgBreak
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AutoSign$ConstantPool.const_tO6aZbjUy3SVIam))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Token$ConstantPool.const_o2gAv8YLg4donJc))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Integer> breakAttempts = this.sgBreak
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(BetterTooltips$DisplayWhen$ConstantPool.const_gTRkiAWjKpDqADl))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(InteractItemEvent$ConstantPool.const_tVqFxkiypGLnByK))))
            .defaultValue(2)
            .sliderMin(1)
            .sliderMax(5)
            .build()
      );
   private final Setting<Integer> ticksExisted = this.sgBreak
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Expr$Binary$ConstantPool.const_nRRFab43dOzy2zb))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ClassPathList$ConstantPool.const_HVSOnW6ePQiLaoA))))
            .defaultValue(0)
            .min(0)
            .build()
      );
   private final Setting<Integer> attackFrequency = this.sgBreak
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(CodeIterator$Jump32$ConstantPool.const_DeY2IW77c6vkPbk))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(NotebotUtils$NotebotMode$ConstantPool.const_beYn9oBtFgtwIzj))))
            .defaultValue(25)
            .min(1)
            .sliderRange(1, 30)
            .build()
      );
   private final Setting<Boolean> fastBreak = this.sgBreak
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ElytraFly$ChestSwapMode$ConstantPool.const_tlbSsiIn4s7NjyV))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(NotebotUtils$OptionalInstrument$ConstantPool.const_7sA8DnOhA0rrVXm))))
            .defaultValue(true)
            .build()
      );
   public final Setting<CrystalAura.PauseMode> pauseOnUse = this.sgPause
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(WaypointsModule$WIcon$ConstantPool.const_ga95madKYZhMmSv)))))
                  .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(TunnelESP$TChunk$ConstantPool.const_D7gmpixisSCIqvq)))))
               .defaultValue(CrystalAura.PauseMode.Place))
            .build()
      );
   public final Setting<CrystalAura.PauseMode> pauseOnMine = this.sgPause
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Profiles$ConstantPool.const_3KVVvrFjfIhNq9n)))))
                  .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(PacketMine$ConstantPool.const_j5BGkOHJiKvG9jK)))))
               .defaultValue(CrystalAura.PauseMode.None))
            .build()
      );
   private final Setting<Boolean> pauseOnLag = this.sgPause
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Compiler$ConstantPool.const_PUjS9nqdVNwOLVN))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ParticleTypeListSetting$Builder$ConstantPool.const_CvdW959JuqSQOxN))))
            .defaultValue(true)
            .build()
      );
   public final Setting<List<Module>> pauseModules = this.sgPause
      .add(
         new ModuleListSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(TabScreen$ConstantPool.const_46WFIgCwdIee0TL))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Nametags$ConstantPool.const_gwPrCHlbIrlMWx4))))
            .defaultValue(BedAura.class)
            .build()
      );
   public final Setting<Double> pauseHealth = this.sgPause
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AlignmentX$ConstantPool.const_Wqqi7Z1Tn6gXIgJ))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(EntityTypeListSettingScreen$ConstantPool.const_edAitF5YFiGWIbC))))
            .defaultValue(AntiPhase$1$ConstantPool.const_EwAEbADukUa0DEE)
            .range(0.0, SocksCmdRequest$1$ConstantPool.const_kAEl5guKwGMqIJo)
            .sliderRange(0.0, SocksCmdRequestDecoder$State$ConstantPool.const_rY2OVOrufd74rh8)
            .build()
      );
   public final Setting<CrystalAura.SwingMode> swingMode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(GuiTheme$ConstantPool.const_8q4ATLOg1LghiZH)))))
                  .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(CustomFontChangedEvent$ConstantPool.const_P1Q2c4oHI0Yjx7g)))))
               .defaultValue(CrystalAura.SwingMode.Both))
            .build()
      );
   private final Setting<CrystalAura.RenderMode> renderMode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Tracer$ConstantPool.const_2dIUIVxcYmaxQz8)))))
                  .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SignatureAttribute$TypeVariable$ConstantPool.const_1qg6YlpukduTgdI)))))
               .defaultValue(CrystalAura.RenderMode.Normal))
            .build()
      );
   private final Setting<Boolean> renderPlace = this.sgRender
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(javassist.compiler.Token$ConstantPool.const_lnfY0LoQ5WNbzIn))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Socks5PasswordAuthRequestDecoder$ConstantPool.const_npp694lBuVF2h90))))
            .defaultValue(true)
            .visible(() -> this.renderMode.get() == CrystalAura.RenderMode.Normal)
            .build()
      );
   private final Setting<Integer> placeRenderTime = this.sgRender
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AntiPacketKick$ConstantPool.const_kFQ7y1dlvvjAOn1))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Shader$ConstantPool.const_LegjQOwWpDrNQY6))))
            .defaultValue(10)
            .min(0)
            .sliderMax(20)
            .visible(() -> this.renderMode.get() == CrystalAura.RenderMode.Normal && this.renderPlace.get())
            .build()
      );
   private final Setting<Boolean> renderBreak = this.sgRender
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Socks4ClientEncoder$ConstantPool.const_YFlnFzgDBbNjLJZ))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SettingGroup$ConstantPool.const_lDJ14LrJ4tnnn7Y))))
            .defaultValue(false)
            .visible(() -> this.renderMode.get() == CrystalAura.RenderMode.Normal)
            .build()
      );
   private final Setting<Integer> breakRenderTime = this.sgRender
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(CodeIterator$If16$ConstantPool.const_ziFDjhb8mzDbfb0))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(CrystalAura$AutoSwitchMode$ConstantPool.const_Mcr1hjTy0HnnajK))))
            .defaultValue(13)
            .min(0)
            .sliderMax(20)
            .visible(() -> this.renderMode.get() == CrystalAura.RenderMode.Normal && this.renderBreak.get())
            .build()
      );
   private final Setting<Integer> smoothness = this.sgRender
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ProfilesTab$EditProfileScreen$ConstantPool.const_voFT6x9N4WNdT9t))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ModuleScreen$ConstantPool.const_gNIouWr98Iq7dwr))))
            .defaultValue(10)
            .min(0)
            .sliderMax(20)
            .visible(() -> this.renderMode.get() == CrystalAura.RenderMode.Smooth)
            .build()
      );
   private final Setting<Double> height = this.sgRender
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(LinkCutTree$ConstantPool.const_qExTbrcgxxr5IUG))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SourceFileAttribute$ConstantPool.const_SeIJebQOFiqJQwo))))
            .defaultValue(FloatMemberValue$ConstantPool.const_T5V72Ui2OnIvjMZ)
            .min(0.0)
            .sliderMax(1.0)
            .visible(() -> this.renderMode.get() == CrystalAura.RenderMode.Gradient)
            .build()
      );
   private final Setting<Integer> renderTime = this.sgRender
      .add(
         new IntSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ArmorHud$Orientation$ConstantPool.const_0FqBRw1vFz42DDW))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Scanners$6$ConstantPool.const_QviMTj8rZxOeiZz))))
            .defaultValue(10)
            .min(0)
            .sliderMax(20)
            .visible(() -> this.renderMode.get() == CrystalAura.RenderMode.Smooth || this.renderMode.get() == CrystalAura.RenderMode.Fading)
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                        .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(LongJump$JumpMode$ConstantPool.const_GO9dgbvF37E9VPt)))))
                     .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(LiquidFiller$ConstantPool.const_yeiIPDeQSbB9gSg)))))
                  .defaultValue(ShapeMode.Both))
               .visible(() -> this.renderMode.get() != CrystalAura.RenderMode.None))
            .build()
      );
   private final Setting<SettingColor> sideColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(InteractBlockEvent$ConstantPool.const_oYNL7dWRaiBltD7))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Socks4ClientDecoder$State$ConstantPool.const_ZmnJNjNyI6nbgYz))))
            .defaultValue(new SettingColor(255, 255, 255, 45))
            .visible(() -> this.shapeMode.get().sides() && this.renderMode.get() != CrystalAura.RenderMode.None)
            .build()
      );
   private final Setting<SettingColor> lineColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(meteordevelopment.meteorclient.asm.Descriptor$ConstantPool.const_ITetCgVnmwZX4oZ))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(FlyOnFirstTime$ConstantPool.const_NHXdXYKPrs6ymxT))))
            .defaultValue(new SettingColor(255, 255, 255))
            .visible(() -> this.shapeMode.get().lines() && this.renderMode.get() != CrystalAura.RenderMode.None)
            .build()
      );
   private final Setting<Boolean> renderDamageText = this.sgRender
      .add(
         new BoolSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(Vfs$File$ConstantPool.const_NoRlA34weQoAeQq))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(BaritoneUtils$ConstantPool.const_SOYgLG4CglOr6ul))))
            .defaultValue(true)
            .visible(() -> this.renderMode.get() != CrystalAura.RenderMode.None)
            .build()
      );
   private final Setting<SettingColor> damageColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ScopedClassPool$ConstantPool.const_LOl4hcP9Q1lQQM4))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SkyLadder$ConstantPool.const_7ifCOKyYVvxinrx))))
            .defaultValue(new SettingColor(255, 255, 255))
            .visible(() -> this.renderMode.get() != CrystalAura.RenderMode.None && this.renderDamageText.get())
            .build()
      );
   private final Setting<Double> damageTextScale = this.sgRender
      .add(
         new DoubleSetting.Builder()
            .name(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(LightOverlay$Spawn$ConstantPool.const_NlPDtowZn3a9lSN))))
            .description(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(ElytraFly$ChestSwapMode$ConstantPool.const_YKuOaUBGud1HaBL))))
            .defaultValue(FontFamily$ConstantPool.const_7sISezI9PTAlVWU)
            .min(1.0)
            .sliderMax(Nametags$1$ConstantPool.const_NJHYyJatDIy1aaF)
            .visible(() -> this.renderMode.get() != CrystalAura.RenderMode.None && this.renderDamageText.get())
            .build()
      );
   private Item mainItem;
   private Item offItem;
   private int breakTimer;
   private int placeTimer;
   private int switchTimer;
   private int ticksPassed;
   private final List<LivingEntity> targets = new ArrayList<>();
   private final Vec3d vec3d = new Vec3d(0.0, 0.0, 0.0);
   private final Vec3d playerEyePos = new Vec3d(0.0, 0.0, 0.0);
   private final Vector3d vec3 = new Vector3d();
   private final Mutable blockPos = new Mutable();
   private final Box box = new Box(0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
   private final Vec3d vec3dRayTraceEnd = new Vec3d(0.0, 0.0, 0.0);
   private RaycastContext raycastContext;
   private final IntSet placedCrystals = new IntOpenHashSet();
   private boolean placing;
   private int placingTimer;
   public int kaTimer;
   private final Mutable placingCrystalBlockPos = new Mutable();
   private final IntSet removed = new IntOpenHashSet();
   private final Int2IntMap attemptedBreaks = new Int2IntOpenHashMap();
   private final Int2IntMap waitingToExplode = new Int2IntOpenHashMap();
   private int attacks;
   private double serverYaw;
   private LivingEntity bestTarget;
   private double bestTargetDamage;
   private int bestTargetTimer;
   private boolean didRotateThisTick;
   private boolean isLastRotationPos;
   private final Vec3d lastRotationPos = new Vec3d(0.0, 0.0, 0.0);
   private double lastYaw;
   private double lastPitch;
   private int lastRotationTimer;
   private int placeRenderTimer;
   private int breakRenderTimer;
   private final Mutable placeRenderPos = new Mutable();
   private final Mutable breakRenderPos = new Mutable();
   private Box renderBoxOne;
   private Box renderBoxTwo;
   private double renderDamage;

   public CrystalAura() {
      super(
         Categories.Combat,
         PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SettingsWidgetFactory$ConstantPool.const_GO0yE9teO4tSSGq))),
         new StringBuilder(PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(DoubleAccountTP$Role$ConstantPool.const_iS9XjJVyo9BrrAz)))),
         PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(AutoTool$ListMode$ConstantPool.const_QoqbR4QnMVVqTMK)))
      );
   }

   @Override
   public void onActivate() {
      this.breakTimer = 0;
      this.placeTimer = 0;
      this.ticksPassed = 0;
      this.raycastContext = new RaycastContext(
         new Vec3d(0.0, 0.0, 0.0), new Vec3d(0.0, 0.0, 0.0), ShapeType.field_17558, FluidHandling.field_1348, mc.field_1724
      );
      this.placing = false;
      this.placingTimer = 0;
      this.kaTimer = 0;
      this.attacks = 0;
      this.serverYaw = mc.field_1724.method_36454();
      this.bestTargetDamage = 0.0;
      this.bestTargetTimer = 0;
      this.lastRotationTimer = this.getLastRotationStopDelay();
      this.placeRenderTimer = 0;
      this.breakRenderTimer = 0;
   }

   @Override
   public void onDeactivate() {
      this.targets.clear();
      this.placedCrystals.clear();
      this.attemptedBreaks.clear();
      this.waitingToExplode.clear();
      this.removed.clear();
      this.bestTarget = null;
   }

   private int getLastRotationStopDelay() {
      return Math.max(10, this.placeDelay.get() / 2 + this.breakDelay.get() / 2 + 10);
   }

   @EventHandler(
      priority = 100
   )
   private void onPreTick(TickEvent.Pre event) {
      this.didRotateThisTick = false;
      this.lastRotationTimer++;
      if (this.placing) {
         if (this.placingTimer > 0) {
            this.placingTimer--;
         } else {
            this.placing = false;
         }
      }

      if (this.kaTimer > 0) {
         this.kaTimer--;
      }

      if (this.ticksPassed < 20) {
         this.ticksPassed++;
      } else {
         this.ticksPassed = 0;
         this.attacks = 0;
      }

      if (this.bestTargetTimer > 0) {
         this.bestTargetTimer--;
      }

      this.bestTargetDamage = 0.0;
      if (this.breakTimer > 0) {
         this.breakTimer--;
      }

      if (this.placeTimer > 0) {
         this.placeTimer--;
      }

      if (this.switchTimer > 0) {
         this.switchTimer--;
      }

      if (this.placeRenderTimer > 0) {
         this.placeRenderTimer--;
      }

      if (this.breakRenderTimer > 0) {
         this.breakRenderTimer--;
      }

      this.mainItem = mc.field_1724.method_6047().method_7909();
      this.offItem = mc.field_1724.method_6079().method_7909();
      IntIterator it = this.waitingToExplode.keySet().iterator();

      while (it.hasNext()) {
         int id = it.nextInt();
         int ticks = this.waitingToExplode.get(id);
         if (ticks > 3) {
            it.remove();
            this.removed.remove(id);
         } else {
            this.waitingToExplode.put(id, ticks + 1);
         }
      }

      ((IVec3d)this.playerEyePos)
         .set(
            mc.field_1724.method_19538().field_1352,
            mc.field_1724.method_19538().field_1351 + mc.field_1724.method_18381(mc.field_1724.method_18376()),
            mc.field_1724.method_19538().field_1350
         );
      this.findTargets();
      if (!this.targets.isEmpty()) {
         if (!this.didRotateThisTick) {
            this.doBreak();
         }

         if (!this.didRotateThisTick) {
            this.doPlace();
         }
      }
   }

   @EventHandler(
      priority = -866
   )
   private void onPreTickLast(TickEvent.Pre event) {
      if (this.rotate.get() && this.lastRotationTimer < this.getLastRotationStopDelay() && !this.didRotateThisTick) {
         Rotations.rotate(
            this.isLastRotationPos ? Rotations.getYaw(this.lastRotationPos) : this.lastYaw,
            this.isLastRotationPos ? Rotations.getPitch(this.lastRotationPos) : this.lastPitch,
            -100,
            null
         );
      }
   }

   @EventHandler
   private void onEntityAdded(EntityAddedEvent event) {
      if (event.entity instanceof EndCrystalEntity) {
         if (this.placing && event.entity.method_24515().equals(this.placingCrystalBlockPos)) {
            this.placing = false;
            this.placingTimer = 0;
            this.placedCrystals.add(event.entity.method_5628());
         }

         if (this.fastBreak.get() && !this.didRotateThisTick && this.attacks < this.attackFrequency.get()) {
            float damage = this.getBreakDamage(event.entity, true);
            if (damage > this.minDamage.get()) {
               this.doBreak(event.entity);
            }
         }
      }
   }

   @EventHandler
   private void onEntityRemoved(EntityRemovedEvent event) {
      if (event.entity instanceof EndCrystalEntity) {
         this.placedCrystals.remove(event.entity.method_5628());
         this.removed.remove(event.entity.method_5628());
         this.waitingToExplode.remove(event.entity.method_5628());
      }
   }

   private void setRotation(boolean isPos, Vec3d pos, double yaw, double pitch) {
      this.didRotateThisTick = true;
      this.isLastRotationPos = isPos;
      if (isPos) {
         ((IVec3d)this.lastRotationPos).set(pos.field_1352, pos.field_1351, pos.field_1350);
      } else {
         this.lastYaw = yaw;
         this.lastPitch = pitch;
      }

      this.lastRotationTimer = 0;
   }

   private void doBreak() {
      if (this.doBreak.get() && this.breakTimer <= 0 && this.switchTimer <= 0 && this.attacks < this.attackFrequency.get()) {
         if (!this.shouldPause(CrystalAura.PauseMode.Break)) {
            float bestDamage = 0.0F;
            Entity crystal = null;

            for (Entity entity : mc.field_1687.method_18112()) {
               float damage = this.getBreakDamage(entity, true);
               if (damage > bestDamage) {
                  bestDamage = damage;
                  crystal = entity;
               }
            }

            if (crystal != null) {
               this.doBreak(crystal);
            }
         }
      }
   }

   private float getBreakDamage(Entity entity, boolean checkCrystalAge) {
      if (!(entity instanceof EndCrystalEntity)) {
         return 0.0F;
      } else if (this.onlyBreakOwn.get() && !this.placedCrystals.contains(entity.method_5628())) {
         return 0.0F;
      } else if (this.removed.contains(entity.method_5628())) {
         return 0.0F;
      } else if (this.attemptedBreaks.get(entity.method_5628()) > this.breakAttempts.get()) {
         return 0.0F;
      } else if (checkCrystalAge && entity.field_6012 < this.ticksExisted.get()) {
         return 0.0F;
      } else if (this.isOutOfRange(entity.method_19538(), entity.method_24515(), false)) {
         return 0.0F;
      } else {
         this.blockPos.method_10101(entity.method_24515()).method_10100(0, -1, 0);
         float selfDamage = DamageUtils.crystalDamage(mc.field_1724, entity.method_19538(), this.predictMovement.get(), this.blockPos);
         if (!(selfDamage > this.maxDamage.get()) && (!this.antiSuicide.get() || !(selfDamage >= EntityUtils.getTotalHealth(mc.field_1724)))) {
            float damage = this.getDamageToTargets(entity.method_19538(), this.blockPos, true, false);
            boolean shouldFacePlace = this.shouldFacePlace();
            double minimumDamage = shouldFacePlace
               ? Math.min(this.minDamage.get(), PacketListSetting$Builder$ConstantPool.const_yvjYDGk25w6kkvt)
               : this.minDamage.get();
            return damage < minimumDamage ? 0.0F : damage;
         } else {
            return 0.0F;
         }
      }
   }

   private void doBreak(Entity crystal) {
      if (this.antiWeakness.get()) {
         StatusEffectInstance weakness = mc.field_1724.method_6112(StatusEffects.field_5911);
         StatusEffectInstance strength = mc.field_1724.method_6112(StatusEffects.field_5910);
         if (weakness != null
            && (strength == null || strength.method_5578() <= weakness.method_5578())
            && !this.isValidWeaknessItem(mc.field_1724.method_6047())) {
            if (!InvUtils.swap(InvUtils.findInHotbar(this::isValidWeaknessItem).slot(), false)) {
               return;
            }

            this.switchTimer = 1;
            return;
         }
      }

      boolean attacked = true;
      if (this.rotate.get()) {
         double yaw = Rotations.getYaw(crystal);
         double pitch = Rotations.getPitch(crystal, Target.Feet);
         if (this.doYawSteps(yaw, pitch)) {
            this.setRotation(true, crystal.method_19538(), 0.0, 0.0);
            Rotations.rotate(yaw, pitch, 50, () -> this.attackCrystal(crystal));
            this.breakTimer = this.breakDelay.get();
         } else {
            attacked = false;
         }
      } else {
         this.attackCrystal(crystal);
         this.breakTimer = this.breakDelay.get();
      }

      if (attacked) {
         this.removed.add(crystal.method_5628());
         this.attemptedBreaks.put(crystal.method_5628(), this.attemptedBreaks.get(crystal.method_5628()) + 1);
         this.waitingToExplode.put(crystal.method_5628(), 0);
         this.breakRenderPos.method_10101(crystal.method_24515().method_10074());
         this.breakRenderTimer = this.breakRenderTime.get();
      }
   }

   private boolean isValidWeaknessItem(ItemStack itemStack) {
      if (itemStack.method_7909() instanceof ToolItem && !(itemStack.method_7909() instanceof HoeItem)) {
         ToolMaterial material = ((ToolItem)itemStack.method_7909()).method_8022();
         return material == ToolMaterials.field_8930 || material == ToolMaterials.field_22033;
      } else {
         return false;
      }
   }

   private void attackCrystal(Entity entity) {
      mc.field_1724.field_3944.method_52787(PlayerInteractEntityC2SPacket.method_34206(entity, mc.field_1724.method_5715()));
      Hand hand = InvUtils.findInHotbar(Items.field_8301).getHand();
      if (hand == null) {
         hand = Hand.field_5808;
      }

      if (this.swingMode.get().client()) {
         mc.field_1724.method_6104(hand);
      }

      if (this.swingMode.get().packet()) {
         mc.method_1562().method_52787(new HandSwingC2SPacket(hand));
      }

      this.attacks++;
   }

   @EventHandler
   private void onPacketSend(PacketEvent.Send event) {
      if (event.packet instanceof UpdateSelectedSlotC2SPacket) {
         this.switchTimer = this.switchDelay.get();
      }
   }

   private void doPlace() {
      if (this.doPlace.get() && this.placeTimer <= 0) {
         if (!this.shouldPause(CrystalAura.PauseMode.Place)) {
            if (InvUtils.testInHotbar(Items.field_8301)) {
               if (this.autoSwitch.get() != CrystalAura.AutoSwitchMode.None) {
                  if (this.noGapSwitch.get()
                     && this.autoSwitch.get() == CrystalAura.AutoSwitchMode.Normal
                     && this.offItem != Items.field_8301
                     && (
                        this.mainItem == Items.field_8367
                           || this.offItem == Items.field_8367
                           || this.mainItem == Items.field_8463
                           || this.offItem == Items.field_8463
                     )) {
                     return;
                  }

                  if (this.noBowSwitch.get() && (this.mainItem == Items.field_8102 || this.offItem == Items.field_8102)) {
                     return;
                  }
               } else if (this.mainItem != Items.field_8301 && this.offItem != Items.field_8301) {
                  return;
               }

               for (Entity entity : mc.field_1687.method_18112()) {
                  if (this.getBreakDamage(entity, false) > 0.0F) {
                     return;
                  }
               }

               AtomicDouble bestDamage = new AtomicDouble(0.0);
               AtomicReference<Mutable> bestBlockPos = new AtomicReference<>(new Mutable());
               AtomicBoolean isSupport = new AtomicBoolean(this.support.get() != CrystalAura.SupportMode.Disabled);
               BlockIterator.register(
                  (int)Math.ceil(this.placeRange.get()),
                  (int)Math.ceil(this.placeRange.get()),
                  (bp, blockState) -> {
                     boolean hasBlock = blockState.method_27852(Blocks.field_9987) || blockState.method_27852(Blocks.field_10540);
                     if (hasBlock || isSupport.get() && blockState.method_45474()) {
                        this.blockPos.method_10103(bp.method_10263(), bp.method_10264() + 1, bp.method_10260());
                        if (mc.field_1687.method_8320(this.blockPos).method_26215()) {
                           if (this.placement112.get()) {
                              this.blockPos.method_10100(0, 1, 0);
                              if (!mc.field_1687.method_8320(this.blockPos).method_26215()) {
                                 return;
                              }
                           }

                           ((IVec3d)this.vec3d)
                              .set(
                                 bp.method_10263() + RichPresence$Assets$ConstantPool.const_ohvd9gvB9AJX4dT,
                                 bp.method_10264() + 1,
                                 bp.method_10260() + StatusEffectListSetting$Builder$ConstantPool.const_dqMuTDqtU1Yzmvk
                              );
                           this.blockPos.method_10101(bp).method_10100(0, 1, 0);
                           if (!this.isOutOfRange(this.vec3d, this.blockPos, true)) {
                              float selfDamage = DamageUtils.crystalDamage(mc.field_1724, this.vec3d, this.predictMovement.get(), bp);
                              if (!(selfDamage > this.maxDamage.get())
                                 && (!this.antiSuicide.get() || !(selfDamage >= EntityUtils.getTotalHealth(mc.field_1724)))) {
                                 float damage = this.getDamageToTargets(this.vec3d, bp, false, !hasBlock && this.support.get() == CrystalAura.SupportMode.Fast);
                                 boolean shouldFacePlace = this.shouldFacePlace();
                                 double minimumDamage = Math.min(
                                    this.minDamage.get(), shouldFacePlace ? HudBox$ConstantPool.const_y5xIgtFnHQ4fiIv : this.minDamage.get()
                                 );
                                 if (!(damage < minimumDamage)) {
                                    double x = bp.method_10263();
                                    double y = bp.method_10264() + 1;
                                    double z = bp.method_10260();
                                    ((IBox)this.box).set(x, y, z, x + 1.0, y + (this.placement112.get() ? 1 : 2), z + 1.0);
                                    if (!this.intersectsWithEntities(this.box)) {
                                       if (damage > bestDamage.get() || isSupport.get() && hasBlock) {
                                          bestDamage.set(damage);
                                          bestBlockPos.get().method_10101(bp);
                                       }

                                       if (hasBlock) {
                                          isSupport.set(false);
                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               );
               BlockIterator.after(
                  () -> {
                     if (bestDamage.get() != 0.0) {
                        BlockHitResult result = this.getPlaceInfo((BlockPos)bestBlockPos.get());
                        ((IVec3d)this.vec3d)
                           .set(
                              result.method_17777().method_10263()
                                 + DefinePackageHelper$Helper$ConstantPool.const_v8CAgnjvBiA7zST
                                 + result.method_17780().method_10163().method_10263() * 1.0 / Sphere2dMarker$ConstantPool.const_GTgKjU3prBDbpmd,
                              result.method_17777().method_10264()
                                 + Configuration$ConstantPool.const_uItr4TtLBbtG44i
                                 + result.method_17780().method_10163().method_10264()
                                    * 1.0
                                    / HighwayBuilder$StraightBlockPosProvider$5$ConstantPool.const_Y9SUSAzAGxSrXVb,
                              result.method_17777().method_10260()
                                 + AutoArmor$ArmorPiece$ConstantPool.const_a1hi6FTtNuny9Oh
                                 + result.method_17780().method_10163().method_10260() * 1.0 / AutoJump$JumpWhen$ConstantPool.const_VGbr4u19DdqJ0jr
                           );
                        if (this.rotate.get()) {
                           double yaw = Rotations.getYaw(this.vec3d);
                           double pitch = Rotations.getPitch(this.vec3d);
                           if (this.yawStepMode.get() == CrystalAura.YawStepMode.Break || this.doYawSteps(yaw, pitch)) {
                              this.setRotation(true, this.vec3d, 0.0, 0.0);
                              Rotations.rotate(
                                 yaw, pitch, 50, () -> this.placeCrystal(result, bestDamage.get(), isSupport.get() ? (BlockPos)bestBlockPos.get() : null)
                              );
                              this.placeTimer = this.placeTimer + this.placeDelay.get();
                           }
                        } else {
                           this.placeCrystal(result, bestDamage.get(), isSupport.get() ? (BlockPos)bestBlockPos.get() : null);
                           this.placeTimer = this.placeTimer + this.placeDelay.get();
                        }
                     }
                  }
               );
            }
         }
      }
   }

   private BlockHitResult getPlaceInfo(BlockPos blockPos) {
      ((IVec3d)this.vec3d)
         .set(
            mc.field_1724.method_23317(), mc.field_1724.method_23318() + mc.field_1724.method_18381(mc.field_1724.method_18376()), mc.field_1724.method_23321()
         );

      for (Direction side : Direction.values()) {
         ((IVec3d)this.vec3dRayTraceEnd)
            .set(
               blockPos.method_10263()
                  + Waypoints$WaypointIterator$ConstantPool.const_4PgI4PiWH2om5NY
                  + side.method_10163().method_10263() * OkPrompt$ConstantPool.const_rTnqBEi2rMZ5bwG,
               blockPos.method_10264()
                  + ModulesScreen$ConstantPool.const_qAaIBojseKNUOFL
                  + side.method_10163().method_10264() * NotFoundException$ConstantPool.const_LvfrQvYnwhOtuJg,
               blockPos.method_10260()
                  + NotebotUtils$ConstantPool.const_Wa9xNr6dvUOCrer
                  + side.method_10163().method_10260() * ProxyType$ConstantPool.const_OwrWbyvob6ZNYC6
            );
         ((IRaycastContext)this.raycastContext).set(this.vec3d, this.vec3dRayTraceEnd, ShapeType.field_17558, FluidHandling.field_1348, mc.field_1724);
         BlockHitResult result = mc.field_1687.method_17742(this.raycastContext);
         if (result != null && result.method_17783() == Type.field_1332 && result.method_17777().equals(blockPos)) {
            return result;
         }
      }

      Direction sidex = blockPos.method_10264() > this.vec3d.field_1351 ? Direction.field_11033 : Direction.field_11036;
      return new BlockHitResult(this.vec3d, sidex, blockPos, false);
   }

   private void placeCrystal(BlockHitResult result, double damage, BlockPos supportBlock) {
      Item targetItem = supportBlock == null ? Items.field_8301 : Items.field_8281;
      FindItemResult item = InvUtils.findInHotbar(targetItem);
      if (item.found()) {
         int prevSlot = mc.field_1724.method_31548().field_7545;
         if (this.autoSwitch.get() != CrystalAura.AutoSwitchMode.None && !item.isOffhand()) {
            InvUtils.swap(item.slot(), false);
         }

         Hand hand = item.getHand();
         if (hand != null) {
            if (supportBlock == null) {
               mc.field_1724.field_3944.method_52787(new PlayerInteractBlockC2SPacket(hand, result, 0));
               if (this.swingMode.get().client()) {
                  mc.field_1724.method_6104(hand);
               }

               if (this.swingMode.get().packet()) {
                  mc.method_1562().method_52787(new HandSwingC2SPacket(hand));
               }

               this.placing = true;
               this.placingTimer = 4;
               this.kaTimer = 8;
               this.placingCrystalBlockPos.method_10101(result.method_17777()).method_10100(0, 1, 0);
               this.placeRenderPos.method_10101(result.method_17777());
               this.renderDamage = damage;
               if (this.renderMode.get() == CrystalAura.RenderMode.Normal) {
                  this.placeRenderTimer = this.placeRenderTime.get();
               } else {
                  this.placeRenderTimer = this.renderTime.get();
                  if (this.renderMode.get() == CrystalAura.RenderMode.Fading) {
                     RenderUtils.renderTickingBlock(
                        this.placeRenderPos, this.sideColor.get(), this.lineColor.get(), this.shapeMode.get(), 0, this.renderTime.get(), true, false
                     );
                  }
               }
            } else {
               BlockUtils.place(supportBlock, item, false, 0, this.swingMode.get().client(), true, false);
               this.placeTimer = this.placeTimer + this.supportDelay.get();
               if (this.supportDelay.get() == 0) {
                  this.placeCrystal(result, damage, null);
               }
            }

            if (this.autoSwitch.get() == CrystalAura.AutoSwitchMode.Silent) {
               InvUtils.swap(prevSlot, false);
            }
         }
      }
   }

   @EventHandler
   private void onPacketSent(PacketEvent.Sent event) {
      if (event.packet instanceof PlayerMoveC2SPacket) {
         this.serverYaw = ((PlayerMoveC2SPacket)event.packet).method_12271((float)this.serverYaw);
      }
   }

   public boolean doYawSteps(double targetYaw, double targetPitch) {
      targetYaw = MathHelper.method_15338(targetYaw) + Macros$ConstantPool.const_JIvBf0kgkS34VNG;
      double serverYaw = MathHelper.method_15338(this.serverYaw) + NbtUtils$ToKey$ConstantPool.const_EjqtSnp2Tv7jLKg;
      if (distanceBetweenAngles(serverYaw, targetYaw) <= this.yawSteps.get()) {
         return true;
      } else {
         double delta = Math.abs(targetYaw - serverYaw);
         double yaw = this.serverYaw;
         if (serverYaw < targetYaw) {
            if (delta < HudRenderer$ConstantPool.const_3udKeeeMgZjBOPr) {
               yaw += this.yawSteps.get();
            } else {
               yaw -= this.yawSteps.get();
            }
         } else if (delta < JbossDir$1$ConstantPool.const_YZwiFb21IDBgI8A) {
            yaw -= this.yawSteps.get();
         } else {
            yaw += this.yawSteps.get();
         }

         this.setRotation(false, null, yaw, targetPitch);
         Rotations.rotate(yaw, targetPitch, -100, null);
         return false;
      }
   }

   private static double distanceBetweenAngles(double alpha, double beta) {
      double phi = Math.abs(beta - alpha) % ElytraFly$1$ConstantPool.const_jiqRDtOw0N6pcTd;
      return phi > Modifier$ConstantPool.const_6oLTZObfIwp04Gw ? SettingGroup$ConstantPool.const_ATgofgBGd9Oanof - phi : phi;
   }

   private boolean shouldFacePlace() {
      if (!this.facePlace.get()) {
         return false;
      } else if (this.forceFacePlace.get().isPressed()) {
         return true;
      } else {
         for (LivingEntity target : this.targets) {
            if (EntityUtils.getTotalHealth(target) <= this.facePlaceHealth.get()) {
               return true;
            }

            for (ItemStack itemStack : target.method_5661()) {
               if (itemStack != null && !itemStack.method_7960()) {
                  if ((double)(itemStack.method_7936() - itemStack.method_7919())
                        / itemStack.method_7936()
                        * AnchorAura$PlaceMode$ConstantPool.const_710ivVAo9Ikg70y
                     <= this.facePlaceDurability.get()) {
                     return true;
                  }
               } else if (this.facePlaceArmor.get()) {
                  return true;
               }
            }
         }

         return false;
      }
   }

   private boolean shouldPause(CrystalAura.PauseMode process) {
      if ((mc.field_1724.method_6115() || mc.field_1690.field_1904.method_1434()) && this.pauseOnUse.get().equals(process)) {
         return true;
      } else if (this.pauseOnLag.get() && TickRate.INSTANCE.getTimeSinceLastTick() >= 1.0F) {
         return true;
      } else {
         for (Module module : this.pauseModules.get()) {
            if (module.isActive()) {
               return true;
            }
         }

         return this.pauseOnMine.get().equals(process) && mc.field_1761.method_2923()
            ? true
            : EntityUtils.getTotalHealth(mc.field_1724) <= this.pauseHealth.get();
      }
   }

   private boolean isOutOfRange(Vec3d vec3d, BlockPos blockPos, boolean place) {
      ((IRaycastContext)this.raycastContext).set(this.playerEyePos, vec3d, ShapeType.field_17558, FluidHandling.field_1348, mc.field_1724);
      BlockHitResult result = mc.field_1687.method_17742(this.raycastContext);
      return result != null && result.method_17777().equals(blockPos)
         ? !PlayerUtils.isWithin(vec3d, (place ? this.placeRange : this.breakRange).get())
         : !PlayerUtils.isWithin(vec3d, (place ? this.placeWallsRange : this.breakWallsRange).get());
   }

   private LivingEntity getNearestTarget() {
      LivingEntity nearestTarget = null;
      double nearestDistance = ExceptionsAttribute$ConstantPool.const_AFCNqTTYzynbz95;

      for (LivingEntity target : this.targets) {
         double distance = PlayerUtils.squaredDistanceTo(target);
         if (distance < nearestDistance) {
            nearestTarget = target;
            nearestDistance = distance;
         }
      }

      return nearestTarget;
   }

   private float getDamageToTargets(Vec3d vec3d, BlockPos obsidianPos, boolean breaking, boolean fast) {
      float damage = 0.0F;
      if (fast) {
         LivingEntity target = this.getNearestTarget();
         if (!this.smartDelay.get() || !breaking || target.field_6235 <= 0) {
            damage = DamageUtils.crystalDamage(target, vec3d, this.predictMovement.get(), obsidianPos);
         }
      } else {
         for (LivingEntity target : this.targets) {
            if (!this.smartDelay.get() || !breaking || target.field_6235 <= 0) {
               float dmg = DamageUtils.crystalDamage(target, vec3d, this.predictMovement.get(), obsidianPos);
               if (dmg > this.bestTargetDamage) {
                  this.bestTarget = target;
                  this.bestTargetDamage = dmg;
                  this.bestTargetTimer = 10;
               }

               damage += dmg;
            }
         }
      }

      return damage;
   }

   @Override
   public String getInfoString() {
      return this.bestTarget != null && this.bestTargetTimer > 0 ? EntityUtils.getName(this.bestTarget) : null;
   }

   private void findTargets() {
      this.targets.clear();

      for (Entity entity : mc.field_1687.method_18112()) {
         if (entity instanceof LivingEntity livingEntity
            && !(
               livingEntity instanceof PlayerEntity player
                  && (
                     player.method_31549().field_7477
                        || livingEntity == mc.field_1724
                        || !player.method_5805()
                        || !Friends.get().shouldAttack(player)
                        || this.ignoreNakeds.get()
                           && player.method_6079().method_7960()
                           && player.method_6047().method_7960()
                           && ((ItemStack)player.method_31548().field_7548.get(0)).method_7960()
                           && ((ItemStack)player.method_31548().field_7548.get(1)).method_7960()
                           && ((ItemStack)player.method_31548().field_7548.get(2)).method_7960()
                           && ((ItemStack)player.method_31548().field_7548.get(3)).method_7960()
                  )
            )
            && this.entities.get().contains(livingEntity.method_5864())
            && !(livingEntity.method_5858(mc.field_1724) > this.targetRange.get() * this.targetRange.get())) {
            this.targets.add(livingEntity);
         }
      }
   }

   private boolean intersectsWithEntities(Box box) {
      return EntityUtils.intersectsWithEntity(box, entity -> !entity.method_7325() && !this.removed.contains(entity.method_5628()));
   }

   @EventHandler
   private void onRender(Render3DEvent event) {
      if (this.renderMode.get() != CrystalAura.RenderMode.None) {
         switch ((CrystalAura.RenderMode)this.renderMode.get()) {
            case Normal:
               if (this.renderPlace.get() && this.placeRenderTimer > 0) {
                  event.renderer.box(this.placeRenderPos, this.sideColor.get(), this.lineColor.get(), this.shapeMode.get(), 0);
               }

               if (this.renderBreak.get() && this.breakRenderTimer > 0) {
                  event.renderer.box(this.breakRenderPos, this.sideColor.get(), this.lineColor.get(), this.shapeMode.get(), 0);
               }
               break;
            case Smooth:
               if (this.placeRenderTimer <= 0) {
                  return;
               }

               if (this.renderBoxOne == null) {
                  this.renderBoxOne = new Box(this.placeRenderPos);
               }

               if (this.renderBoxTwo == null) {
                  this.renderBoxTwo = new Box(this.placeRenderPos);
               } else {
                  ((IBox)this.renderBoxTwo).set(this.placeRenderPos);
               }

               double offsetX = (this.renderBoxTwo.field_1323 - this.renderBoxOne.field_1323) / this.smoothness.get().intValue();
               double offsetY = (this.renderBoxTwo.field_1322 - this.renderBoxOne.field_1322) / this.smoothness.get().intValue();
               double offsetZ = (this.renderBoxTwo.field_1321 - this.renderBoxOne.field_1321) / this.smoothness.get().intValue();
               ((IBox)this.renderBoxOne)
                  .set(
                     this.renderBoxOne.field_1323 + offsetX,
                     this.renderBoxOne.field_1322 + offsetY,
                     this.renderBoxOne.field_1321 + offsetZ,
                     this.renderBoxOne.field_1320 + offsetX,
                     this.renderBoxOne.field_1325 + offsetY,
                     this.renderBoxOne.field_1324 + offsetZ
                  );
               event.renderer.box(this.renderBoxOne, this.sideColor.get(), this.lineColor.get(), this.shapeMode.get(), 0);
            case Fading:
            default:
               break;
            case Gradient:
               if (this.placeRenderTimer <= 0) {
                  return;
               }

               Color bottom = new Color(0, 0, 0, 0);
               int x = this.placeRenderPos.method_10263();
               int y = this.placeRenderPos.method_10264() + 1;
               int z = this.placeRenderPos.method_10260();
               if (this.shapeMode.get().sides()) {
                  event.renderer.quadHorizontal(x, y, z, x + 1, z + 1, this.sideColor.get());
                  event.renderer.gradientQuadVertical(x, y, z, x + 1, y - this.height.get(), z, bottom, this.sideColor.get());
                  event.renderer.gradientQuadVertical(x, y, z, x, y - this.height.get(), z + 1, bottom, this.sideColor.get());
                  event.renderer.gradientQuadVertical(x + 1, y, z, x + 1, y - this.height.get(), z + 1, bottom, this.sideColor.get());
                  event.renderer.gradientQuadVertical(x, y, z + 1, x + 1, y - this.height.get(), z + 1, bottom, this.sideColor.get());
               }

               if (this.shapeMode.get().lines()) {
                  event.renderer.line(x, y, z, x + 1, y, z, this.lineColor.get());
                  event.renderer.line(x, y, z, x, y, z + 1, this.lineColor.get());
                  event.renderer.line(x + 1, y, z, x + 1, y, z + 1, this.lineColor.get());
                  event.renderer.line(x, y, z + 1, x + 1, y, z + 1, this.lineColor.get());
                  event.renderer.line(x, y, z, x, y - this.height.get(), z, this.lineColor.get(), bottom);
                  event.renderer.line(x + 1, y, z, x + 1, y - this.height.get(), z, this.lineColor.get(), bottom);
                  event.renderer.line(x, y, z + 1, x, y - this.height.get(), z + 1, this.lineColor.get(), bottom);
                  event.renderer.line(x + 1, y, z + 1, x + 1, y - this.height.get(), z + 1, this.lineColor.get(), bottom);
               }
         }
      }
   }

   @EventHandler
   private void onRender2D(Render2DEvent event) {
      if (this.renderMode.get() != CrystalAura.RenderMode.None && this.renderDamageText.get()) {
         if (this.placeRenderTimer > 0 || this.breakRenderTimer > 0) {
            if (this.renderMode.get() == CrystalAura.RenderMode.Smooth) {
               if (this.renderBoxOne == null) {
                  return;
               }

               this.vec3
                  .set(
                     this.renderBoxOne.field_1323 + CtMethod$ConstantPool.const_6K9YHYrhq5qVcWO,
                     this.renderBoxOne.field_1322 + ExportedObject$ConstantPool.const_QjYj6K9hL4CdF4B,
                     this.renderBoxOne.field_1321 + EmptyIterator$ConstantPool.const_nEYG46utLieyyfX
                  );
            } else {
               this.vec3
                  .set(
                     this.placeRenderPos.method_10263() + BlockSetting$ConstantPool.const_01OvIipTL8kuNgo,
                     this.placeRenderPos.method_10264() + CondExpr$ConstantPool.const_ggBgF99b0nYYVDG,
                     this.placeRenderPos.method_10260() + WHorizontalSeparator$ConstantPool.const_oEyTQqOVsm8o9rw
                  );
            }

            if (NametagUtils.to2D(this.vec3, this.damageTextScale.get())) {
               NametagUtils.begin(this.vec3);
               TextRenderer.get().begin(1.0, false, true);
               String text = String.format(
                  PrUWNXOQ6S(7o9WZd0XZo(YwPWITQU2Z(SignatureAttribute$BaseType$ConstantPool.const_Ats2cmlrnGrbB8o))), this.renderDamage
               );
               double w = TextRenderer.get().getWidth(text) / DefaultSettingsWidgetFactory$WSelectedCountLabel$ConstantPool.const_9inYhVmwFANjWit;
               TextRenderer.get().render(text, -w, 0.0, this.damageColor.get(), true);
               TextRenderer.get().end();
               NametagUtils.end();
            }
         }
      }
   }

   public static enum AutoSwitchMode {
      Normal,
      Silent,
      None;
   }

   public static enum PauseMode {
      Both,
      Place,
      Break,
      None;

      public boolean equals(CrystalAura.PauseMode process) {
         return this == process || this == Both;
      }
   }

   public static enum RenderMode {
      Normal,
      Smooth,
      Fading,
      Gradient,
      None;
   }

   public static enum SupportMode {
      Disabled,
      Accurate,
      Fast;
   }

   public static enum SwingMode {
      Both,
      Packet,
      Client,
      None;

      public boolean packet() {
         return this == Packet || this == Both;
      }

      public boolean client() {
         return this == Client || this == Both;
      }
   }

   public static enum YawStepMode {
      Break,
      All;
   }
}
