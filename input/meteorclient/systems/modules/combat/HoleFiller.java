package meteordevelopment.meteorclient.systems.modules.combat;

import de.florianmichael.waybackauthlib.WaybackAuthLib$Response$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5CommandRequest$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5CommandStatus$ConstantPool;
import java.util.ArrayList;
import java.util.List;
import javassist.bytecode.CodeAttribute$ConstantPool;
import javassist.bytecode.CodeIterator$Pointers$ConstantPool;
import javassist.bytecode.CodeIterator$Table$ConstantPool;
import javassist.bytecode.LongInfo$ConstantPool;
import javassist.bytecode.Opcode$ConstantPool;
import javassist.bytecode.analysis.Util$ConstantPool;
import javassist.bytecode.stackmap.TypedBlock$ConstantPool;
import javassist.expr.Instanceof$ConstantPool;
import javassist.util.proxy.DefineClassHelper$1$ConstantPool;
import meteordevelopment.meteorclient.asm.Asm$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.render.Render3DEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.renderer.Scissor$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.operations.TextOperation$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.SoundEventListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.input.WMeteorDropdown$WValue$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorPlus$ConstantPool;
import meteordevelopment.meteorclient.mixin.AbstractBlockAccessor;
import meteordevelopment.meteorclient.mixininterface.IAbstractFurnaceScreenHandler$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IBox;
import meteordevelopment.meteorclient.mixininterface.IChatHudLineVisible$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IItemEntity$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IPlayerMoveC2SPacket$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.BlockListSetting;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.KeybindSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$AuthTokenResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.friends.Friends;
import meteordevelopment.meteorclient.systems.hud.elements.CompassHud$Direction$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Module$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoPotion$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.BlockType$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AutoJump$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes.Vanilla$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.FreeLook$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.AirPlace$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.VeinMiner$ListMode$ConstantPool;
import meteordevelopment.meteorclient.systems.profiles.Profile$ConstantPool;
import meteordevelopment.meteorclient.utils.files.StreamUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.ComponentMapReader$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.Keybind;
import meteordevelopment.meteorclient.utils.misc.NbtUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.other.MemoryUtil$ConstantPool;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.InvUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.render.postprocess.PostProcessShaders$ConstantPool;
import meteordevelopment.meteorclient.utils.tooltip.BannerTooltipComponent$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockIterator;
import meteordevelopment.meteorclient.utils.world.BlockIterator$Callback$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.Dir;
import meteordevelopment.meteorclient.utils.world.Timer$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.EventPriority$ConstantPool;
import meteordevelopment.starscript.Starscript$1$ConstantPool;
import meteordevelopment.starscript.compiler.Compiler$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Null$ConstantPool;
import meteordevelopment.starscript.utils.Stack$ConstantPool;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.entity.TntEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos.Mutable;
import org.reflections.Configuration$ConstantPool;
import org.reflections.scanners.AbstractScanner$ConstantPool;
import org.reflections.scanners.Scanners$6$ConstantPool;
import org.reflections.util.QueryBuilder$ConstantPool;
import org.reflections.vfs.Vfs$Dir$ConstantPool;

public class HoleFiller extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgSmart = this.settings.createGroup(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Timer$ConstantPool.const_CjEqEnnQlwc2BaS))));
   private final SettingGroup sgRender = this.settings.createGroup(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Compiler$ConstantPool.const_GwBreqJWSVbZ2Zj))));
   private final Setting<List<Block>> blocks = this.sgGeneral
      .add(
         new BlockListSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(PostProcessShaders$ConstantPool.const_sRDq4SGOmzdRpyg))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Configuration$ConstantPool.const_FWQtrGVuFWeAwNe))))
            .defaultValue(Blocks.field_10540, Blocks.field_22423, Blocks.field_22108, Blocks.field_23152, Blocks.field_10343)
            .build()
      );
   private final Setting<Integer> searchRadius = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(WMeteorPlus$ConstantPool.const_DxOIQeI8w4aeuPv))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(FreeLook$Mode$ConstantPool.const_9wrBRWaAhdbrRLL))))
            .defaultValue(5)
            .min(0)
            .sliderMax(6)
            .build()
      );
   private final Setting<Double> placeRange = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(BlockType$ConstantPool.const_1n8S2p2N14YlCoS))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Stack$ConstantPool.const_9ZlrtWtF6AAKTdw))))
            .defaultValue(CompassHud$Direction$ConstantPool.const_HNQp0lIj2MbKcqz)
            .min(0.0)
            .sliderMax(Asm$ConstantPool.const_fCIui52FjAB49TG)
            .build()
      );
   private final Setting<Boolean> doubles = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(SelfTrap$ConstantPool.const_W0Dgfc7httqvSs7))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(IAbstractFurnaceScreenHandler$ConstantPool.const_H4yibG3T6AnI7pV))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> rotate = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Opcode$ConstantPool.const_gWyjxQlIie1edy7))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Scanners$6$ConstantPool.const_6WLzspES5NLjaqQ))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Integer> placeDelay = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Module$ConstantPool.const_hN72O9txNtjkFnv))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(ComponentMapReader$ConstantPool.const_D4TkkDTry2fRZlD))))
            .defaultValue(1)
            .min(0)
            .build()
      );
   private final Setting<Integer> blocksPerTick = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(TypedBlock$ConstantPool.const_V4OmB61eTeRpyBn))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(AbstractScanner$ConstantPool.const_J6T6eJdtotdX7Jo))))
            .defaultValue(3)
            .min(1)
            .build()
      );
   private final Setting<Boolean> smart = this.sgSmart
      .add(
         new BoolSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(TextOperation$ConstantPool.const_1WariAtYZoAOpQQ))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(NbtUtils$ConstantPool.const_nIiP4zbaqoy97Ov))))
            .defaultValue(true)
            .build()
      );
   public final Setting<Keybind> forceFill = this.sgSmart
      .add(
         new KeybindSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(IItemEntity$ConstantPool.const_yBAgsoAJBescAoR))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(WaybackAuthLib$Response$ConstantPool.const_Cw0tS9TnH0TD6lQ))))
            .defaultValue(Keybind.none())
            .visible(this.smart::get)
            .build()
      );
   private final Setting<Boolean> predict = this.sgSmart
      .add(
         new BoolSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(AutoPotion$ConstantPool.const_qpN967ed4Bj879F))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Profile$ConstantPool.const_RXHATCF2eG7ibJW))))
            .defaultValue(true)
            .visible(this.smart::get)
            .build()
      );
   private final Setting<Boolean> ignoreSafe = this.sgSmart
      .add(
         new BoolSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Util$ConstantPool.const_F8vWNRuDBLRiRba))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(DefineClassHelper$1$ConstantPool.const_mn2NtjMf7ZOX9yz))))
            .defaultValue(true)
            .visible(this.smart::get)
            .build()
      );
   private final Setting<Boolean> onlyMoving = this.sgSmart
      .add(
         new BoolSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(AutoWeapon$Weapon$ConstantPool.const_BgTN8LB2PxrQRwe))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Socks5CommandStatus$ConstantPool.const_bHi3FpxOMO4J1NS))))
            .defaultValue(true)
            .visible(this.smart::get)
            .build()
      );
   private final Setting<Double> targetRange = this.sgSmart
      .add(
         new DoubleSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(AutoJump$Mode$ConstantPool.const_MtlnRTjNev61rTB))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(StreamUtils$ConstantPool.const_7My2NEjv5PsOqrS))))
            .defaultValue(MicrosoftLogin$AuthTokenResponse$ConstantPool.const_ANGSWZ3TzNGtH2a)
            .min(0.0)
            .sliderMin(1.0)
            .sliderMax(Scanners$6$ConstantPool.const_sZO9y4QTBECbvwV)
            .visible(this.smart::get)
            .build()
      );
   private final Setting<Double> feetRange = this.sgSmart
      .add(
         new DoubleSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Scissor$ConstantPool.const_ErJ1VoQf6qAvroq))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(IPlayerMoveC2SPacket$ConstantPool.const_nTg1ZULNBbFKW2g))))
            .defaultValue(Vanilla$ConstantPool.const_nNYBnvbVVw0GfjS)
            .min(0.0)
            .sliderMax(AirPlace$ConstantPool.const_SuoiLZbFSnOyArK)
            .visible(this.smart::get)
            .build()
      );
   private final Setting<Boolean> swing = this.sgRender
      .add(
         new BoolSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(SoundEventListSettingScreen$ConstantPool.const_em8yDIorer5ZW7y))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(MemoryUtil$ConstantPool.const_qydq7hRqZLaS644))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> render = this.sgRender
      .add(
         new BoolSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(EventPriority$ConstantPool.const_wndOnbtqY57GCDF))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Render3DEvent$ConstantPool.const_LyUiXAjvsJuXB4z))))
            .defaultValue(true)
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                        .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(BannerTooltipComponent$ConstantPool.const_5ARIrvr8lYlnubt)))))
                     .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(WMeteorDropdown$WValue$ConstantPool.const_xtzWIz9sixgvAjw)))))
                  .defaultValue(ShapeMode.Both))
               .visible(this.render::get))
            .build()
      );
   private final Setting<SettingColor> sideColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(QueryBuilder$ConstantPool.const_8Xjf4ejaqHfrNNN))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Starscript$1$ConstantPool.const_ZgWFxPVIF2mHwb4))))
            .defaultValue(new SettingColor(197, 137, 232, 10))
            .visible(() -> this.render.get() && this.shapeMode.get().sides())
            .build()
      );
   private final Setting<SettingColor> lineColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Instanceof$ConstantPool.const_GI91bcbCBUXTZyp))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Socks5CommandRequest$ConstantPool.const_rwrecKpoygnihEs))))
            .defaultValue(new SettingColor(197, 137, 232))
            .visible(() -> this.render.get() && this.shapeMode.get().lines())
            .build()
      );
   private final Setting<SettingColor> nextSideColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(WMeteorDropdown$WValue$ConstantPool.const_nrqbaEcWBFTGexW))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(CodeAttribute$ConstantPool.const_b4JrA7aWm4HvoSw))))
            .defaultValue(new SettingColor(227, 196, 245, 10))
            .visible(() -> this.render.get() && this.shapeMode.get().sides())
            .build()
      );
   private final Setting<SettingColor> nextLineColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Vfs$Dir$ConstantPool.const_3i7GeAGwN0pRlQK))))
            .description(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(InvUtils$ConstantPool.const_NXOQsaPVGVj2TE6))))
            .defaultValue(new SettingColor(227, 196, 245))
            .visible(() -> this.render.get() && this.shapeMode.get().lines())
            .build()
      );
   private final List<PlayerEntity> targets = new ArrayList<>();
   private final List<HoleFiller.Hole> holes = new ArrayList<>();
   private final Mutable testPos = new Mutable();
   private final Box box = new Box(0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
   private int timer;

   public HoleFiller() {
      super(
         Categories.Combat,
         hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(VeinMiner$ListMode$ConstantPool.const_fYSRBEfKLlmeuBi))),
         new StringBuilder(hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(IChatHudLineVisible$ConstantPool.const_ciCzn7MqHkSdEV4)))),
         hNemN6aceF(rt4JJpjiSt(g8lCuFSs4g(Expr$Null$ConstantPool.const_quitpK2ddtVFvTY)))
      );
   }

   @Override
   public void onActivate() {
      this.timer = 0;
   }

   @EventHandler
   private void onTick(TickEvent.Pre event) {
      if (this.smart.get()) {
         this.setTargets();
      }

      this.holes.clear();
      FindItemResult block = InvUtils.findInHotbar(itemStack -> this.blocks.get().contains(Block.method_9503(itemStack.method_7909())));
      if (block.found()) {
         BlockIterator.register(this.searchRadius.get(), this.searchRadius.get(), (blockPos, blockState) -> {
            if (this.validHole(blockPos)) {
               int bedrock = 0;
               int obsidian = 0;
               Direction air = null;

               for (Direction direction : Direction.values()) {
                  if (direction != Direction.field_11036) {
                     BlockState state = mc.field_1687.method_8320(blockPos.method_10093(direction));
                     if (state.method_26204() == Blocks.field_9987) {
                        bedrock++;
                     } else if (state.method_26204() == Blocks.field_10540) {
                        obsidian++;
                     } else {
                        if (direction == Direction.field_11033) {
                           return;
                        }

                        if (this.validHole(blockPos.method_10093(direction)) && air == null) {
                           for (Direction dir : Direction.values()) {
                              if (dir != direction.method_10153() && dir != Direction.field_11036) {
                                 BlockState blockState1 = mc.field_1687.method_8320(blockPos.method_10093(direction).method_10093(dir));
                                 if (blockState1.method_26204() == Blocks.field_9987) {
                                    bedrock++;
                                 } else {
                                    if (blockState1.method_26204() != Blocks.field_10540) {
                                       return;
                                    }

                                    obsidian++;
                                 }
                              }
                           }

                           air = direction;
                        }
                     }

                     if (obsidian + bedrock == 5 && air == null) {
                        this.holes.add(new HoleFiller.Hole(blockPos, (byte)0));
                     } else if (obsidian + bedrock == 8 && this.doubles.get() && air != null) {
                        this.holes.add(new HoleFiller.Hole(blockPos, Dir.get(air)));
                     }
                  }
               }
            }
         });
         BlockIterator.after(() -> {
            if (this.timer <= 0 && !this.holes.isEmpty()) {
               int bpt = 0;

               for (HoleFiller.Hole hole : this.holes) {
                  if (bpt < this.blocksPerTick.get() && BlockUtils.place(hole.blockPos, block, this.rotate.get(), 10, this.swing.get(), true)) {
                     bpt++;
                  }
               }

               this.timer = this.placeDelay.get();
            }
         });
         this.timer--;
      }
   }

   @EventHandler(
      priority = 100
   )
   private void onRender(Render3DEvent event) {
      if (this.render.get() && !this.holes.isEmpty()) {
         for (HoleFiller.Hole hole : this.holes) {
            boolean isNext = false;

            for (int i = 0; i < this.holes.size(); i++) {
               if (this.holes.get(i).equals(hole) && i < this.blocksPerTick.get()) {
                  isNext = true;
               }
            }

            Color side = isNext ? this.nextSideColor.get() : this.sideColor.get();
            Color line = isNext ? this.nextLineColor.get() : this.lineColor.get();
            event.renderer.box(hole.blockPos, side, line, this.shapeMode.get(), hole.exclude);
         }
      }
   }

   private boolean validHole(BlockPos pos) {
      this.testPos.method_10101(pos);
      if (mc.field_1724.method_24515().equals(this.testPos)) {
         return false;
      } else if (this.distance(mc.field_1724, this.testPos, false) > this.placeRange.get()) {
         return false;
      } else if (mc.field_1687.method_8320(this.testPos).method_26204() == Blocks.field_10343) {
         return false;
      } else if (((AbstractBlockAccessor)mc.field_1687.method_8320(this.testPos).method_26204()).isCollidable()) {
         return false;
      } else {
         this.testPos.method_10069(0, 1, 0);
         if (((AbstractBlockAccessor)mc.field_1687.method_8320(this.testPos).method_26204()).isCollidable()) {
            return false;
         } else {
            this.testPos.method_10069(0, -1, 0);
            ((IBox)this.box).set(pos);
            if (!mc.field_1687
               .method_8333(null, this.box, entity -> entity instanceof PlayerEntity || entity instanceof TntEntity || entity instanceof EndCrystalEntity)
               .isEmpty()) {
               return false;
            } else {
               return this.smart.get() && !this.forceFill.get().isPressed()
                  ? this.targets
                     .stream()
                     .anyMatch(
                        target -> target.method_23318() > this.testPos.method_10264() && this.distance(target, this.testPos, true) < this.feetRange.get()
                     )
                  : true;
            }
         }
      }
   }

   private void setTargets() {
      this.targets.clear();

      for (PlayerEntity player : mc.field_1687.method_18456()) {
         if (!(player.method_5858(mc.field_1724) > Math.pow(this.targetRange.get(), CodeIterator$Table$ConstantPool.const_BAJdnYbNPoeHCqW))
            && !player.method_7337()
            && player != mc.field_1724
            && !player.method_29504()
            && Friends.get().shouldAttack(player)
            && (!this.ignoreSafe.get() || !this.isSurrounded(player))
            && (
               !this.onlyMoving.get()
                  || player.method_23317() - player.field_6014 == 0.0
                     && player.method_23318() - player.field_6036 == 0.0
                     && player.method_23321() - player.field_5969 == 0.0
            )) {
            this.targets.add(player);
         }
      }
   }

   private boolean isSurrounded(PlayerEntity target) {
      for (Direction dir : Direction.values()) {
         if (dir != Direction.field_11036 && dir != Direction.field_11033) {
            this.testPos.method_10101(target.method_24515().method_10093(dir));
            Block block = mc.field_1687.method_8320(this.testPos).method_26204();
            if (block != Blocks.field_10540
               && block != Blocks.field_9987
               && block != Blocks.field_23152
               && block != Blocks.field_22423
               && block != Blocks.field_22108) {
               return false;
            }
         }
      }

      return true;
   }

   private double distance(PlayerEntity player, BlockPos pos, boolean feet) {
      Vec3d testVec = player.method_19538();
      if (!feet) {
         testVec.method_1031(0.0, player.method_18381(mc.field_1724.method_18376()), 0.0);
      } else if (this.predict.get()) {
         testVec.method_1031(player.method_23317() - player.field_6014, player.method_23318() - player.field_6036, player.method_23321() - player.field_5969);
      }

      double i = testVec.field_1352 - (pos.method_10263() + BlockIterator$Callback$ConstantPool.const_nFbahdapBbCAww5);
      double j = testVec.field_1351 - (pos.method_10264() + (feet ? 1.0 : CodeIterator$Pointers$ConstantPool.const_hAyvBSZDJG2IgIp));
      double k = testVec.field_1350 - (pos.method_10260() + LongInfo$ConstantPool.const_4MNkFfkdr1dqz9s);
      return Math.sqrt(i * i + j * j + k * k);
   }

   private static class Hole {
      private final Mutable blockPos = new Mutable();
      private final byte exclude;

      public Hole(BlockPos blockPos, byte exclude) {
         this.blockPos.method_10101(blockPos);
         this.exclude = exclude;
      }
   }
}
