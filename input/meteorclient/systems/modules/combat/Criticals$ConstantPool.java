package meteordevelopment.meteorclient.systems.modules.combat;

public final class Criticals$ConstantPool {
   public static int const_VDwTqN2HtDnSdu1 = (int)((int)1606203614L ^ 1223034259 ^ ((int)-1597251516L ^ -1215209851));
   public static String const_QvlqetLaWnIKOJi = Azy32QiDTV(TZSJ3w4tO6("ƒƤƭƤƢƵǡƒƮƴƯƥƲ"));
   public static String const_B7q6w1mYADrmyL7 = Haek9CSJ0H(TZSJ3w4tO6("ρϽϼϦεϼϦεϿϠϦϡεϴεϡϼϰϷϧϰϴϾϰϧεϡϺεϸϴϾϰεϼϡεϹϰϦϦεϹϼϾϰϹϬεϡϺεϷϧϰϴϾεϷϹϺ϶ϾϦεϼϳεϼϡε϶ϴϻεϴϣϺϼϱεϼϡλ"));
   public static String const_YpYCrVInKflb9X1 = B4qhvZtgdF(TZSJ3w4tO6("ȑɠ"));
   public static double const_BgTWcvDytcOd4ez = Double.longBitsToDouble(
      930648525460812225L ^ 8866373025459915140L ^ -2048596597556110626L ^ -3138990717588086117L
   );
   public static int const_awnjBqneJj2twVv = (int)(8492850519519210710L ^ 8492850519197062289L) ^ (int)(-1121940686 ^ -2139867662);
   public static int const_o6YaFgK6NHLelyr = (int)(372077259773852850L ^ -372077259483072521L) ^ (int)(-1529777452 ^ 1786902549);
   public static String const_dTotnDFDKe64bzw = wWgER70bOe(TZSJ3w4tO6("ӀӈӞӉӍӞӉӆ"));
   public static double const_MI46lQl7iym9WFq = Double.longBitsToDouble(
      7496407497738408786L ^ -7622541645537620958L ^ 1544055721381095976L ^ -6183769513951333032L
   );
   public static String const_aeVI97r6dHcQIvI = d3FHw72ukN(TZSJ3w4tO6("߬ߓߚߏޛߖߔߟߞޛߏߔޛߎ߈ߞޛߝߔ߉ޛߏߓߒ߈ޛߖߚ߉ߐߞ߉ޕ"));
   public static String const_z4VrWhW6wefr6qj = XyvVHggyF7(TZSJ3w4tO6("ŊŶŻľŬŻŮſŷŬľżŲűŽŵŭľŪűľųŷŰŻİ"));
   public static int const_OeT1KlPKsugBaoH = (int)(-691742493006643263L ^ 691742491979279581L) ^ (int)(1436821964 ^ -275219518);
   public static String const_v297DgJu7YD8NdL = 4m5nHToGU4(TZSJ3w4tO6("͜"));
   public static String const_QBMlbijqIyrYfjP = XwASI6wHBU(TZSJ3w4tO6("߬߫߱ߠ߷ߣߤߦߠޥߨߠ߱߭ߪߡ߶ޥ߾ޏ"));
   public static int const_NMHy9lqLqP4nliO = (int)((int)(-2052706542 ^ -274442049) ^ ((int)1073994633L ^ 704752448));
   public static String const_CTnvTSSa7D3kIaw = TuBxNnsLrL(TZSJ3w4tO6("ʠ"));
}
