package meteordevelopment.meteorclient.systems.modules.combat;

import javassist.CtField$CodeInitializer0$ConstantPool;
import javassist.bytecode.CodeAttribute$RuntimeCopyException$ConstantPool;
import javassist.bytecode.StackMapTable$Copier$ConstantPool;
import javassist.compiler.ast.StringL$ConstantPool;
import javassist.util.proxy.SecurityActions$7$ConstantPool;
import meteordevelopment.meteorclient.asm.transformers.CanvasWorldRendererTransformer$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.widgets.containers.WSection$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WVerticalList$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.ggboy.AntiPhase$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.Rotation$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.HandView$SwingMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$IBlockPosProvider$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Nuker$ListMode$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.FakeClientPlayer$ConstantPool;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.item.Items;
import net.minecraft.network.packet.s2c.play.EntityStatusS2CPacket;
import org.reflections.scanners.AbstractScanner$ConstantPool;

public class AutoTotem extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final Setting<AutoTotem.Mode> mode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(Rotation$ConstantPool.const_P2bKSmls9vq2L1n)))))
                  .description(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(StringL$ConstantPool.const_4eV7YiGCLT4VOLp)))))
               .defaultValue(AutoTotem.Mode.Smart))
            .build()
      );
   private final Setting<Integer> delay = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(WSection$ConstantPool.const_vtdrXSI6ejg4nD1))))
            .description(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(HighwayBuilder$IBlockPosProvider$ConstantPool.const_qrrSQgW0MVaOJDL))))
            .defaultValue(0)
            .min(0)
            .build()
      );
   private final Setting<Integer> health = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(FakeClientPlayer$ConstantPool.const_t8JMSIQcluVrIaI))))
            .description(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(HandView$SwingMode$ConstantPool.const_dYarY4I4eUqNjST))))
            .defaultValue(10)
            .range(0, 36)
            .sliderMax(36)
            .visible(() -> this.mode.get() == AutoTotem.Mode.Smart)
            .build()
      );
   private final Setting<Boolean> elytra = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(StackMapTable$Copier$ConstantPool.const_dLKPJYR4MEAFkos))))
            .description(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(Nuker$ListMode$ConstantPool.const_Y1F7W997rTJ1JX0))))
            .defaultValue(true)
            .visible(() -> this.mode.get() == AutoTotem.Mode.Smart)
            .build()
      );
   private final Setting<Boolean> fall = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(AntiPhase$ConstantPool.const_nYlhV0FeOpFoahs))))
            .description(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(AbstractScanner$ConstantPool.const_LHH2JqX9TbN4Qi2))))
            .defaultValue(true)
            .visible(() -> this.mode.get() == AutoTotem.Mode.Smart)
            .build()
      );
   private final Setting<Boolean> explosion = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(CodeAttribute$RuntimeCopyException$ConstantPool.const_TaxYdSDQrai5Ogc))))
            .description(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(CanvasWorldRendererTransformer$ConstantPool.const_dr6DjGI3gRvG4vF))))
            .defaultValue(true)
            .visible(() -> this.mode.get() == AutoTotem.Mode.Smart)
            .build()
      );
   public boolean locked;
   private int totems;
   private int ticks;

   public AutoTotem() {
      super(
         Categories.Combat,
         AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(CtField$CodeInitializer0$ConstantPool.const_WaRddDAHiVl52Gp))),
         new StringBuilder(AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(SecurityActions$7$ConstantPool.const_UEFB4X692Jyd1lS)))),
         AD7wrLK9jk(4NpouxLVMj(AoSWs96dlt(WVerticalList$ConstantPool.const_NiI47vGCQQS6r2W)))
      );
   }

   @EventHandler(
      priority = 200
   )
   private void onTick(TickEvent.Pre event) {
      FindItemResult result = InvUtils.find(Items.field_8288);
      this.totems = result.count();
      if (this.totems <= 0) {
         this.locked = false;
      } else if (this.ticks >= this.delay.get()) {
         boolean low = mc.field_1724.method_6032() + mc.field_1724.method_6067() - PlayerUtils.possibleHealthReductions(this.explosion.get(), this.fall.get())
            <= this.health.get().intValue();
         boolean ely = this.elytra.get()
            && mc.field_1724.method_6118(EquipmentSlot.field_6174).method_7909() == Items.field_8833
            && mc.field_1724.method_6128();
         this.locked = this.mode.get() == AutoTotem.Mode.Strict || this.mode.get() == AutoTotem.Mode.Smart && (low || ely);
         if (this.locked && mc.field_1724.method_6079().method_7909() != Items.field_8288) {
            InvUtils.move().from(result.slot()).toOffhand();
         }

         this.ticks = 0;
         return;
      }

      this.ticks++;
   }

   @EventHandler(
      priority = 100
   )
   private void onReceivePacket(PacketEvent.Receive event) {
      if (event.packet instanceof EntityStatusS2CPacket p) {
         if (p.method_11470() == 35) {
            Entity entity = p.method_11469(mc.field_1687);
            if (entity != null && entity.equals(mc.field_1724)) {
               this.ticks = 0;
            }
         }
      }
   }

   public boolean isLocked() {
      return this.isActive() && this.locked;
   }

   @Override
   public String getInfoString() {
      return String.valueOf(this.totems);
   }

   public static enum Mode {
      Smart,
      Strict;
   }
}
