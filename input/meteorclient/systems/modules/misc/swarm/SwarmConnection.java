package meteordevelopment.meteorclient.systems.modules.misc.swarm;

import io.netty.handler.codec.socks.SocksAuthResponse$ConstantPool;
import java.io.DataOutputStream;
import java.io.IOException;
import java.net.Socket;
import javassist.expr.FieldAccess$ProceedForRead$ConstantPool;
import javassist.util.proxy.DefinePackageHelper$Java7$ConstantPool;
import javax.annotation.concurrent.GuardedBy$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.TravelEventPost$ConstantPool;
import meteordevelopment.meteorclient.events.world.ServerConnectEndEvent$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.InventoryHud$Background$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.MeteorTextHud$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$DiagonalBlockPosProvider$4$ConstantPool;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.orbit.IEventBus$ConstantPool;

public class SwarmConnection extends Thread {
   public final Socket socket;
   public String messageToSend;

   public SwarmConnection(Socket socket) {
      this.socket = socket;
      this.start();
   }

   @Override
   public void run() {
      ChatUtils.infoPrefix(
         JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(IEventBus$ConstantPool.const_eRwDw1Gvgh2SjqI))),
         JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(DefinePackageHelper$Java7$ConstantPool.const_YaIvw5hwVS6bapY))),
         this.getIp(this.socket.getInetAddress().getHostAddress())
      );

      try {
         DataOutputStream out = new DataOutputStream(this.socket.getOutputStream());

         while (!this.isInterrupted()) {
            if (this.messageToSend != null) {
               try {
                  out.writeUTF(this.messageToSend);
                  out.flush();
               } catch (Exception var3) {
                  ChatUtils.errorPrefix(
                     JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(InventoryHud$Background$ConstantPool.const_JY52LwkNWflLahO))),
                     JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(ServerConnectEndEvent$ConstantPool.const_1YIPcCLFhSScDIQ)))
                  );
                  var3.printStackTrace();
               }

               this.messageToSend = null;
            }
         }

         out.close();
      } catch (IOException var4) {
         ChatUtils.infoPrefix(
            JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(HighwayBuilder$DiagonalBlockPosProvider$4$ConstantPool.const_dbqB3v4odN8Ee6A))),
            JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(SocksAuthResponse$ConstantPool.const_TwQdS7Fwj3PAcnW))),
            this.getIp(this.socket.getInetAddress().getHostAddress()),
            this.socket.getPort()
         );
         var4.printStackTrace();
      }
   }

   public void disconnect() {
      try {
         this.socket.close();
      } catch (IOException var2) {
         var2.printStackTrace();
      }

      ChatUtils.infoPrefix(
         JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(TravelEventPost$ConstantPool.const_roY10bZ5fnO3FVO))),
         JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(MeteorTextHud$ConstantPool.const_NQMzK39T9VNF5ve))),
         this.socket.getInetAddress().getHostAddress()
      );
      this.interrupt();
   }

   public String getConnection() {
      return this.getIp(this.socket.getInetAddress().getHostAddress()) + ":" + this.socket.getPort();
   }

   private String getIp(String ip) {
      return ip.equals(JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(GuardedBy$ConstantPool.const_5GT9e2IMO0HLwR2))))
         ? JKMCSGaAWJ(9oSYkcnO59(e1GNlLi7hg(FieldAccess$ProceedForRead$ConstantPool.const_yelGo7G7JC4LVbJ)))
         : ip;
   }
}
