package meteordevelopment.meteorclient.systems.modules.misc.swarm;

import io.netty.handler.codec.socks.SocksInitResponseDecoder$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5AddressEncoder$1$ConstantPool;
import io.netty.handler.proxy.ProxyConnectException$ConstantPool;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import javassist.bytecode.StackMapTable$Walker$ConstantPool;
import javassist.bytecode.stackmap.BasicBlock$Maker$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.KillAura$ShieldMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$1$ConstantPool;
import meteordevelopment.meteorclient.utils.network.MeteorExecutor;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.meteorclient.utils.render.postprocess.EntityOutlineShader$ConstantPool;

public class SwarmHost extends Thread {
   private ServerSocket socket;
   private final SwarmConnection[] clientConnections = new SwarmConnection[50];

   public SwarmHost(int port) {
      try {
         this.socket = new ServerSocket(port);
      } catch (IOException var3) {
         this.socket = null;
         ChatUtils.errorPrefix(
            aL1WTT2mnG(7bcZFLodgR(fltYtTJwaT(HighwayBuilder$State$1$ConstantPool.const_mOKsOSvnKTrpZ3G))),
            aL1WTT2mnG(7bcZFLodgR(fltYtTJwaT(KillAura$ShieldMode$ConstantPool.const_pWWIo1geDs6lEQp))),
            port
         );
         var3.printStackTrace();
      }

      if (this.socket != null) {
         this.start();
      }
   }

   @Override
   public void run() {
      ChatUtils.infoPrefix(
         aL1WTT2mnG(7bcZFLodgR(fltYtTJwaT(Socks5AddressEncoder$1$ConstantPool.const_W6rdrOSgmnsTnZF))),
         aL1WTT2mnG(7bcZFLodgR(fltYtTJwaT(SocksInitResponseDecoder$ConstantPool.const_OFULgucL1Ggi6DU))),
         this.socket.getLocalPort()
      );

      while (!this.isInterrupted()) {
         try {
            Socket connection = this.socket.accept();
            this.assignConnectionToSubServer(connection);
         } catch (IOException var2) {
            ChatUtils.errorPrefix(
               aL1WTT2mnG(7bcZFLodgR(fltYtTJwaT(BasicBlock$Maker$ConstantPool.const_TYh9OS6li4SlxDY))),
               aL1WTT2mnG(7bcZFLodgR(fltYtTJwaT(EntityOutlineShader$ConstantPool.const_sErS7t1LhBp3zZj)))
            );
            var2.printStackTrace();
         }
      }
   }

   public void assignConnectionToSubServer(Socket connection) {
      for (int i = 0; i < this.clientConnections.length; i++) {
         if (this.clientConnections[i] == null) {
            this.clientConnections[i] = new SwarmConnection(connection);
            break;
         }
      }
   }

   public void disconnect() {
      for (SwarmConnection connection : this.clientConnections) {
         if (connection != null) {
            connection.disconnect();
         }
      }

      try {
         this.socket.close();
      } catch (IOException var5) {
         var5.printStackTrace();
      }

      ChatUtils.infoPrefix(
         aL1WTT2mnG(7bcZFLodgR(fltYtTJwaT(ProxyConnectException$ConstantPool.const_q6gAVb3qzibEbds))),
         aL1WTT2mnG(7bcZFLodgR(fltYtTJwaT(StackMapTable$Walker$ConstantPool.const_wdZvyLoa2StEqvW))),
         this.socket.getLocalPort()
      );
      this.interrupt();
   }

   public void sendMessage(String s) {
      MeteorExecutor.execute(() -> {
         for (SwarmConnection connection : this.clientConnections) {
            if (connection != null) {
               connection.messageToSend = s;
            }
         }
      });
   }

   public SwarmConnection[] getConnections() {
      return this.clientConnections;
   }

   public int getConnectionCount() {
      int count = 0;

      for (SwarmConnection clientConnection : this.clientConnections) {
         if (clientConnection != null) {
            count++;
         }
      }

      return count;
   }
}
