package meteordevelopment.meteorclient.systems.modules.misc.swarm;

import io.netty.handler.codec.socksx.v4.Socks4CommandStatus$ConstantPool;
import io.netty.handler.proxy.ProxyConnectException$ConstantPool;
import javassist.bytecode.CodeIterator$Lookup$ConstantPool;
import javassist.bytecode.SignatureAttribute$MethodSignature$ConstantPool;
import javassist.bytecode.StackMapTable$Copier$ConstantPool;
import meteordevelopment.meteorclient.events.game.GameJoinedEvent;
import meteordevelopment.meteorclient.events.game.GameLeftEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.GuiTheme;
import meteordevelopment.meteorclient.gui.screens.settings.FontFaceSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WWidget;
import meteordevelopment.meteorclient.gui.widgets.containers.WHorizontalList;
import meteordevelopment.meteorclient.gui.widgets.containers.WVerticalList;
import meteordevelopment.meteorclient.gui.widgets.pressable.WButton;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StringSetting;
import meteordevelopment.meteorclient.systems.accounts.types.TheAlteningAccount$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.ModuleInit$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.SkyLadder$1$ConstantPool;
import meteordevelopment.meteorclient.utils.Utils$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.player.EChestMemory$ConstantPool;
import meteordevelopment.meteorclient.utils.render.PlayerHeadUtils$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.Util;
import org.reflections.vfs.JarInputDir$1$ConstantPool;

public class Swarm extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   public final Setting<Swarm.Mode> mode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(JarInputDir$1$ConstantPool.const_Od2M9u7Ta9B7jxW)))))
                  .description(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(ModuleInit$ConstantPool.const_Ng4gDQmqn6DIY6M)))))
               .defaultValue(Swarm.Mode.Host))
            .build()
      );
   private final Setting<String> ipAddress = this.sgGeneral
      .add(
         new StringSetting.Builder()
            .name(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(Socks4CommandStatus$ConstantPool.const_VPqZ3BAHBIyTel1))))
            .description(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(PlayerHeadUtils$ConstantPool.const_dckHFt9SQGagZhM))))
            .defaultValue(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(NotebotUtils$ConstantPool.const_qhDRIJQL3GnMjuT))))
            .visible(() -> this.mode.get() == Swarm.Mode.Worker)
            .build()
      );
   private final Setting<Integer> serverPort = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(SkyLadder$1$ConstantPool.const_TLFqwdGoIIkFOLg))))
            .description(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(TheAlteningAccount$ConstantPool.const_eVrDS2dfLGQvqD1))))
            .defaultValue(6969)
            .range(1, ProxyConnectException$ConstantPool.const_ran4ytOwx0DlK7O)
            .noSlider()
            .build()
      );
   public SwarmHost host;
   public SwarmWorker worker;

   public Swarm() {
      super(
         Categories.Misc,
         JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(EChestMemory$ConstantPool.const_T4eaWGqVa9eM8V5))),
         new StringBuilder(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(CodeIterator$Lookup$ConstantPool.const_LtFo9u6SHotrtFt)))),
         JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(Utils$ConstantPool.const_hAQSj46UeO69mbx)))
      );
   }

   @Override
   public WWidget getWidget(GuiTheme theme) {
      WVerticalList list = theme.verticalList();
      WHorizontalList b = list.add(theme.horizontalList()).expandX().widget();
      WButton start = b.add(theme.button(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(StackMapTable$Copier$ConstantPool.const_q6vvGgGZ27W7JwM))))).expandX().widget();
      start.action = () -> {
         if (this.isActive()) {
            this.close();
            if (this.mode.get() == Swarm.Mode.Host) {
               this.host = new SwarmHost(this.serverPort.get());
            } else {
               this.worker = new SwarmWorker(this.ipAddress.get(), this.serverPort.get());
            }
         }
      };
      WButton stop = b.add(theme.button(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(Utils$ConstantPool.const_nAxgMi9gLOSnYDq))))).expandX().widget();
      stop.action = this::close;
      WButton guide = list.add(theme.button(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(FontFaceSettingScreen$ConstantPool.const_88v9wqVTiTHvEeM))))).expandX().widget();
      guide.action = () -> Util.method_668()
         .method_670(JoaqgDeFWu(ESrAGZ7094(bxlWVy4ZQf(SignatureAttribute$MethodSignature$ConstantPool.const_vNb7GJG8Bwbxjro))));
      return list;
   }

   @Override
   public String getInfoString() {
      return this.mode.get().name();
   }

   @Override
   public void onActivate() {
      this.close();
   }

   @Override
   public void onDeactivate() {
      this.close();
   }

   public void close() {
      try {
         if (this.host != null) {
            this.host.disconnect();
            this.host = null;
         }

         if (this.worker != null) {
            this.worker.disconnect();
            this.worker = null;
         }
      } catch (Exception var2) {
      }
   }

   @EventHandler
   private void onGameLeft(GameLeftEvent event) {
      this.toggle();
   }

   @EventHandler
   private void onGameJoin(GameJoinedEvent event) {
      this.toggle();
   }

   @Override
   public void toggle() {
      this.close();
      super.toggle();
   }

   public boolean isHost() {
      return this.mode.get() == Swarm.Mode.Host && this.host != null && !this.host.isInterrupted();
   }

   public boolean isWorker() {
      return this.mode.get() == Swarm.Mode.Worker && this.worker != null && !this.worker.isInterrupted();
   }

   @EventHandler
   private void onTick(TickEvent.Post event) {
      if (this.isWorker()) {
         this.worker.tick();
      }
   }

   public static enum Mode {
      Host,
      Worker;
   }
}
