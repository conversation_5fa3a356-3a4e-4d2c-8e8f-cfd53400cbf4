package meteordevelopment.meteorclient.systems.modules.misc.swarm;

public final class Swarm$ConstantPool {
   public static String const_79II4FxGe6Gd2GI = pNW7QhTTlG(ylwXSyLeW8("քֲ֣֤\u05f7ֲֿ֣\u05f7ֶָ֢֣\u05f7ֶ֤֧֠\u05f7ְֲֶ֣֥֣\u05f9"));
   public static int const_zjVw7rWe5aiLb4t = (int)((int)(-1086355191 ^ -294380756) ^ ((int)558290053L ^ 1879917040));
   public static double const_LfVbMf2Q4vdooIE = Double.longBitsToDouble(
      -6812366721073223324L ^ -6507230809276597257L ^ -8255224707764942218L ^ -3917080147365242651L
   );
   public static String const_65aioJgnYO6v9IC = Wv5CtyJetv(ylwXSyLeW8("ٌ٧ٽ٠ٽ٠٬ٺةٽ٦ة٨ٽٽ٨٪٢ا"));
   public static String const_SMDlk8GTvvGk25G = UWYq7OvTeQ(ylwXSyLeW8("ҦҬҹҹҹҹӁӀҮҏӀҦҁҌҌӀ҉ғӀҒ҅҃ҏҍҍ҅Ҏ҄҅҄Ӏҗ҉Ҕ҈ӀҔ҈҉ғӀҍҏ҄ҕҌ҅ӎ"));
   public static String const_L3qqtvVO1DfLKIk = Baav5SBbrF(ylwXSyLeW8("ٖٗٙٚٞن"));
   public static String const_YCZ9oLyxplabF8d = z4sCj11Rqt(ylwXSyLeW8("˖˲˵˾˞˘˳˾˨˯˙˷˴˸˰˺˿˾"));
}
