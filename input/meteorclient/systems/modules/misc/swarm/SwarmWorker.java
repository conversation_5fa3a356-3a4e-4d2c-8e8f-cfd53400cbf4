package meteordevelopment.meteorclient.systems.modules.misc.swarm;

import io.netty.handler.codec.socks.SocksInitRequestDecoder$State$ConstantPool;
import java.io.DataInputStream;
import java.io.IOException;
import java.net.Socket;
import javassist.bytecode.ClassFileWriter$FieldWriter$ConstantPool;
import meteordevelopment.meteorclient.commands.Commands;
import meteordevelopment.meteorclient.events.entity.player.FinishUsingItemEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WHorizontalSeparator$ConstantPool;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.systems.hud.YAnchor$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.NoRotate$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.EntityOwner$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$StraightBlockPosProvider$3$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.StashFinder$ChunkScreen$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.ICopyable$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.MeteorStarscript$1$ConstantPool;
import meteordevelopment.meteorclient.utils.other.JsonDateDeserializer$ConstantPool;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.orbit.EventBus$ConstantPool;
import meteordevelopment.starscript.utils.Stack$ConstantPool;
import net.minecraft.block.Block;

public class SwarmWorker extends Thread {
   private Socket socket;
   public Block target;

   public SwarmWorker(String ip, int port) {
      try {
         this.socket = new Socket(ip, port);
      } catch (Exception var4) {
         this.socket = null;
         ChatUtils.warningPrefix(
            Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(NoRotate$ConstantPool.const_mUFgwaifV2r1bbd))),
            Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(Stack$ConstantPool.const_4FRaeSeoLJ1IVvN))),
            ip,
            port
         );
         var4.printStackTrace();
      }

      if (this.socket != null) {
         this.start();
      }
   }

   @Override
   public void run() {
      ChatUtils.infoPrefix(
         Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(EntityOwner$ConstantPool.const_jvoormW4azkcLGd))),
         Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(SocksInitRequestDecoder$State$ConstantPool.const_GJK6bPtOaBvvBrN))),
         this.getIp(this.socket.getInetAddress().getHostAddress()),
         this.socket.getPort()
      );

      try {
         DataInputStream in = new DataInputStream(this.socket.getInputStream());

         while (!this.isInterrupted()) {
            String read = in.readUTF();
            if (read.startsWith(Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(JsonDateDeserializer$ConstantPool.const_xyhbDki09zO84L6))))) {
               ChatUtils.infoPrefix(
                  Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(MeteorStarscript$1$ConstantPool.const_0bXymnvBIwFmnyJ))),
                  Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(HighwayBuilder$StraightBlockPosProvider$3$ConstantPool.const_7yS8wwBmGpwigzG))),
                  read
               );

               try {
                  Commands.dispatch(read);
               } catch (Exception var4) {
                  ChatUtils.error(Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(WHorizontalSeparator$ConstantPool.const_b27agveJYAevJPn))));
                  var4.printStackTrace();
               }
            }
         }

         in.close();
      } catch (IOException var5) {
         ChatUtils.errorPrefix(
            Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(ClassFileWriter$FieldWriter$ConstantPool.const_CIQFd7laMnCIaW4))),
            Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(FinishUsingItemEvent$ConstantPool.const_0PleUAd76eiy4Vn)))
         );
         var5.printStackTrace();
         this.disconnect();
      }
   }

   public void disconnect() {
      try {
         this.socket.close();
      } catch (IOException var2) {
         var2.printStackTrace();
      }

      PathManagers.get().stop();
      ChatUtils.infoPrefix(
         Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(StashFinder$ChunkScreen$ConstantPool.const_daTi5N7fTiuYnhM))),
         Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(EventBus$ConstantPool.const_ObtqqBGAObNtTCy)))
      );
      this.interrupt();
   }

   public void tick() {
      if (this.target != null) {
         PathManagers.get().stop();
         PathManagers.get().mine(this.target);
         this.target = null;
      }
   }

   public String getConnection() {
      return this.getIp(this.socket.getInetAddress().getHostAddress()) + ":" + this.socket.getPort();
   }

   private String getIp(String ip) {
      return ip.equals(Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(ICopyable$ConstantPool.const_JmtSFwOejBODY1L))))
         ? Gelgy2QaZT(b4q2JWLwPb(drwqaDcx2J(YAnchor$ConstantPool.const_OB5twbjwe7g9g9L)))
         : ip;
   }
}
