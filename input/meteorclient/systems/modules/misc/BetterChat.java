package meteordevelopment.meteorclient.systems.modules.misc;

import com.mojang.authlib.GameProfile;
import com.mojang.blaze3d.systems.RenderSystem;
import io.netty.handler.codec.socks.SocksAuthRequest$ConstantPool;
import io.netty.handler.codec.socks.SocksAuthResponseDecoder$ConstantPool;
import io.netty.handler.proxy.ProxyHandler$2$ConstantPool;
import it.unimi.dsi.fastutil.chars.Char2CharMap;
import it.unimi.dsi.fastutil.chars.Char2CharOpenHashMap;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.ints.IntList;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import javassist.ByteArrayClassPath$1$ConstantPool;
import javassist.ClassPath$ConstantPool;
import javassist.JarDirClassPath$ConstantPool;
import javassist.bytecode.CodeAnalyzer$ConstantPool;
import javassist.bytecode.Descriptor$PrettyPrinter$ConstantPool;
import javassist.bytecode.PackageInfo$ConstantPool;
import javassist.bytecode.SignatureAttribute$Cursor$ConstantPool;
import javassist.bytecode.StackMapTable$OffsetShifter$ConstantPool;
import javassist.bytecode.StackMapTable$Writer$ConstantPool;
import javassist.bytecode.analysis.IntQueue$Entry$ConstantPool;
import javassist.bytecode.stackmap.TypeData$TypeVar$ConstantPool;
import javassist.util.HotSwapper$ConstantPool;
import javassist.util.proxy.ProxyObjectInputStream$ConstantPool;
import javassist.util.proxy.SerializedProxy$1$ConstantPool;
import javax.annotation.MatchesPattern$Checker$ConstantPool;
import javax.annotation.MatchesPattern$ConstantPool;
import meteordevelopment.discordipc.connection.Connection$ConstantPool;
import meteordevelopment.meteorclient.Main$OperatingSystem$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.asm.MethodInfo$ConstantPool;
import meteordevelopment.meteorclient.commands.Commands;
import meteordevelopment.meteorclient.commands.arguments.ModuleArgumentType$ConstantPool;
import meteordevelopment.meteorclient.commands.arguments.ProfileArgumentType$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.WaspCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.ClipAtLedgeEvent$ConstantPool;
import meteordevelopment.meteorclient.events.game.ReceiveMessageEvent;
import meteordevelopment.meteorclient.events.game.SendMessageEvent;
import meteordevelopment.meteorclient.events.render.ApplyTransformationEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderOperation$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.GuiTab$GuiScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.WMeteorWindow$WMeteorHeader$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorFavorite$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorPlus$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.input.WTextBox$ConstantPool;
import meteordevelopment.meteorclient.mixin.ChatHudAccessor;
import meteordevelopment.meteorclient.mixininterface.IChatHudLine;
import meteordevelopment.meteorclient.mixininterface.IChatHudLineVisible;
import meteordevelopment.meteorclient.renderer.Mesh$Attrib$ConstantPool;
import meteordevelopment.meteorclient.renderer.PostProcessRenderer$ConstantPool;
import meteordevelopment.meteorclient.settings.BlockSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StatusEffectListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.StorageBlockListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.settings.StringListSetting;
import meteordevelopment.meteorclient.settings.StringSetting;
import meteordevelopment.meteorclient.systems.accounts.AllMod$1$1$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.AllMod$1$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$GameOwnershipResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.HudElement$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.PlayerRadarHud$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.Burrow$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.HoleFiller$Hole$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AntiClimb$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.FeetPlace$SurroundType$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.Phase$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.Anchor$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.ClickTP$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.FastClimb$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.modes.Bounce$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoFish$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.MiddleClickExtra$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.PotionSaver$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.BetterTooltips$DisplayWhen$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.BlockSelection$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ESP$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.HandView$SwingMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.LightOverlay$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.NoRender$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WaypointsModule$WIcon$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Xray$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.blockesp.ESPBlock$1$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$11$ConstantPool;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.entity.DamageUtils$1$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.text.MeteorClickEvent;
import meteordevelopment.meteorclient.utils.misc.text.TextVisitor;
import meteordevelopment.meteorclient.utils.network.Capes$Cape$ConstantPool;
import meteordevelopment.meteorclient.utils.network.Capes$ConstantPool;
import meteordevelopment.meteorclient.utils.network.MeteorExecutor$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.decoder.NBSSongDecoder$ConstantPool;
import meteordevelopment.meteorclient.utils.player.ChatUtils;
import meteordevelopment.meteorclient.utils.player.InventorySorter$InvPart$ConstantPool;
import meteordevelopment.meteorclient.utils.player.TitleScreenCredits$Credit$ConstantPool;
import meteordevelopment.meteorclient.utils.render.FontUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.compiler.Expr$Bool$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Conditional$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Group$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Variable$ConstantPool;
import meteordevelopment.starscript.utils.Stack$ConstantPool;
import meteordevelopment.starscript.value.Value$Number$ConstantPool;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.hud.ChatHudLine;
import net.minecraft.client.gui.hud.ChatHudLine.Visible;
import net.minecraft.client.network.PlayerListEntry;
import net.minecraft.text.HoverEvent;
import net.minecraft.text.MutableText;
import net.minecraft.text.Style;
import net.minecraft.text.Text;
import net.minecraft.text.ClickEvent.Action;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;
import org.reflections.serializers.JavaCodeSerializer$ConstantPool;
import org.reflections.serializers.JsonSerializer$ConstantPool;
import org.reflections.vfs.JbossDir$ConstantPool;
import org.reflections.vfs.Vfs$DefaultUrlTypes$7$ConstantPool;

public class BetterChat extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgFilter = this.settings.createGroup(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(WMeteorFavorite$ConstantPool.const_qd5n1Bc95ovj2VB))));
   private final SettingGroup sgLongerChat = this.settings.createGroup(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(HoleFiller$Hole$ConstantPool.const_AjBulnimlj6rvFb))));
   private final SettingGroup sgPrefix = this.settings.createGroup(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Expr$Bool$ConstantPool.const_s2OTsa6jeH2VAv3))));
   private final SettingGroup sgSuffix = this.settings
      .createGroup(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(StackMapTable$OffsetShifter$ConstantPool.const_hlyhFFbaZzOnO5R))));
   private final Setting<Boolean> annoy = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Xray$ConstantPool.const_Nir2Z6HargYpwkp))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(InventorySorter$InvPart$ConstantPool.const_HBPE2v494AdwDfO))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> fancy = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(HandView$SwingMode$ConstantPool.const_8FTbTJ8Tn4X8tNG))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(AntiClimb$ConstantPool.const_6bbLqJ1vN9nbe62))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> timestamps = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Anchor$ConstantPool.const_1tMSVesrndWquu1))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(StackMapTable$Writer$ConstantPool.const_CugDApfEUeLYAaB))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> playerHeads = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(MeteorExecutor$ConstantPool.const_lZg2vTqrtr4l7Bq))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(TypeData$TypeVar$ConstantPool.const_W6S79eI2vCyFhLd))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> coordsProtection = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(FontUtils$ConstantPool.const_JYMGT70W3ttGQi1))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Stack$ConstantPool.const_Ajb0JDnoB6HV90V))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> keepHistory = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ClassPath$ConstantPool.const_dof6sYoXStFUF7i))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(MatchesPattern$ConstantPool.const_5EL61B1Ft1bOjaw))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> antiSpam = this.sgFilter
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Capes$ConstantPool.const_rkVk2sBZ28yGhI3))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ProfileArgumentType$ConstantPool.const_1MJVjEMJS9qizvG))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Integer> antiSpamDepth = this.sgFilter
      .add(
         new IntSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(PostProcessRenderer$ConstantPool.const_WAKDO9OWfr0A79d))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(MicrosoftLogin$GameOwnershipResponse$ConstantPool.const_wNuNouE4vOONcaO))))
            .defaultValue(20)
            .min(1)
            .sliderMin(1)
            .visible(this.antiSpam::get)
            .build()
      );
   private final Setting<Boolean> antiClear = this.sgFilter
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(WTextBox$ConstantPool.const_6Zb7y6dUNOlOWa4))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(SignatureAttribute$Cursor$ConstantPool.const_rpL4bjRsg6jdgUn))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> filterRegex = this.sgFilter
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(AutoRespawn$ConstantPool.const_nY462ieozdPiFy0))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Expr$Group$ConstantPool.const_HGZFrgla45Sy07t))))
            .defaultValue(false)
            .build()
      );
   private final Setting<List<String>> regexFilters = this.sgFilter
      .add(
         new StringListSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ClickTP$ConstantPool.const_fI1bWxbJy2CPnQN))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(PackageInfo$ConstantPool.const_b1qto1jhhjy4Vnq))))
            .visible(this.filterRegex::get)
            .onChanged(strings -> this.compileFilterRegexList())
            .build()
      );
   private final Setting<Boolean> infiniteChatBox = this.sgLongerChat
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Phase$ConstantPool.const_KFFXQRIhBOqnefj))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Descriptor$PrettyPrinter$ConstantPool.const_WgmwotAzpsJyQ0O))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> longerChatHistory = this.sgLongerChat
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(SerializedProxy$1$ConstantPool.const_lbd49btWB4rcwDV))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(IntQueue$Entry$ConstantPool.const_cTGu6GWN1TAvqSg))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Integer> longerChatLines = this.sgLongerChat
      .add(
         new IntSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(JavaCodeSerializer$ConstantPool.const_3DGID1xbBy5jd85))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ProxyHandler$2$ConstantPool.const_ppZEglBdGVfdm9c))))
            .defaultValue(1000)
            .min(0)
            .sliderRange(0, 1000)
            .visible(this.longerChatHistory::get)
            .build()
      );
   private final Setting<Boolean> prefix = this.sgPrefix
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(PotionSaver$ConstantPool.const_uZuoWmjldG7yd3V))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Connection$ConstantPool.const_ooeqRevMSDenDhY))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> prefixRandom = this.sgPrefix
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(MiddleClickExtra$ConstantPool.const_SJ1nhG4Mid8gII2))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(PlayerRadarHud$ConstantPool.const_lVGydk9eVP8VToF))))
            .defaultValue(false)
            .build()
      );
   private final Setting<String> prefixText = this.sgPrefix
      .add(
         new StringSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(WaypointsModule$WIcon$ConstantPool.const_hoLjPex0nvSbAS5))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(CodeAnalyzer$ConstantPool.const_0AhAQgAg1EiorRx))))
            .defaultValue(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ProxyObjectInputStream$ConstantPool.const_w9sC1eoAPGSrTmA))))
            .visible(() -> !this.prefixRandom.get())
            .build()
      );
   private final Setting<Boolean> prefixSmallCaps = this.sgPrefix
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(StorageBlockListSetting$Builder$ConstantPool.const_vOSq9g9ev69Xd7z))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Expr$Variable$ConstantPool.const_EtIaww79ptaTaGi))))
            .defaultValue(false)
            .visible(() -> !this.prefixRandom.get())
            .build()
      );
   private final Setting<Boolean> suffix = this.sgSuffix
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Expr$Conditional$ConstantPool.const_EM5JydrOGgjbYeI))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(AllMod$1$1$ConstantPool.const_gqvjED1BjJ9FcpP))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> suffixRandom = this.sgSuffix
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(AutoFish$ConstantPool.const_2jxwdbyKDqYdrav))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(BlockSelection$ConstantPool.const_4Q2i9aoeyHqT8v6))))
            .defaultValue(false)
            .build()
      );
   private final Setting<String> suffixText = this.sgSuffix
      .add(
         new StringSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(JsonSerializer$ConstantPool.const_R1ePoIiJGHkoX8e))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(AllMod$1$ConstantPool.const_11x5GCA6FaybqQo))))
            .defaultValue(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Bounce$ConstantPool.const_u5LLCOlWFu2QrAe))))
            .visible(() -> !this.suffixRandom.get())
            .build()
      );
   private final Setting<Boolean> suffixSmallCaps = this.sgSuffix
      .add(
         new BoolSetting.Builder()
            .name(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ByteArrayClassPath$1$ConstantPool.const_noi0DfNpfPelSbf))))
            .description(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(GuiRenderOperation$ConstantPool.const_jlt4Ream21sIUWO))))
            .defaultValue(true)
            .visible(() -> !this.suffixRandom.get())
            .build()
      );
   private static final Pattern antiSpamRegex = Pattern.compile(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(WMeteorPlus$ConstantPool.const_Wv8Ara6BmQI1VBr))));
   private static final Pattern antiClearRegex = Pattern.compile(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(FeetPlace$SurroundType$ConstantPool.const_O21LrQKtYaJj4Cy))));
   private static final Pattern timestampRegex = Pattern.compile(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(NoRender$ConstantPool.const_Pn8wjrqbaoLVkNR))));
   private static final Pattern usernameRegex = Pattern.compile(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(JbossDir$ConstantPool.const_162VdGZjOoq3Lr6))));
   private final Char2CharMap SMALL_CAPS = new Char2CharOpenHashMap();
   private final SimpleDateFormat dateFormat = new SimpleDateFormat(
      7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(HighwayBuilder$State$11$ConstantPool.const_rXAMbHTaJ7DVWnl)))
   );
   public final IntList lines = new IntArrayList();
   private static final List<BetterChat.CustomHeadEntry> CUSTOM_HEAD_ENTRIES = new ArrayList<>();
   private static final Pattern TIMESTAMP_REGEX = Pattern.compile(
      7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(TitleScreenCredits$Credit$ConstantPool.const_4il87VyxBy41ii1)))
   );
   private final List<Pattern> filterRegexList = new ArrayList<>();
   private static final Pattern coordRegex = Pattern.compile(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(MethodInfo$ConstantPool.const_XxUKAIAqJLTb1Ys))));

   public BetterChat() {
      super(
         Categories.Misc,
         7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(StatusEffectListSetting$Builder$ConstantPool.const_D26Fq7i64I9haW2))),
         new StringBuilder(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(WaspCommand$ConstantPool.const_BOACHF3KqAnDyqf)))),
         7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(BlockSetting$Builder$ConstantPool.const_jpBv4SdiFaXa77d)))
      );
      String[] a = 7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(HotSwapper$ConstantPool.const_kFYTv0Fzij1LaWj)))
         .split(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(FastClimb$ConstantPool.const_5i4nLdGbqvpBtXG))));
      String[] b = 7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(DamageUtils$1$ConstantPool.const_uTRSbnHIS4PtCO6)))
         .split(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ModuleArgumentType$ConstantPool.const_AFF6JymwYxTy9MO))));

      for (int i = 0; i < a.length; i++) {
         this.SMALL_CAPS.put(a[i].charAt(0), b[i].charAt(0));
      }

      this.compileFilterRegexList();
   }

   @EventHandler
   private void onMessageReceive(ReceiveMessageEvent event) {
      Text message = event.getMessage();
      if (this.filterRegex.get()) {
         String messageString = message.getString();

         for (Pattern pattern : this.filterRegexList) {
            if (pattern.matcher(messageString).find()) {
               event.cancel();
               return;
            }
         }
      }

      if (this.antiClear.get()) {
         String messageString = message.getString();
         if (antiClearRegex.matcher(messageString).find()) {
            MutableText newMessage = Text.method_43473();
            TextVisitor.visit(
               message,
               (text, style, string) -> {
                  Matcher antiClearMatcher = antiClearRegex.matcher(string);
                  if (antiClearMatcher.find()) {
                     newMessage.method_10852(
                        Text.method_43470(antiClearMatcher.replaceAll(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(SocksAuthRequest$ConstantPool.const_2vjAaTCQALpK1iw)))))
                           .method_10862(style)
                     );
                  } else {
                     newMessage.method_10852(text.method_27662().method_10862(style));
                  }

                  return Optional.empty();
               },
               Style.field_24360
            );
            message = newMessage;
         }
      }

      if (this.antiSpam.get()) {
         Text antiSpammed = this.appendAntiSpam(message);
         if (antiSpammed != null) {
            message = antiSpammed;
         }
      }

      if (this.timestamps.get()) {
         Text timestamp = Text.method_43470("<" + this.dateFormat.format(new Date()) + "> ").method_27692(Formatting.field_1080);
         message = Text.method_43473().method_10852(timestamp).method_10852(message);
      }

      event.setMessage(message);
   }

   @EventHandler
   private void onMessageSend(SendMessageEvent event) {
      String message = event.message;
      if (this.annoy.get()) {
         message = this.applyAnnoy(message);
      }

      if (this.fancy.get()) {
         message = this.applyFancy(message);
      }

      message = this.getPrefix() + message + this.getSuffix();
      if (this.coordsProtection.get() && this.containsCoordinates(message)) {
         MutableText warningMessage = Text.method_43470(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(LightOverlay$1$ConstantPool.const_NxwpF3WIf6M5CBg))));
         MutableText sendButton = this.getSendButton(message);
         warningMessage.method_10852(sendButton);
         ChatUtils.sendMsg(warningMessage);
         event.cancel();
      } else {
         event.message = message;
      }
   }

   private Text appendAntiSpam(Text text) {
      String textString = text.getString();
      Text returnText = null;
      int messageIndex = -1;
      List<ChatHudLine> messages = ((ChatHudAccessor)mc.field_1705.method_1743()).getMessages();
      if (messages.isEmpty()) {
         return null;
      } else {
         for (int i = 0; i < Math.min(this.antiSpamDepth.get(), messages.size()); i++) {
            String stringToCheck = messages.get(i).comp_893().getString();
            Matcher timestampMatcher = timestampRegex.matcher(stringToCheck);
            if (timestampMatcher.find()) {
               stringToCheck = stringToCheck.substring(8);
            }

            if (textString.equals(stringToCheck)) {
               messageIndex = i;
               returnText = text.method_27661()
                  .method_10852(
                     Text.method_43470(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(HudElement$ConstantPool.const_5P9L7UxNsx8EFYZ)))).method_27692(Formatting.field_1080)
                  );
               break;
            }

            Matcher matcher = antiSpamRegex.matcher(stringToCheck);
            if (matcher.find()) {
               String group = matcher.group(matcher.groupCount());
               int number = Integer.parseInt(group);
               if (stringToCheck.substring(0, matcher.start()).equals(textString)) {
                  messageIndex = i;
                  returnText = text.method_27661().method_10852(Text.method_43470(" (" + (number + 1) + ")").method_27692(Formatting.field_1080));
                  break;
               }
            }
         }

         if (returnText != null) {
            List<Visible> visible = ((ChatHudAccessor)mc.field_1705.method_1743()).getVisibleMessages();
            int start = -1;

            for (int i = 0; i < messageIndex; i++) {
               start += this.lines.getInt(i);
            }

            for (int i = this.lines.getInt(messageIndex); i > 0; i--) {
               visible.remove(start + 1);
            }

            messages.remove(messageIndex);
            this.lines.removeInt(messageIndex);
         }

         return returnText;
      }
   }

   public void removeLine(int index) {
      if (index >= this.lines.size()) {
         if (this.antiSpam.get()) {
            this.error(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(MatchesPattern$Checker$ConstantPool.const_vKI5NdekWvVjWK0))), new Object[0]);
            this.antiSpam.set(false);
         }
      } else {
         this.lines.removeInt(index);
      }
   }

   public static void registerCustomHead(String prefix, Identifier texture) {
      CUSTOM_HEAD_ENTRIES.add(new BetterChat.CustomHeadEntry(prefix, texture));
   }

   public int modifyChatWidth(int width) {
      return this.isActive() && this.playerHeads.get() ? width + 10 : width;
   }

   public void drawPlayerHead(DrawContext context, Visible line, int y, int color) {
      if (this.isActive() && this.playerHeads.get()) {
         if (((IChatHudLineVisible)line).meteor$isStartOfEntry()) {
            RenderSystem.enableBlend();
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, Color.toRGBAA(color) / WMeteorWindow$WMeteorHeader$ConstantPool.const_ypqgyFSi9Go7c5G);
            this.drawTexture(context, (IChatHudLine)line, y);
            RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
            RenderSystem.disableBlend();
         }

         context.method_51448().method_46416(SocksAuthResponseDecoder$ConstantPool.const_AqNT6QFAretT1fo, 0.0F, 0.0F);
      }
   }

   private void drawTexture(DrawContext context, IChatHudLine line, int y) {
      String text = line.meteor$getText().trim();
      int startOffset = 0;

      try {
         Matcher m = TIMESTAMP_REGEX.matcher(text);
         if (m.find()) {
            startOffset = m.end() + 1;
         }
      } catch (IllegalStateException var9) {
      }

      for (BetterChat.CustomHeadEntry entry : CUSTOM_HEAD_ENTRIES) {
         if (text.startsWith(entry.prefix(), startOffset)) {
            context.method_25293(entry.texture(), 0, y, 8, 8, 0.0F, 0.0F, 64, 64, 64, 64);
            return;
         }
      }

      GameProfile sender = this.getSender(line, text);
      if (sender != null) {
         PlayerListEntry entryx = mc.method_1562().method_2871(sender.getId());
         if (entryx != null) {
            Identifier skin = entryx.method_52810().comp_1626();
            context.method_25293(skin, 0, y, 8, 8, Burrow$ConstantPool.const_WTqcW2ObAZAiSk4, ClipAtLedgeEvent$ConstantPool.const_8Ako4qWqefl6n5E, 8, 8, 64, 64);
            context.method_25293(
               skin,
               0,
               y,
               8,
               8,
               Main$OperatingSystem$ConstantPool.const_YqlRPnJufFNv46j,
               ApplyTransformationEvent$ConstantPool.const_17pnkyr6FCa5yMs,
               8,
               8,
               64,
               64
            );
         }
      }
   }

   private GameProfile getSender(IChatHudLine line, String text) {
      GameProfile sender = line.meteor$getSender();
      if (sender == null) {
         Matcher usernameMatcher = usernameRegex.matcher(text);
         if (usernameMatcher.matches()) {
            String username = usernameMatcher.group(1);
            PlayerListEntry entry = mc.method_1562().method_2874(username);
            if (entry != null) {
               sender = entry.method_2966();
            }
         }
      }

      return sender;
   }

   private String applyAnnoy(String message) {
      StringBuilder sb = new StringBuilder(message.length());
      boolean upperCase = true;

      for (int cp : message.codePoints().toArray()) {
         if (upperCase) {
            sb.appendCodePoint(Character.toUpperCase(cp));
         } else {
            sb.appendCodePoint(Character.toLowerCase(cp));
         }

         upperCase = !upperCase;
      }

      return sb.toString();
   }

   private String applyFancy(String message) {
      StringBuilder sb = new StringBuilder();

      for (char ch : message.toCharArray()) {
         sb.append(this.SMALL_CAPS.getOrDefault(ch, ch));
      }

      return sb.toString();
   }

   private void compileFilterRegexList() {
      this.filterRegexList.clear();

      for (int i = 0; i < this.regexFilters.get().size(); i++) {
         try {
            this.filterRegexList.add(Pattern.compile(this.regexFilters.get().get(i)));
         } catch (PatternSyntaxException var4) {
            String removed = this.regexFilters.get().remove(i);
            this.error(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ColorListSetting$ConstantPool.const_rWGrWyj2gcaYZtU))), new Object[]{removed});
         }
      }
   }

   private String getPrefix() {
      return this.prefix.get()
         ? this.getAffix(this.prefixText.get(), this.prefixSmallCaps.get(), this.prefixRandom.get())
         : 7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ESPBlock$1$ConstantPool.const_fb0jgWqSFOdLK1j)));
   }

   private String getSuffix() {
      return this.suffix.get()
         ? this.getAffix(this.suffixText.get(), this.suffixSmallCaps.get(), this.suffixRandom.get())
         : 7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Mesh$Attrib$ConstantPool.const_MR1cbxwoGron7gr)));
   }

   private String getAffix(String text, boolean smallcaps, boolean random) {
      if (random) {
         return String.format(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(GuiTab$GuiScreen$ConstantPool.const_hBv093mUH29Vnin))), Utils.random(0, 1000));
      } else {
         return smallcaps ? this.applyFancy(text) : text;
      }
   }

   private boolean containsCoordinates(String message) {
      return coordRegex.matcher(message).find();
   }

   private MutableText getSendButton(String message) {
      MutableText sendButton = Text.method_43470(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(JsonSerializer$ConstantPool.const_T0Y7FTR6duDqbdW))));
      MutableText hintBaseText = Text.method_43470(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Vfs$DefaultUrlTypes$7$ConstantPool.const_OZIgE2KYyROeqKA))));
      MutableText hintMsg = Text.method_43470(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Value$Number$ConstantPool.const_bqnHyvqXveyNtDX))));
      hintMsg.method_10862(hintBaseText.method_10866().method_27706(Formatting.field_1080));
      hintBaseText.method_10852(hintMsg);
      hintBaseText.method_10852(Text.method_43470("\n" + message));
      sendButton.method_10862(
         sendButton.method_10866()
            .method_27706(Formatting.field_1079)
            .method_10958(
               new MeteorClickEvent(
                  Action.field_11750, Commands.get(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(NBSSongDecoder$ConstantPool.const_MqwHciToO7jU9e8)))).toString(message)
               )
            )
            .method_10949(new HoverEvent(net.minecraft.text.HoverEvent.Action.field_24342, hintBaseText))
      );
      return sendButton;
   }

   public boolean isInfiniteChatBox() {
      return this.isActive() && this.infiniteChatBox.get();
   }

   public boolean isLongerChat() {
      return this.isActive() && this.longerChatHistory.get();
   }

   public boolean keepHistory() {
      return this.isActive() && this.keepHistory.get();
   }

   public int getExtraChatLines() {
      return this.longerChatLines.get();
   }

   static {
      registerCustomHead(
         7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(JarDirClassPath$ConstantPool.const_FeS7ENFKhcL2NBl))),
         MeteorClient.identifier(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(BetterTooltips$DisplayWhen$ConstantPool.const_n4iK6fOTYibQmO9))))
      );
      registerCustomHead(
         7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(Capes$Cape$ConstantPool.const_gTTep4pRlovSIBr))),
         MeteorClient.identifier(7vOAt93sB6(UiLVDQ6037(Bpb1TqwZXN(ESP$1$ConstantPool.const_9sUgqFqZAjBQv8s))))
      );
   }

   private record CustomHeadEntry(String prefix, Identifier texture) {
   }
}
