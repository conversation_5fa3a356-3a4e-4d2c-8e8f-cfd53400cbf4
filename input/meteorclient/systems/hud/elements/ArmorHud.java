package meteordevelopment.meteorclient.systems.hud.elements;

import io.netty.handler.codec.socks.SocksAuthResponseDecoder$State$ConstantPool;
import io.netty.handler.codec.socks.SocksInitResponseDecoder$State$ConstantPool;
import io.netty.handler.codec.socks.SocksResponseType$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5InitialRequestDecoder$1$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5PasswordAuthResponseDecoder$1$ConstantPool;
import javassist.bytecode.AnnotationsAttribute$ConstantPool;
import javassist.bytecode.ConstInfoPadding$ConstantPool;
import javassist.bytecode.TypeAnnotationsAttribute$SubCopier$ConstantPool;
import javassist.compiler.Token$ConstantPool;
import javassist.expr.MethodCall$ConstantPool;
import javax.annotation.RegEx$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.gui.renderer.packer.TexturePacker$Image$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.SettingsWidgetFactory$Factory$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.containers.WSection$WHeader$ConstantPool;
import meteordevelopment.meteorclient.renderer.Texture$ConstantPool;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StatusEffectListSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.Hud;
import meteordevelopment.meteorclient.systems.hud.HudElement;
import meteordevelopment.meteorclient.systems.hud.HudElementInfo;
import meteordevelopment.meteorclient.systems.hud.HudRenderer;
import meteordevelopment.meteorclient.systems.modules.Categories$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.BedAura$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoBuy$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.ElytraAndArmor$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.SkyLadder$BaseMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AntiVoid$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.TridentBoost$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.FastUse$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.SpeedMine$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.BreakIndicators$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$Position$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.StorageESP$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Ambience$Custom$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.AutoMount$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Collisions$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$Floor$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Nuker$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.waypoints.Waypoints$ConstantPool;
import meteordevelopment.meteorclient.utils.entity.ProjectileEntitySimulator$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralMessageType$ConstantPool;
import meteordevelopment.meteorclient.utils.render.ByteTexture$ConstantPool;
import meteordevelopment.meteorclient.utils.render.PeekScreen$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.world.BlockEntityIterator$ConstantPool;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;

public class ArmorHud extends HudElement {
   public static final HudElementInfo<ArmorHud> INFO = new HudElementInfo<>(
      Hud.GROUP,
      734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(SocksInitResponseDecoder$State$ConstantPool.const_KARcKK9QmDU0GSA))),
      734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(ConstInfoPadding$ConstantPool.const_j5jovh1P17OQCbC))),
      ArmorHud::new
   );
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgDurability = this.settings
      .createGroup(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(ProjectileEntitySimulator$ConstantPool.const_JcFJmyi5Eq1pkTy))));
   private final SettingGroup sgBackground = this.settings
      .createGroup(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(TexturePacker$Image$ConstantPool.const_6F3lfJDJp2KetK6))));
   private final Setting<ArmorHud.Orientation> orientation = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                        .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(HighwayBuilder$Floor$ConstantPool.const_oQ64nwSQ1oe6anC)))))
                     .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(AntiVoid$Mode$ConstantPool.const_1OKQ1hBAVMFl6hJ)))))
                  .defaultValue(ArmorHud.Orientation.Horizontal))
               .onChanged(val -> this.calculateSize()))
            .build()
      );
   private final Setting<Boolean> flipOrder = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(MethodCall$ConstantPool.const_Q4TFGayoxr8cKXQ))))
            .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(BlockEntityIterator$ConstantPool.const_JyjqQqJKc0a6Tgo))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Double> scale = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(Socks5InitialRequestDecoder$1$ConstantPool.const_n6tetVxrhNgDSqi))))
            .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(TridentBoost$ConstantPool.const_qNNF4qNb77ZLYUO))))
            .defaultValue(Token$ConstantPool.const_p6wWCWOfnJ35TTx)
            .onChanged(aDouble -> this.calculateSize())
            .min(1.0)
            .sliderRange(1.0, WSection$WHeader$ConstantPool.const_OGdnFeudtuSdDOt)
            .build()
      );
   private final Setting<Integer> border = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(FastUse$ConstantPool.const_ggxnVVD6Tv7WWQA))))
            .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(SettingsWidgetFactory$Factory$ConstantPool.const_IqXXLva2VYWC3Di))))
            .defaultValue(0)
            .onChanged(integer -> this.calculateSize())
            .build()
      );
   private final Setting<ArmorHud.Durability> durability = this.sgDurability
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                        .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(ByteTexture$ConstantPool.const_rZJr0TnjVmATHg2)))))
                     .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(AutoBuy$ConstantPool.const_nIjO6pzXyridU9T)))))
                  .defaultValue(ArmorHud.Durability.Bar))
               .onChanged(durability1 -> this.calculateSize()))
            .build()
      );
   private final Setting<SettingColor> durabilityColor = this.sgDurability
      .add(
         new ColorSetting.Builder()
            .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(AstralMessageType$ConstantPool.const_Jtz2VjL5yrD8NAQ))))
            .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(Ambience$Custom$ConstantPool.const_NwG0oqrPLJsZIDX))))
            .visible(() -> this.durability.get() == ArmorHud.Durability.Total || this.durability.get() == ArmorHud.Durability.Percentage)
            .defaultValue(new SettingColor())
            .build()
      );
   private final Setting<Boolean> durabilityShadow = this.sgDurability
      .add(
         new BoolSetting.Builder()
            .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(Nametags$Position$ConstantPool.const_6e0A1orSutdxqL9))))
            .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(StatusEffectListSetting$Builder$ConstantPool.const_Er4MnW4npdfGjQ7))))
            .visible(() -> this.durability.get() == ArmorHud.Durability.Total || this.durability.get() == ArmorHud.Durability.Percentage)
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> background = this.sgBackground
      .add(
         new BoolSetting.Builder()
            .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(Collisions$ConstantPool.const_cVXg61liIXdIB7D))))
            .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(SkyLadder$BaseMode$ConstantPool.const_Eaj6YiLDjk3vomm))))
            .defaultValue(false)
            .build()
      );
   private final Setting<SettingColor> backgroundColor = this.sgBackground
      .add(
         new ColorSetting.Builder()
            .name(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(AutoMount$ConstantPool.const_wvo6FAD1nY9Vlry))))
            .description(734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(Nuker$Mode$ConstantPool.const_RWt9qh1SJGWvLNT))))
            .visible(this.background::get)
            .defaultValue(new SettingColor(25, 25, 25, 50))
            .build()
      );

   public ArmorHud() {
      super(INFO);
      this.calculateSize();
   }

   @Override
   public void setSize(double width, double height) {
      super.setSize(width + this.border.get() * 2, height + this.border.get() * 2);
   }

   private void calculateSize() {
      switch ((ArmorHud.Orientation)this.orientation.get()) {
         case Horizontal:
            this.setSize(
               SocksResponseType$ConstantPool.const_uoU21jaeCJOjGdm * this.scale.get() * TypeAnnotationsAttribute$SubCopier$ConstantPool.const_xA6hCSXxFGQwOLw
                  + BreakIndicators$ConstantPool.const_Ywi1he2Osuo6fXP,
               Texture$ConstantPool.const_nGxiIiLCqwPedKS * this.scale.get()
            );
            break;
         case Vertical:
            this.setSize(
               StorageESP$ConstantPool.const_shlUD9s9rnwlQxT * this.scale.get(),
               SpeedMine$ConstantPool.const_9ildydozO8UqUFR * this.scale.get() * AnnotationsAttribute$ConstantPool.const_d6qF1WHztYlaAfI
                  + ElytraAndArmor$ConstantPool.const_L2WAQGbohY4IqZB
            );
      }
   }

   @Override
   public void render(HudRenderer renderer) {
      double x = this.x;
      double y = this.y;
      int slot = this.flipOrder.get() ? 3 : 0;

      for (int position = 0; position < 4; position++) {
         ItemStack itemStack = this.getItem(slot);
         double armorX;
         double armorY;
         if (this.orientation.get() == ArmorHud.Orientation.Vertical) {
            armorX = x;
            armorY = y + position * 18 * this.scale.get();
         } else {
            armorX = x + position * 18 * this.scale.get();
            armorY = y;
         }

         renderer.item(
            itemStack, (int)armorX, (int)armorY, this.scale.get().floatValue(), itemStack.method_7963() && this.durability.get() == ArmorHud.Durability.Bar
         );
         if (itemStack.method_7963()
            && !this.isInEditor()
            && this.durability.get() != ArmorHud.Durability.Bar
            && this.durability.get() != ArmorHud.Durability.None) {
            String message = switch ((ArmorHud.Durability)this.durability.get()) {
               case Total -> Integer.toString(itemStack.method_7936() - itemStack.method_7919());
               case Percentage -> Integer.toString(
                  Math.round((itemStack.method_7936() - itemStack.method_7919()) * Categories$ConstantPool.const_Uqf97uwluLybvwb / itemStack.method_7936())
               );
               default -> 734qQwGJ66(9Ya9VDO1Vp(oITX3wXkQV(BedAura$ConstantPool.const_CMwXwbDjar1S1rT)));
            };
            double messageWidth = renderer.textWidth(message);
            if (this.orientation.get() == ArmorHud.Orientation.Vertical) {
               armorX = x + RegEx$ConstantPool.const_wrhpX68yecQ4jt7 * this.scale.get() - messageWidth / PeekScreen$ConstantPool.const_rb3i14c5D6QLEDp;
               armorY = y
                  + 18 * position * this.scale.get()
                  + (SocksAuthResponseDecoder$State$ConstantPool.const_SO6R7QOnNj26xEr * this.scale.get() - renderer.textHeight());
            } else {
               armorX = x
                  + 18 * position * this.scale.get()
                  + Waypoints$ConstantPool.const_3Fklz3W4yYdHbJA * this.scale.get()
                  - messageWidth / Socks5PasswordAuthResponseDecoder$1$ConstantPool.const_UCzMeT2jWlTx2YW;
               armorY = y + (this.getHeight() - renderer.textHeight());
            }

            renderer.text(message, armorX, armorY, this.durabilityColor.get(), this.durabilityShadow.get());
         }

         if (this.flipOrder.get()) {
            slot--;
         } else {
            slot++;
         }
      }

      if (this.background.get()) {
         renderer.quad(this.x, this.y, this.getWidth(), this.getHeight(), this.backgroundColor.get());
      }
   }

   private ItemStack getItem(int i) {
      if (this.isInEditor()) {
         return switch (i) {
            case 1 -> Items.field_22029.method_7854();
            case 2 -> Items.field_22028.method_7854();
            case 3 -> Items.field_22027.method_7854();
            default -> Items.field_22030.method_7854();
         };
      } else {
         return MeteorClient.mc.field_1724.method_31548().method_7372(i);
      }
   }

   public static enum Durability {
      None,
      Bar,
      Total,
      Percentage;
   }

   public static enum Orientation {
      Horizontal,
      Vertical;
   }
}
