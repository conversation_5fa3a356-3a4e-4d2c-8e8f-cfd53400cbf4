package meteordevelopment.meteorclient.systems.hud;

public final class XAnchor$ConstantPool {
   public static String const_SdaqqduQSVmzbG2 = Ygb572cjrs(AVarGnn1r1("͟ͅ"));
   public static int const_OGBXIFGE0wXV9Gi = (int)(2991080094149039800L ^ -2991080094147185733L) ^ (int)(918054553 ^ -916733500);
   public static String const_aTik77oQGu0DZ21 = JI8oj45wTl(AVarGnn1r1("selectionLineWidth"));
   public static String const_xh1tqQCeL79SLZj = 0bJjlNOwMn(AVarGnn1r1("ȑȼȦȥȹȴȬȦɵȷȴȶȾȲȧȺȠȻȱɻ"));
   public static String const_NPaJ5Aq4tD4297R = FdwV0IcZBK(AVarGnn1r1("ňťſżŠŭŵſĬŮŭůŧūžţŹŢŨĢ"));
   public static int const_o7SWqB9JRJoGB4g = (int)((int)(275143113 ^ 1709669022) ^ ((int)-358991546L ^ -1625613686));
   public static String const_JSQwMwzqDFSA89V = WvFC5vZfK1(AVarGnn1r1("͉ͮͶ̡͵̡ͩͤͲͩ͠ͱͤͲ̡͠ͳ̡ͤͳͤͯͥͤͳ̯ͤͥ"));
   public static String const_HcW7Pa6gTKtwhRp = syjVS62OJT(AVarGnn1r1("!90\"4|\"90!4|<>54"));
   public static String const_X7aUV2naIGKktMw = BZcW0HJSBw(AVarGnn1r1("ߺ߶߯\u07baߵ\u07fb߸ߴ\u07fcߤ\u07baߧ߲ߥ\u07baߣ߾ߴ\u07fc"));
   public static int const_ZfNJQzfUqfgTQ1J = (int)((int)(-758297811 ^ -661223229) ^ (int)(680918817 ^ 583940708));
   public static String const_6LtEqw6FY25otpL = ZHljwAYnFH(AVarGnn1r1("ŷųũũ"));
   public static String const_2kOrozDaPqB6Avm = B6IBTDUoEV(AVarGnn1r1("ΔλξβιΣΘΧΣξθιΤΔϥ΄·ζδμβΣ"));
   public static String const_tTNmlQNVpfaaIAn = axPv64YIrd(AVarGnn1r1("ՓմամթճմթգճՓԲՃՐագիեմ"));
   public static String const_iHYjdOQaiYlaKOt = Duv6IFemYc(AVarGnn1r1("I1#$K/( I\n\u000f\u0004"));
}
