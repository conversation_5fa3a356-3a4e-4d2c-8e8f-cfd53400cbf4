package meteordevelopment.meteorclient.systems.accounts.types;

import java.util.Optional;
import meteordevelopment.meteorclient.systems.accounts.Account;
import meteordevelopment.meteorclient.systems.accounts.AccountType;
import meteordevelopment.meteorclient.utils.misc.CPSUtils$ConstantPool;
import net.minecraft.client.session.Session;
import net.minecraft.util.Uuids;

public class CrackedAccount extends Account<CrackedAccount> {
   public CrackedAccount(String name) {
      super(AccountType.Cracked, name);
   }

   @Override
   public boolean fetchInfo() {
      this.cache.username = this.name;
      return true;
   }

   @Override
   public boolean login() {
      super.login();
      this.cache.loadHead();
      setSession(
         new Session(
            this.name,
            Uuids.method_43344(this.name),
            JByqtbdaVe(gNUsWVVpmk(8LJ2U1wrAJ(CPSUtils$ConstantPool.const_g7TynDthNVSm8oZ))),
            Optional.empty(),
            Optional.empty(),
            net.minecraft.client.session.Session.AccountType.field_1988
         )
      );
      return true;
   }

   @Override
   public boolean equals(Object o) {
      return !(o instanceof CrackedAccount) ? false : ((CrackedAccount)o).getUsername().equals(this.getUsername());
   }
}
