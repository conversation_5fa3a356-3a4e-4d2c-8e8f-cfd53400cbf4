package meteordevelopment.meteorclient.systems.accounts.types;

import com.mojang.authlib.Environment;
import com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService;
import de.florianmichael.waybackauthlib.InvalidCredentialsException;
import de.florianmichael.waybackauthlib.WaybackAuthLib;
import java.util.Optional;
import javassist.CannotCompileException$ConstantPool;
import javassist.ClassPoolTail$ConstantPool;
import javassist.bytecode.StackMap$Printer$ConstantPool;
import javassist.expr.Expr$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.commands.commands.DisconnectCommand$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.FovCommand$ConstantPool;
import meteordevelopment.meteorclient.events.world.BlockActivateEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorTriangle$ConstantPool;
import meteordevelopment.meteorclient.mixin.MinecraftClientAccessor;
import meteordevelopment.meteorclient.mixin.YggdrasilMinecraftSessionServiceAccessor;
import meteordevelopment.meteorclient.settings.FontFaceSetting$Builder$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.Account;
import meteordevelopment.meteorclient.systems.accounts.AccountType;
import meteordevelopment.meteorclient.systems.accounts.TokenAccount;
import meteordevelopment.meteorclient.systems.modules.movement.NoFall$PlacedItem$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.SafeWalk$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.Rotation$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.BossStack$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.LightOverlay$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.TimeChanger$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WaypointsModule$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.NbtException;
import meteordevelopment.meteorclient.utils.player.PlayerUtils$1$ConstantPool;
import net.minecraft.client.session.Session;
import net.minecraft.nbt.NbtCompound;
import org.jetbrains.annotations.Nullable;

public class TheAlteningAccount extends Account<TheAlteningAccount> implements TokenAccount {
   private static final Environment ENVIRONMENT = new Environment(
      J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(ClassPoolTail$ConstantPool.const_IIrtjpNiGeCvebq))),
      J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(FovCommand$ConstantPool.const_qnZLNTEQywYviLw))),
      J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(BlockActivateEvent$ConstantPool.const_AyG2t9wD4SIVwuM)))
   );
   private static final YggdrasilAuthenticationService SERVICE = new YggdrasilAuthenticationService(
      ((MinecraftClientAccessor)MeteorClient.mc).getProxy(), ENVIRONMENT
   );
   private String token;
   @Nullable
   private WaybackAuthLib auth;

   public TheAlteningAccount(String token) {
      super(AccountType.TheAltening, token);
      this.token = token;
   }

   @Override
   public boolean fetchInfo() {
      this.auth = this.getAuth();

      try {
         this.auth.logIn();
         this.cache.username = this.auth.getCurrentProfile().getName();
         this.cache.uuid = this.auth.getCurrentProfile().getId().toString();
         this.cache.loadHead();
         return true;
      } catch (InvalidCredentialsException var2) {
         MeteorClient.LOG.error(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(BossStack$ConstantPool.const_pTKWiNGU66N1reD))));
         return false;
      } catch (Exception var3) {
         MeteorClient.LOG.error(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(StackMap$Printer$ConstantPool.const_oynu6nW2d6OqGgT))));
         return false;
      }
   }

   @Override
   public boolean login() {
      if (this.auth == null) {
         return false;
      } else {
         applyLoginEnvironment(
            SERVICE,
            YggdrasilMinecraftSessionServiceAccessor.createYggdrasilMinecraftSessionService(SERVICE.getServicesKeySet(), SERVICE.getProxy(), ENVIRONMENT)
         );

         try {
            setSession(
               new Session(
                  this.auth.getCurrentProfile().getName(),
                  this.auth.getCurrentProfile().getId(),
                  this.auth.getAccessToken(),
                  Optional.empty(),
                  Optional.empty(),
                  net.minecraft.client.session.Session.AccountType.field_1988
               )
            );
            return true;
         } catch (Exception var2) {
            MeteorClient.LOG.error(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(DisconnectCommand$ConstantPool.const_jCMPNUkrlT2JWq4))));
            return false;
         }
      }
   }

   private WaybackAuthLib getAuth() {
      WaybackAuthLib auth = new WaybackAuthLib(ENVIRONMENT.servicesHost());
      auth.setUsername(this.name);
      auth.setPassword(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(FontFaceSetting$Builder$ConstantPool.const_BsZq1A53Dr4JAil))));
      return auth;
   }

   @Override
   public String getToken() {
      return this.token;
   }

   @Override
   public NbtCompound toTag() {
      NbtCompound tag = new NbtCompound();
      tag.method_10582(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(CannotCompileException$ConstantPool.const_7DmyxcOOo9aAuKN))), this.type.name());
      tag.method_10582(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(TimeChanger$ConstantPool.const_qwW1KwGdDKLuFKI))), this.name);
      tag.method_10582(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(PlayerUtils$1$ConstantPool.const_IGToy5Bl0DDufQQ))), this.token);
      tag.method_10566(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(WaypointsModule$ConstantPool.const_RcIwFQQVQGVOtA6))), this.cache.toTag());
      return tag;
   }

   public TheAlteningAccount fromTag(NbtCompound tag) {
      if (tag.method_10545(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(NoFall$PlacedItem$ConstantPool.const_b70LGWA9j2Qnth1))))
         && tag.method_10545(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(SafeWalk$ConstantPool.const_ddq4qnYguWcNiKh))))
         && tag.method_10545(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(Rotation$ConstantPool.const_57w7FqwlW4SY0h6))))) {
         this.name = tag.method_10558(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(Expr$ConstantPool.const_ITjRVoYL3QNDNYd))));
         this.token = tag.method_10558(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(WMeteorTriangle$ConstantPool.const_lTLfLOwTHobGYrT))));
         this.cache.fromTag(tag.method_10562(J4gNnYQ6L5(pgDjeAaeuX(rzdFL3gTu4(LightOverlay$ConstantPool.const_BriVvIYW9BgDgVq)))));
         return this;
      } else {
         throw new NbtException();
      }
   }
}
