package meteordevelopment.meteorclient.gui.screens.settings;

public final class StorageBlockListSettingScreen$BlockEntityTypeInfo$ConstantPool {
   public static int const_AQtGqrMLshriazD = (int)((int)(1373558356 ^ 445106760) ^ ((int)172768118L ^ -1947492908));
   public static int const_0IRnE4JibOvb4IG = (int)(8634989593246075144L ^ 8634989592676478785L) ^ (int)(-1917453901 ^ -1279764465);
   public static String const_eyc4ESABZbeWORm = hFgxV34rL2(DWzKLn44ao("߱ߵߗ߸ߟ߷߬"));
   public static String const_bmeAlwBXq6DrxQj = VkBwe0RNHE(DWzKLn44ao("Ɇɯɬɾ"));
   public static String const_RnGT0DY7OHlxSxB = 1ijg1oSvBN(DWzKLn44ao("ג\u05ceדו\u05c8\u05cf׆\u058c\u05caׄט"));
   public static String const_htYFtStbqua3BSk = E1a36tTq2e(DWzKLn44ao("ՇՄՑՑՐ"));
   public static String const_LNgf52BRP7JNiGj = LPaO9dAr0j(DWzKLn44ao("ǇǻǶƳǽǲǾǶƳǼǵƳǧǻǶƳǣǡǼǫǪƽ"));
   public static double const_1nFHUqj4Og7C6SV = Double.longBitsToDouble(
      -9205842011604575320L ^ 5520315772451264250L ^ -3185588962471627152L ^ 2375932978413550370L
   );
   public static String const_ZqFbbjAArSGmKg0 = DaLWSv6vTG(DWzKLn44ao("݀ݑ\u074cݐݐ\u074b݂݊ݑݼݗ݂ݑ݄݆ݗ"));
}
