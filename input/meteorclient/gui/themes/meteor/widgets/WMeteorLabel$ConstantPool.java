package meteordevelopment.meteorclient.gui.themes.meteor.widgets;

public final class WMeteorLabel$ConstantPool {
   public static String const_NsnwYEyvBOtir6E = 2Vgs5nNnJl(Frl8Nt4vtu("\u000e\u00171q"));
   public static String const_BGabnwbbCbwOfBe = dMBOC0PxqY(Frl8Nt4vtu("ܟܸܭܯܧݬܥܿݬܩܡܼܸܵ"));
   public static int const_sx8G0S3DWb6AwTS = (int)(2339763562590506123L ^ -2339763562225661742L) ^ (int)-770724913L ^ 1538306004;
   public static String const_SyL1jrbsL11mlFv = yoLAGIsQNd(Frl8Nt4vtu("ǍǁǃǞǁǀǋǀǚ"));
   public static String const_LAcVRGYF7TQXQgu = JavO6BAlSn(Frl8Nt4vtu("ФЫбЬѨЧзРФЮ"));
   public static double const_Yi9Cv7Gkbleea4L = Double.longBitsToDouble(
      -6779627722007852162L ^ -1101326452002694720L ^ 3451959590063648444L ^ 4516392555592072194L
   );
   public static String const_Fsq7e2GOa0uCviO = B2jOjWNkFA(Frl8Nt4vtu("ۇےہ۔ۖۇڞہے\u06dd۔ۖ"));
   public static String const_97dad6DQoATMVd8 = 1ursPDJuK5(Frl8Nt4vtu("莼恇蕎蒍紇"));
   public static double const_3tl8Biu0p1gQniw = Double.longBitsToDouble(
      -8172298132903127270L ^ 4699088311583907549L ^ -6481063975023747174L ^ 2963732968217925213L
   );
   public static int const_BEdWtUj1dtwbKhT = (int)((int)(-1688455065 ^ -2019731605) ^ ((int)-1476130270L ^ -1262131783));
   public static String const_Ig76rtg8wXnWEqj = W2BniQJdo4(Frl8Nt4vtu("ŰŌŁĄŇŋňŋŖĄŋłĄŐŌŁĄŃŖŅŗŗĊ"));
   public static double const_nvySSOZ1VjoeJ5v = Double.longBitsToDouble(
      116711425200090489L ^ -1994207378071745753L ^ -9155091875974063383L ^ 6523058823686268205L
   );
}
