package meteordevelopment.meteorclient.gui.themes.meteor.widgets;

import io.netty.handler.codec.socks.SocksAuthScheme$ConstantPool;
import io.netty.handler.codec.socksx.v4.DefaultSocks4CommandResponse$ConstantPool;
import javassist.util.proxy.RuntimeSupport$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorWidget;
import meteordevelopment.meteorclient.gui.widgets.WHorizontalSeparator;
import meteordevelopment.meteorclient.mixininterface.IExplosionS2CPacket$ConstantPool;
import meteordevelopment.meteorclient.systems.friends.Friends$ConstantPool;

public class WMeteorHorizontalSeparator extends WHorizontalSeparator implements MeteorWidget {
   public WMeteorHorizontalSeparator(String text) {
      super(text);
   }

   @Override
   protected void onRender(GuiRenderer renderer, double mouseX, double mouseY, double delta) {
      if (this.text == null) {
         this.renderWithoutText(renderer);
      } else {
         this.renderWithText(renderer);
      }
   }

   private void renderWithoutText(GuiRenderer renderer) {
      MeteorGuiTheme theme = this.theme();
      double s = theme.scale(1.0);
      double w = this.width / RuntimeSupport$ConstantPool.const_J4boIBy0tDeVoAj;
      renderer.quad(this.x, this.y + s, w, s, theme.separatorEdges.get(), theme.separatorCenter.get());
      renderer.quad(this.x + w, this.y + s, w, s, theme.separatorCenter.get(), theme.separatorEdges.get());
   }

   private void renderWithText(GuiRenderer renderer) {
      MeteorGuiTheme theme = this.theme();
      double s = theme.scale(SocksAuthScheme$ConstantPool.const_CGoPGntsTnoyDbj);
      double h = theme.scale(1.0);
      double textStart = Math.round(
         this.width / Friends$ConstantPool.const_rUgA5B6z1NzhDV7 - this.textWidth / DefaultSocks4CommandResponse$ConstantPool.const_hh32p1eF2FE64S6 - s
      );
      double textEnd = s + textStart + this.textWidth + s;
      double offsetY = Math.round(this.height / IExplosionS2CPacket$ConstantPool.const_PN4jj2YWbXrfAoB);
      renderer.quad(this.x, this.y + offsetY, textStart, h, theme.separatorEdges.get(), theme.separatorCenter.get());
      renderer.text(this.text, this.x + textStart + s, this.y, theme.separatorText.get(), false);
      renderer.quad(this.x + textEnd, this.y + offsetY, this.width - textEnd, h, theme.separatorCenter.get(), theme.separatorEdges.get());
   }
}
