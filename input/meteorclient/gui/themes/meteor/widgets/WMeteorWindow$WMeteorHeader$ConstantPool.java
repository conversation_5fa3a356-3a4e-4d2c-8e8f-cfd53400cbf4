package meteordevelopment.meteorclient.gui.themes.meteor.widgets;

public final class WMeteorWindow$WMeteorHeader$ConstantPool {
   public static String const_jax4VHXUtNBwTCF = d2VLthqsby(DDKv0SNq7I("ǭǜǈǎǘǎƝǊǕǔǑǘƝǙǏǔǓǖǔǓǚƓ"));
   public static double const_38qV4Mkgcl9aU9Q = Double.longBitsToDouble(
      -8420194161176003018L ^ 6209094896976456378L ^ -8639536245417998967L ^ 7706898539486269701L
   );
   public static String const_PT6yasDCuFbOAte = gOpndbAle9(DDKv0SNq7I("|JAKJ]"));
   public static float const_ypqgyFSi9Go7c5G = Float.intBitsToFloat((int)1464771942L ^ -270415435 ^ (int)(-64451988 ^ 133752511));
   public static String const_tsFw1QbX9DrAqBl = qbigC5PQob(DDKv0SNq7I("϶ϑωΞϓϟϐχΞϜϒϑϝϕύΞϑϘϘύϛϊΞϊϑΞωϟύώΞϟϊΞϘόϑϓΞϊϖϛΞϊϟόϙϛϊΐ"));
   public static double const_GSSwVH4yhZtA6uw = Double.longBitsToDouble(
      -9150823237594613961L ^ -6274365473021142086L ^ 3690976146958641666L ^ 6547596312256824975L
   );
   public static int const_BdkQilENSplGwF8 = (int)((int)-1220026319L ^ -476406590 ^ ((int)-1806343386L ^ -1064821462));
   public static float const_JGenG0uhLrUAWNL = Float.intBitsToFloat((int)(1249347090 ^ -59909750) ^ (int)-1916433425L ^ 87518809);
}
