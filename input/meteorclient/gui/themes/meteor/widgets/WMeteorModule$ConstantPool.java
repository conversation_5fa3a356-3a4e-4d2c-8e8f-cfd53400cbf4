package meteordevelopment.meteorclient.gui.themes.meteor.widgets;

public final class WMeteorModule$ConstantPool {
   public static String const_NfDqQbUJy3LZtej = 5lHbmTe4hx(DO1ZiGSVQS("ΛϒϕΛ"));
   public static double const_Bjb2Vn6Ls3Y1eeq = Double.longBitsToDouble(
      1361701924179885108L ^ 8358136732464125015L ^ 6097962389696345740L ^ 8501667724707290863L
   );
   public static String const_oxO8xIrNt5tRolt = wVhh6Bqeji(DO1ZiGSVQS("њѼѧѯѠѩѢѫѽ"));
   public static double const_grSIP6DJjEdpqnD = Double.longBitsToDouble(
      -2702847462111192053L ^ 4463618327027035362L ^ -4994111963630372590L ^ 7123887657798404603L
   );
   public static int const_s6n1Wiz9Bgynv7h = (int)((int)1594419593L ^ -978266977 ^ ((int)1603046326L ^ -986400787));
}
