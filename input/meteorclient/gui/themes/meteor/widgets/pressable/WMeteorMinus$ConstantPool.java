package meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable;

public final class WMeteorMinus$ConstantPool {
   public static String const_Q36IxEibexufgco = gVv6pdYyeT(UwwySWTIZU("ܥܪܢܹܠܠ"));
   public static double const_wPxBSvQ4anNCipY = Double.longBitsToDouble(
      -7267548023640783346L ^ -1052553359475650861L ^ 831203569750850674L ^ 2434733148857889967L
   );
   public static String const_JcniTfFIQqT6fcP = w6oNhctVhM(UwwySWTIZU("ƥƁƟƂƕƞ"));
   public static String const_8STNretX0VbTl77 = SYVJ2gIzWG(UwwySWTIZU("ְֲַַ\u05f6ֺֺ֮֩"));
   public static String const_NnhmtxuVxJIr4DJ = BSN4qv9WWe(UwwySWTIZU("ƐƗƂƗƖƐǎƆƅƅƆƀƗƐ"));
   public static String const_dg7Bi0rgF5IoJuY = cundIsyWQd(UwwySWTIZU("ǐ"));
}
