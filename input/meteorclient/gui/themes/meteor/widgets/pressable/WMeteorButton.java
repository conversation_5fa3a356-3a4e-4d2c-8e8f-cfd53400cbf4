package meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable;

import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.renderer.packer.GuiTexture;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorWidget;
import meteordevelopment.meteorclient.gui.widgets.pressable.WButton;
import meteordevelopment.meteorclient.settings.FontFaceSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.PopChams$GhostPlayer$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.WaypointsModule$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$StraightBlockPosProvider$2$ConstantPool;

public class WMeteorButton extends W<PERSON>utton implements MeteorWidget {
   public WMeteorButton(String text, GuiTexture texture) {
      super(text, texture);
   }

   @Override
   protected void onRender(GuiRenderer renderer, double mouseX, double mouseY, double delta) {
      MeteorGuiTheme theme = this.theme();
      double pad = this.pad();
      this.renderBackground(renderer, this, this.pressed, this.mouseOver);
      if (this.text != null) {
         renderer.text(
            this.text,
            this.x
               + this.width / HighwayBuilder$StraightBlockPosProvider$2$ConstantPool.const_2ZYbcKm9jNsEB2b
               - this.textWidth / PopChams$GhostPlayer$ConstantPool.const_36gSiJ4o6YS0rYE,
            this.y + pad,
            theme.textColor.get(),
            false
         );
      } else {
         double ts = theme.textHeight();
         renderer.quad(
            this.x + this.width / FontFaceSetting$ConstantPool.const_WAPPAYjdLwslWdt - ts / WaypointsModule$ConstantPool.const_qjMjImVhGK6lF1M,
            this.y + pad,
            ts,
            ts,
            this.texture,
            theme.textColor.get()
         );
      }
   }
}
