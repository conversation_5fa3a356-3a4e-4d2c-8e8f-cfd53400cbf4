package meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable;

import javassist.DirClassPath$ConstantPool;
import javassist.bytecode.CodeAttribute$RuntimeCopyException$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorWidget;
import meteordevelopment.meteorclient.gui.widgets.pressable.WMinus;
import meteordevelopment.meteorclient.systems.modules.movement.AutoJump$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.PacketMine$ConstantPool;

public class WMeteorMinus extends WMinus implements MeteorWidget {
   @Override
   protected void onRender(GuiRenderer renderer, double mouseX, double mouseY, double delta) {
      double pad = this.pad();
      double s = this.theme.scale(AutoJump$Mode$ConstantPool.const_D7IFizJkSCbYbop);
      this.renderBackground(renderer, this, this.pressed, this.mouseOver);
      renderer.quad(
         this.x + pad,
         this.y + this.height / PacketMine$ConstantPool.const_gOWugV6YLGFLdWL - s / DirClassPath$ConstantPool.const_g7Y2Q6YvPmO2tnj,
         this.width - pad * CodeAttribute$RuntimeCopyException$ConstantPool.const_VbA2vFNUlgct9IW,
         s,
         this.theme().minusColor.get()
      );
   }
}
