package meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable;

public final class WMeteorFavorite$ConstantPool {
   public static String const_UOsdaOAedLwfhZk = 05yZjWIlId(qsnDqeCvaq("զիժաջիհծպջէհթծզգպսժ"));
   public static String const_UTBq3LF14Be21dC = 6Oq7UOATWF(qsnDqeCvaq(")ru`utr;!"));
   public static String const_doMbh1tX9aZat7I = MJLLC6WQ9a(qsnDqeCvaq("ĽĴĩĕĺĶľ"));
   public static int const_KEvV1Xd9LjIVdl8 = (int)((int)(-1929772718 ^ 264306559) ^ ((int)1482898102L ^ -614874702));
   public static int const_K5xq9rCQmm6rYXE = (int)(-2480406149574562240L ^ -2480406148431599655L) ^ (int)(1027423963 ^ 1725888410);
   public static String const_py52oD2WQRQV7gl = Egbkc4ttxq(qsnDqeCvaq("׀םו\u05c9\u05caח׀ף\u05caחק\u05c9\u05ca׆\u05ceז"));
   public static String const_46hWPH32IdvhpD4 = I04TGAn2jc(qsnDqeCvaq("ͺͥͦ;͙ͨͽͤͬͦ͡͝͠ͼͽ͚̈́"));
   public static String const_1emNphmJSlmQJ9e = agJ4MkjwNW(qsnDqeCvaq("؇ةؠءؼثٮطءػؼٮءعؠٮؾؼءؤثحغاآثؽ٠"));
   public static int const_KX2Vzqzi3YoAaCl = (int)((int)(-593882426 ^ 996188208) ^ (int)(-1359343564 ^ 1224778899));
   public static String const_qd5n1Bc95ovj2VB = IyQsRKvGuO(qsnDqeCvaq("ϭςχϟώϙ"));
   public static String const_RZVOWG9sWqV69Oe = XSrDabyeeQ(qsnDqeCvaq("虈唊詑詡"));
   public static String const_oIl2VOg4xlbNA9S = 9l2uLnadnL(qsnDqeCvaq("ʩʨ˪ʮʩʴʳʦ˪ʥʵʢʦʬ"));
}
