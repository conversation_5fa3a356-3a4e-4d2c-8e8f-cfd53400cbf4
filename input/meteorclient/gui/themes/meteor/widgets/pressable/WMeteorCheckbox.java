package meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable;

import javassist.SerialVersionUID$1$ConstantPool;
import meteordevelopment.meteorclient.events.game.SendMessageEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorWidget;
import meteordevelopment.meteorclient.gui.widgets.pressable.WCheckbox;
import meteordevelopment.meteorclient.systems.modules.ggboy.DoubleAccountTP$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.BossStack$ConstantPool;
import meteordevelopment.meteorclient.utils.tooltip.MapTooltipComponent$ConstantPool;
import net.minecraft.util.math.MathHelper;

public class WMeteorCheckbox extends WCheckbox implements MeteorWidget {
   private double animProgress;

   public WMeteorCheckbox(boolean checked) {
      super(checked);
      this.animProgress = checked ? 1.0 : 0.0;
   }

   @Override
   protected void onRender(GuiRenderer renderer, double mouseX, double mouseY, double delta) {
      MeteorGuiTheme theme = this.theme();
      this.animProgress = this.animProgress + (this.checked ? 1 : -1) * delta * MapTooltipComponent$ConstantPool.const_NWeIAyc5AWeIv0W;
      this.animProgress = MathHelper.method_15350(this.animProgress, 0.0, 1.0);
      this.renderBackground(renderer, this, this.pressed, this.mouseOver);
      if (this.animProgress > 0.0) {
         double cs = (this.width - theme.scale(BossStack$ConstantPool.const_fITfNbI0DVz1vmB))
            / SendMessageEvent$ConstantPool.const_pk2zNkQQHL1qDEd
            * this.animProgress;
         renderer.quad(
            this.x + (this.width - cs) / SerialVersionUID$1$ConstantPool.const_bwWQnu0DhiPzvOT,
            this.y + (this.height - cs) / DoubleAccountTP$ConstantPool.const_CCl429B1oYf13OM,
            cs,
            cs,
            theme.checkboxColor.get()
         );
      }
   }
}
