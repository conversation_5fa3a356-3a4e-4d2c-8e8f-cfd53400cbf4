package meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable;

import io.netty.handler.codec.socks.SocksCmdStatus$ConstantPool;
import javassist.bytecode.BadBytecode$ConstantPool;
import javassist.bytecode.MethodrefInfo$ConstantPool;
import javassist.bytecode.ParameterAnnotationsAttribute$ConstantPool;
import javassist.tools.reflect.Loader$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorWidget;
import meteordevelopment.meteorclient.gui.widgets.pressable.WPlus;
import meteordevelopment.meteorclient.systems.modules.ggboy.AutoSign$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Section$ConstantPool;

public class WMeteorPlus extends WPlus implements MeteorWidget {
   @Override
   protected void onRender(GuiRenderer renderer, double mouseX, double mouseY, double delta) {
      MeteorGuiTheme theme = this.theme();
      double pad = this.pad();
      double s = theme.scale(Expr$Section$ConstantPool.const_NxtmBjj3jT6XfG7);
      this.renderBackground(renderer, this, this.pressed, this.mouseOver);
      renderer.quad(
         this.x + pad,
         this.y + this.height / MethodrefInfo$ConstantPool.const_L8bhdHoq0gvLdw1 - s / BadBytecode$ConstantPool.const_JFegQeZfKqESSgb,
         this.width - pad * AutoSign$ConstantPool.const_eHnJ486Wpq7tHr7,
         s,
         theme.plusColor.get()
      );
      renderer.quad(
         this.x + this.width / ParameterAnnotationsAttribute$ConstantPool.const_DyvjqugJMlYg6GE - s / Loader$ConstantPool.const_BikqQ9bKorSrWIJ,
         this.y + pad,
         s,
         this.height - pad * SocksCmdStatus$ConstantPool.const_lOd56XMgIK8JNcH,
         theme.plusColor.get()
      );
   }
}
