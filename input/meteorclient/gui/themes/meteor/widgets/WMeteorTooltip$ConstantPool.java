package meteordevelopment.meteorclient.gui.themes.meteor.widgets;

public final class WMeteorTooltip$ConstantPool {
   public static String const_HxozUYBNz09IlQF = 0avqZeQq4w(we2pO5moFS("àáúëìáú"));
   public static String const_FQl7SpViTnJCUtY = i5N7uSq5Sf(
      we2pO5moFS("ף\u05cfו\u05ccׄ\u05ceևהր\u05cfׂהׁ\u05c9\u05ceրׁ\u05ceיրדׅגזׅגր\u05c9\u05ce׆\u05cfג\u05cdׁה\u05c9\u05cf\u05ce֎")
   );
   public static int const_Bo7ddDwr46FXQ93 = (int)((int)209166932L ^ -1255876928 ^ (int)(-585646643 ^ 1682229793));
   public static String const_i7YRS1Bj9Xs0NoA = bL1ry9fIqG(we2pO5moFS("ŅŚşŒœńěŚœŐł"));
   public static double const_tMJGiBM1hR6bbBW = Double.longBitsToDouble(
      8044227474395392064L ^ -1249858402689993341L ^ -2106331649350041841L ^ 2576486308095828684L
   );
   public static String const_WNrQWMWlnsI9jqS = R0aBLdY0Jp(we2pO5moFS("ѸяфюяјљЊўтяЊшцхщсЊѝтяјяЊуўЊуљЊњцыщуфэЊыЊшяюЄ"));
   public static String const_X9qPzrSdkeTS4tl = 1WLwZhQwWS(we2pO5moFS("şţŮīŨŤŧŤŹīŤŭīſţŮīŸŢůŮŸīŤŭīſţŮīũŧŤŨŠŸīſţŪſīŨŪťīũŮīũŹŤŠŮťĥ"));
   public static String const_HOkLXPFyhUHTALV = agWlg2iqtV(we2pO5moFS("߄߫߮ߧޢ߬߭߶ޢߤ߭߷߬ߦ"));
   public static String const_TLgk9z262U8Oiee = yTbnFEOJbG(we2pO5moFS("ȜȠȭɨȼȡȥȭȺɨȾȩȤȽȭɨȮȧȺɨȟȭȪȅȧȬȭɨȜȡȥȭȺɦ"));
   public static String const_42ddf7ngrlA2lG5 = Oq203N8UFF(we2pO5moFS("ҤҘҕӐ҄ҟҟҜ҃Ӑ҉ҟ҅Ӑ҇ґҞ҄Ӑ҄ҟӐ҅҃ҕӞ"));
   public static String const_qokOMV1nKI8bGRr = Qof1TWoCoN(we2pO5moFS("ˎ˯˴˅˱˵ˡˬ˳"));
}
