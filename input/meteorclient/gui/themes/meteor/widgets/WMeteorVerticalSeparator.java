package meteordevelopment.meteorclient.gui.themes.meteor.widgets;

import io.netty.handler.codec.socksx.v5.Socks5InitialResponseDecoder$ConstantPool;
import javassist.bytecode.annotation.FloatMemberValue$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorWidget;
import meteordevelopment.meteorclient.gui.widgets.WVerticalSeparator;
import meteordevelopment.meteorclient.renderer.GL$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.NoRotate$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.Color;

public class WMeteorVerticalSeparator extends WVerticalSeparator implements MeteorWidget {
   @Override
   protected void onRender(<PERSON><PERSON><PERSON>ender<PERSON> renderer, double mouseX, double mouseY, double delta) {
      MeteorGuiTheme theme = this.theme();
      Color colorEdges = theme.separatorEdges.get();
      Color colorCenter = theme.separatorCenter.get();
      double s = theme.scale(1.0);
      double offsetX = Math.round(this.width / GL$ConstantPool.const_vIeqL7wNPpE9Tft);
      renderer.quad(this.x + offsetX, this.y, s, this.height / NoRotate$ConstantPool.const_aTHdgJUIpXbuW76, colorEdges, colorEdges, colorCenter, colorCenter);
      renderer.quad(
         this.x + offsetX,
         this.y + this.height / Socks5InitialResponseDecoder$ConstantPool.const_lgeKLhLx9LDtPIl,
         s,
         this.height / FloatMemberValue$ConstantPool.const_lXqRJWAo1QWssCB,
         colorCenter,
         colorCenter,
         colorEdges,
         colorEdges
      );
   }
}
