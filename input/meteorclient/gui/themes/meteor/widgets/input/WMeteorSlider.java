package meteordevelopment.meteorclient.gui.themes.meteor.widgets.input;

import meteordevelopment.meteorclient.events.game.GameJoinedEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorWidget;
import meteordevelopment.meteorclient.gui.widgets.input.WSlider;
import meteordevelopment.meteorclient.pathing.BaritonePathManager$GoalDirection$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Breadcrumbs$Section$ConstantPool;

public class WMeteorSlider extends WSlider implements MeteorWidget {
   public WMeteorSlider(double value, double min, double max) {
      super(value, min, max);
   }

   @Override
   protected void onRender(<PERSON><PERSON><PERSON><PERSON><PERSON> renderer, double mouseX, double mouseY, double delta) {
      double valueWidth = this.valueWidth();
      this.renderBar(renderer, valueWidth);
      this.renderHandle(renderer, valueWidth);
   }

   private void renderBar(GuiRenderer renderer, double valueWidth) {
      MeteorGuiTheme theme = this.theme();
      double s = theme.scale(BaritonePathManager$GoalDirection$ConstantPool.const_dJl4AIoNByyqGgz);
      double handleSize = this.handleSize();
      double x = this.x + handleSize / Breadcrumbs$Section$ConstantPool.const_Nlq9bY7gvdkdnJe;
      double y = this.y + this.height / ShapeMode$ConstantPool.const_e6eyW970kE4BaVn - s / GameJoinedEvent$ConstantPool.const_w57m4zaJLyYOS6K;
      renderer.quad(x, y, valueWidth, s, theme.sliderLeft.get());
      renderer.quad(x + valueWidth, y, this.width - valueWidth - handleSize, s, theme.sliderRight.get());
   }

   private void renderHandle(GuiRenderer renderer, double valueWidth) {
      MeteorGuiTheme theme = this.theme();
      double s = this.handleSize();
      renderer.quad(this.x + valueWidth, this.y, s, s, GuiRenderer.CIRCLE, theme.sliderHandle.get(this.dragging, this.handleMouseOver));
   }
}
