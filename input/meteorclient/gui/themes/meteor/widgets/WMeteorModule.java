package meteordevelopment.meteorclient.gui.themes.meteor.widgets;

import javassist.bytecode.stackmap.TypeTag$ConstantPool;
import javassist.scopedpool.ScopedClassPoolRepositoryImpl$ConstantPool;
import javassist.tools.rmi.RemoteException$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.events.world.BlockUpdateEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.renderer.GuiRenderer;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorWidget;
import meteordevelopment.meteorclient.gui.utils.AlignmentX;
import meteordevelopment.meteorclient.gui.widgets.pressable.WPressable;
import meteordevelopment.meteorclient.systems.config.Config;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.profiles.Profiles$ConstantPool;
import meteordevelopment.meteorclient.utils.player.TitleScreenCredits$Response$ConstantPool;
import meteordevelopment.orbit.EventBus$LambdaFactoryInfo$ConstantPool;
import net.minecraft.util.math.MathHelper;

public class WMeteorModule extends WPressable implements MeteorWidget {
   private final Module module;
   private double titleWidth;
   private double animationProgress1;
   private double animationProgress2;

   public WMeteorModule(Module module) {
      this.module = module;
      this.tooltip = module.description;
      if (module.isActive()) {
         this.animationProgress1 = 1.0;
         this.animationProgress2 = 1.0;
      } else {
         this.animationProgress1 = 0.0;
         this.animationProgress2 = 0.0;
      }
   }

   @Override
   public double pad() {
      return this.theme.scale(BlockUpdateEvent$ConstantPool.const_tFdO76KkDtTirFq);
   }

   @Override
   protected void onCalculateSize() {
      double pad = this.pad();
      if (this.titleWidth == 0.0) {
         this.titleWidth = this.theme.textWidth(Config.get().chinese.get() ? this.module.chineseTitle : this.module.title);
      }

      this.width = pad + this.titleWidth + pad;
      this.height = pad + this.theme.textHeight() + pad;
   }

   @Override
   protected void onPressed(int button) {
      if (button == 0) {
         this.module.toggle();
      } else if (button == 1) {
         MeteorClient.mc.method_1507(this.theme.moduleScreen(this.module));
      }
   }

   @Override
   protected void onRender(GuiRenderer renderer, double mouseX, double mouseY, double delta) {
      MeteorGuiTheme theme = this.theme();
      double pad = this.pad();
      this.animationProgress1 = this.animationProgress1
         + delta * ScopedClassPoolRepositoryImpl$ConstantPool.const_6FiVaxmBfCePSTv * (!this.module.isActive() && !this.mouseOver ? -1 : 1);
      this.animationProgress1 = MathHelper.method_15350(this.animationProgress1, 0.0, 1.0);
      this.animationProgress2 = this.animationProgress2 + delta * Profiles$ConstantPool.const_rLeB1mwVIGToGUH * (this.module.isActive() ? 1 : -1);
      this.animationProgress2 = MathHelper.method_15350(this.animationProgress2, 0.0, 1.0);
      if (this.animationProgress1 > 0.0) {
         renderer.quad(this.x, this.y, this.width * this.animationProgress1, this.height, theme.moduleBackground.get());
      }

      if (this.animationProgress2 > 0.0) {
         renderer.quad(
            this.x,
            this.y + this.height * (1.0 - this.animationProgress2),
            theme.scale(TypeTag$ConstantPool.const_371ipl45srN7iVw),
            this.height * this.animationProgress2,
            theme.accentColor.get()
         );
      }

      double x = this.x + pad;
      double w = this.width - pad * EventBus$LambdaFactoryInfo$ConstantPool.const_QBx92G426vBklqh;
      if (theme.moduleAlignment.get() == AlignmentX.Center) {
         x += w / RemoteException$ConstantPool.const_uiLS2wvF3HG4yyY - this.titleWidth / TitleScreenCredits$Response$ConstantPool.const_Jo7Z6YOiqBH8EGY;
      } else if (theme.moduleAlignment.get() == AlignmentX.Right) {
         x += w - this.titleWidth;
      }

      renderer.text(Config.get().chinese.get() ? this.module.chineseTitle : this.module.title, x, this.y + pad, theme.textColor.get(), false);
   }
}
