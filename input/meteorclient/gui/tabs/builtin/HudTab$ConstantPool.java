package meteordevelopment.meteorclient.gui.tabs.builtin;

public final class HudTab$ConstantPool {
   public static String const_BeXpTZKmCVY069I = K96cPKbAJf(A5BQ12sYAt("ҁӌӃӎӜӜ"));
   public static int const_TDFjB7GGKgi5FlL = (int)(-3953525431350443941L ^ 3953525429948595957L) ^ (int)(957113864 ^ -1436476898);
   public static String const_ZmWsYIOa9DOhkWI = RaFloAJih9(A5BQ12sYAt("֯֎֕քփ֍֎ւ֊֒"));
   public static String const_viQnDvHCgVes2rL = TuGbs2wpsX(A5BQ12sYAt("ȖȢȣȸȺȶȣȾȴȶȻȻȮɷȲȶȣȤɷȱȸȸȳɹ"));
   public static double const_TAJWPSYW9lwDQyJ = Double.longBitsToDouble(
      -5721420848969782052L ^ -5486361706643887535L ^ 479733799344117846L ^ -5035672765350502693L
   );
   public static String const_flAKXGTWnTz1Bt6 = bY9xgSWCU3(A5BQ12sYAt("НкТѵСнмЦѵикбРйаѵзандУаЦѻ"));
}
