package meteordevelopment.meteorclient.commands.arguments;

import com.mojang.brigadier.StringReader;
import com.mojang.brigadier.arguments.ArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.exceptions.DynamicCommandExceptionType;
import com.mojang.brigadier.suggestion.Suggestions;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import io.netty.handler.codec.socksx.v5.Socks5CommandResponseDecoder$1$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5InitialResponseDecoder$1$ConstantPool;
import java.util.Collection;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import meteordevelopment.meteorclient.systems.macros.Macro;
import meteordevelopment.meteorclient.systems.macros.Macros;
import net.minecraft.command.CommandSource;
import net.minecraft.text.Text;

public class MacroArgumentType implements ArgumentType<Macro> {
   private static final MacroArgumentType INSTANCE = new MacroArgumentType();
   private static final DynamicCommandExceptionType NO_SUCH_MACRO = new DynamicCommandExceptionType(
      name -> Text.method_43470("Macro with name " + name + " doesn't exist.")
   );

   public static MacroArgumentType create() {
      return INSTANCE;
   }

   public static Macro get(CommandContext<?> context) {
      return (Macro)context.getArgument(0A4s9huOvM(xOnj6X6Ll1(m1eRDCtKvl(Socks5CommandResponseDecoder$1$ConstantPool.const_zgMa4DTjldNgzbN))), Macro.class);
   }

   private MacroArgumentType() {
   }

   public Macro parse(StringReader reader) throws CommandSyntaxException {
      String argument = reader.readString();
      Macro macro = Macros.get().get(argument);
      if (macro == null) {
         throw NO_SUCH_MACRO.create(argument);
      } else {
         return macro;
      }
   }

   public CompletableFuture<Suggestions> listSuggestions(CommandContext context, SuggestionsBuilder builder) {
      return CommandSource.method_9264(Macros.get().getAll().stream().map(macro -> macro.name.get()), builder);
   }

   public Collection<String> getExamples() {
      return Macros.get()
         .getAll()
         .stream()
         .limit(Socks5InitialResponseDecoder$1$ConstantPool.const_MduYQednpFDujrg)
         .map(macro -> macro.name.get())
         .collect(Collectors.toList());
   }
}
