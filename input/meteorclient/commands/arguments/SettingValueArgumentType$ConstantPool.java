package meteordevelopment.meteorclient.commands.arguments;

public final class SettingValueArgumentType$ConstantPool {
   public static String const_yiDGEPL2ZryDeqi = Cn8y4Dadp5(BcqHQ2MHkB("\u05fb\u05f7\u05f5ר\u05ed\u05ec\u05fdת\u05f6\u05f9\u05f5\u05fd"));
   public static String const_gF6H7jYa4S6gdUM = 4tPjQ66mlu(BcqHQ2MHkB("̛̫̮̻̀̍̃̊̌̌̀́̿̚̚"));
   public static double const_SgT6zLmDQjs4cGR = Double.longBitsToDouble(
      -4587456649761561841L ^ 6814052791577429053L ^ 7433254587362780218L ^ -4175314897887624440L
   );
   public static String const_uQ5nIhyb146bxrZ = eQvfZ72J2n(BcqHQ2MHkB("ؑ\u0602؋؈\u0604؎ؓ؞"));
   public static float const_6Y8mIyXU8Be0Ib8 = Float.intBitsToFloat((int)905522818L ^ 1745294852 ^ (int)(-2139461254 ^ -1615834628));
   public static String const_wLdmotaJtJQkjdb = PrDW65ly12(BcqHQ2MHkB("䯊铫鄂熦鎈愊宛倧"));
}
