package meteordevelopment.meteorclient.utils.world;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javassist.CtNewClass$ConstantPool;
import javassist.bytecode.stackmap.TypeData$TypeVar$ConstantPool;
import javassist.compiler.ast.Stmnt$ConstantPool;
import javassist.tools.reflect.Metalevel$ConstantPool;
import javassist.util.proxy.DefineClassHelper$Helper$ConstantPool;
import javax.annotation.OverridingMethodsMustInvokeSuper$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.commands.arguments.SettingArgumentType$ConstantPool;
import meteordevelopment.meteorclient.events.render.HeldItemRendererEvent$ConstantPool;
import meteordevelopment.meteorclient.gui.tabs.builtin.GuiTab$GuiScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorPlus$ConstantPool;
import meteordevelopment.meteorclient.gui.utils.WindowConfig$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.Accounts$1$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$GameOwnershipResponse$Item$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.ggboy.OffFireWork$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.RangeCheck$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.BetterChat$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$AutoPilotMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Ambience$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.StashFinder$ChunkScreen$ConstantPool;
import meteordevelopment.meteorclient.utils.Base;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.Rotation;
import meteordevelopment.orbit.listeners.LambdaListener$ConstantPool;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.block.HangingSignBlock;
import net.minecraft.block.SignBlock;
import net.minecraft.block.WallSignBlock;
import net.minecraft.entity.Entity;
import net.minecraft.entity.ItemEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.network.packet.c2s.play.HandSwingC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket.Mode;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;

public class BlockUtilGrim implements Base {
   public static final List<Block> SneakBlocks = Arrays.asList(
      Blocks.field_10443,
      Blocks.field_10034,
      Blocks.field_10380,
      Blocks.field_9980,
      Blocks.field_10486,
      Blocks.field_40285,
      Blocks.field_10246,
      Blocks.field_42740,
      Blocks.field_10535,
      Blocks.field_10333,
      Blocks.field_10312,
      Blocks.field_10228,
      Blocks.field_10200,
      Blocks.field_10608,
      Blocks.field_10485,
      Blocks.field_10199,
      Blocks.field_10407,
      Blocks.field_10063,
      Blocks.field_10203,
      Blocks.field_10600,
      Blocks.field_10275,
      Blocks.field_10051,
      Blocks.field_10140,
      Blocks.field_10532,
      Blocks.field_10268,
      Blocks.field_10605,
      Blocks.field_10373,
      Blocks.field_10055,
      Blocks.field_10068,
      Blocks.field_10371,
      Blocks.field_16492
   );
   public static final List<Class> SneakBlockClass = Arrays.asList(SignBlock.class, HangingSignBlock.class, WallSignBlock.class);

   public static boolean canPlace(BlockPos pos, boolean strictDirection) {
      return getInteractDirection(pos, strictDirection) != null;
   }

   public static boolean isSneakBlockClass(Block block) {
      if (block == null) {
         return false;
      } else {
         for (Class clazz : SneakBlockClass) {
            if (clazz.isInstance(block)) {
               return true;
            }
         }

         return false;
      }
   }

   public static boolean canPlaceIf(BlockPos pos, boolean strictDirection, Direction direction) {
      return getInteractDirectionIf(pos, strictDirection, direction) != null;
   }

   public static boolean placeBlock(BlockPos pos) {
      return placeBlock(pos, true, true, true);
   }

   public static boolean placeBlock(BlockPos pos, boolean strictDirection, boolean clientSwing, boolean rotate) {
      Direction direction = getInteractDirection(pos, strictDirection);
      if (direction == null) {
         return false;
      } else {
         BlockPos neighbor = pos.method_10093(direction.method_10153());
         return placeBlock(neighbor, direction, clientSwing, rotate);
      }
   }

   public static boolean placeUpBlock(BlockPos pos, boolean strictDirection, boolean clientSwing, boolean rotate) {
      Direction direction = getInteractDirectionSlabBlock(pos, strictDirection);
      if (direction == null) {
         return false;
      } else {
         BlockPos neighbor = pos.method_10093(direction.method_10153());
         return placeUpBlock(neighbor, direction, clientSwing, rotate);
      }
   }

   public static boolean placeDownBlock(BlockPos pos, boolean strictDirection, boolean clientSwing, boolean rotate) {
      Direction direction = getInteractDirectionSlabBlock(pos, strictDirection);
      if (direction == null) {
         return false;
      } else if (!BlockUtils.canSee_alien(pos, direction)) {
         return false;
      } else {
         BlockPos neighbor = pos.method_10093(direction.method_10153());
         return placeDownBlock(neighbor, direction, clientSwing, rotate);
      }
   }

   public static boolean placeBlockByFaceDirection(BlockPos pos, boolean strictDirection, boolean clientSwing, boolean rotate, Direction faceDirection) {
      Direction direction = getInteractDirection(pos, strictDirection);
      if (direction == null) {
         return false;
      } else {
         BlockPos neighbor = pos.method_10093(direction.method_10153());
         return placeBlockByFaceDirection(pos, neighbor, direction, clientSwing, rotate, faceDirection);
      }
   }

   public static boolean placeBlockByFaceDirection(
      BlockPos initPos, BlockPos pos, Direction direction, boolean clientSwing, boolean rotate, Direction faceDirection
   ) {
      Vec3d hitVec = pos.method_46558().method_1019(new Vec3d(direction.method_23955()).method_1021(GuiTab$GuiScreen$ConstantPool.const_9ns4yAlKrc6ld9e));
      if (rotate) {
         Rotation rotation = new Rotation(hitVec).setPriority(10);
         MeteorClient.ROTATIONGRIM.register(rotation);
         rotation.setYaw(getDirectionYaw(faceDirection));
         rotation.setPitch(MicrosoftLogin$GameOwnershipResponse$Item$ConstantPool.const_VR0w4fSXzuE7FB1);
         boolean rot = MeteorClient.ROTATIONGRIM.register(rotation);
         if (!rot) {
            return false;
         }
      }

      boolean placed = placeBlock(new BlockHitResult(hitVec, direction, pos, false), clientSwing);
      MeteorClient.ROTATIONGRIM.sync();
      return placed;
   }

   public static float getDirectionYaw(Direction direction) {
      if (direction == null) {
         return 0.0F;
      } else {
         switch (direction) {
            case field_11043:
               return CtNewClass$ConstantPool.const_cyP1ut2d3kobr5A;
            case field_11035:
               return 0.0F;
            case field_11039:
               return OffFireWork$ConstantPool.const_odXVnbAldIlBZyD;
            case field_11034:
               return Stmnt$ConstantPool.const_EoGBUm9ZtTZdRM0;
            default:
               return 0.0F;
         }
      }
   }

   public static boolean placeBlock(BlockPos pos, Direction direction, boolean clientSwing, boolean rotate) {
      Vec3d hitVec = pos.method_46558().method_1019(new Vec3d(direction.method_23955()).method_1021(HeldItemRendererEvent$ConstantPool.const_aT3wGe2eyCDSynL));
      if (rotate) {
         boolean rot = MeteorClient.ROTATIONGRIM.register(new Rotation(hitVec).setPriority(10));
         if (!rot) {
            return false;
         }
      }

      boolean placed = placeBlock(new BlockHitResult(hitVec, direction, pos, false), clientSwing);
      MeteorClient.ROTATIONGRIM.sync();
      return placed;
   }

   public static boolean placeUpBlock(BlockPos pos, Direction direction, boolean clientSwing, boolean rotate) {
      Vec3d hitVec = pos.method_46558().method_1031(0.0, SettingArgumentType$ConstantPool.const_9zESTdm2W0rZQMH, 0.0);
      if (rotate) {
         boolean rot = MeteorClient.ROTATIONGRIM.register(new Rotation(hitVec).setPriority(10));
         if (!rot) {
            return false;
         }
      }

      boolean placed = placeBlock(new BlockHitResult(hitVec, direction, pos, false), clientSwing);
      MeteorClient.ROTATIONGRIM.sync();
      return placed;
   }

   public static boolean placeDownBlock(BlockPos pos, Direction direction, boolean clientSwing, boolean rotate) {
      Vec3d hitVec = pos.method_46558().method_1031(0.0, Metalevel$ConstantPool.const_9NlVNacaaQNx66I, 0.0);
      if (rotate) {
         boolean rot = MeteorClient.ROTATIONGRIM.register(new Rotation(hitVec).setPriority(10));
         if (!rot) {
            return false;
         }
      }

      boolean placed = placeBlock(new BlockHitResult(hitVec, direction, pos, false), clientSwing);
      MeteorClient.ROTATIONGRIM.sync();
      return placed;
   }

   public static boolean placeBlock(BlockHitResult hitResult, boolean clientSwing) {
      return placeBlockImmediately(hitResult, clientSwing);
   }

   public static boolean placeBlockImmediately(BlockHitResult result, boolean clientSwing) {
      BlockState state = mc.field_1687.method_8320(result.method_17777());
      boolean shouldSneak = (SneakBlocks.contains(state.method_26204()) || isSneakBlockClass(mc.field_1687.method_8320(result.method_17777()).method_26204()))
         && !mc.field_1724.method_5715();
      if (shouldSneak) {
         mc.field_1724.field_3944.method_52787(new ClientCommandC2SPacket(mc.field_1724, Mode.field_12979));
      }

      ActionResult actionResult = placeBlockInternally(result);
      if (actionResult.method_23665() && actionResult.method_23666()) {
         if (clientSwing) {
            mc.field_1724.method_6104(Hand.field_5808);
         } else {
            mc.method_1562().method_52787(new HandSwingC2SPacket(Hand.field_5808));
         }
      }

      if (shouldSneak) {
         mc.field_1724.field_3944.method_52787(new ClientCommandC2SPacket(mc.field_1724, Mode.field_12984));
      }

      return actionResult.method_23665();
   }

   private static ActionResult placeBlockInternally(BlockHitResult hitResult) {
      return mc.field_1761.method_2896(mc.field_1724, Hand.field_5808, hitResult);
   }

   public static Direction getInteractDirection(BlockPos blockPos, boolean strictDirection) {
      Set<Direction> ncpDirections = getPlaceDirectionsNCP(mc.field_1724.method_33571(), blockPos.method_46558());
      Direction interactDirection = null;

      for (Direction direction : Direction.values()) {
         BlockState state = mc.field_1687.method_8320(blockPos.method_10093(direction));
         if (!state.method_26215() && state.method_26227().method_15769() && (!strictDirection || ncpDirections.contains(direction.method_10153()))) {
            interactDirection = direction;
            break;
         }
      }

      return interactDirection == null ? null : interactDirection.method_10153();
   }

   public static Direction getInteractDirectionExitUpDown(BlockPos blockPos, boolean strictDirection) {
      Set<Direction> ncpDirections = getPlaceDirectionsNCP(mc.field_1724.method_33571(), blockPos.method_46558());
      Direction interactDirection = null;

      for (Direction direction : Direction.values()) {
         BlockState state = mc.field_1687.method_8320(blockPos.method_10093(direction));
         if (!state.method_26215()
            && state.method_26227().method_15769()
            && (!strictDirection || ncpDirections.contains(direction.method_10153()))
            && direction != Direction.field_11036
            && direction != Direction.field_11033) {
            interactDirection = direction;
            break;
         }
      }

      return interactDirection == null ? null : interactDirection.method_10153();
   }

   public static Direction getInteractDirectionIf(BlockPos blockPos, boolean strictDirection, Direction direction_) {
      Set<Direction> ncpDirections = getPlaceDirectionsNCP(mc.field_1724.method_33571(), blockPos.method_46558());
      Direction interactDirection = null;

      for (Direction direction : Direction.values()) {
         BlockState state = mc.field_1687.method_8320(blockPos.method_10093(direction));
         if ((!state.method_26215() && state.method_26227().method_15769() || direction == direction_)
            && (!strictDirection || ncpDirections.contains(direction.method_10153()))) {
            interactDirection = direction;
            break;
         }
      }

      return interactDirection == null ? null : interactDirection.method_10153();
   }

   public static Direction getInteractDirectionSlabBlock(BlockPos blockPos, boolean strictDirection) {
      Set<Direction> ncpDirections = getPlaceDirectionsNCP(mc.field_1724.method_33571(), blockPos.method_46558());
      Direction interactDirection = null;

      for (Direction direction : Direction.values()) {
         if (direction != Direction.field_11036 && direction != Direction.field_11033) {
            BlockState state = mc.field_1687.method_8320(blockPos.method_10093(direction));
            if (!state.method_26215() && state.method_26227().method_15769() && (!strictDirection || ncpDirections.contains(direction.method_10153()))) {
               interactDirection = direction;
               break;
            }
         }
      }

      return interactDirection == null ? null : interactDirection.method_10153();
   }

   public static Set<Direction> getPlaceDirectionsNCP(Vec3d eyePos, Vec3d blockPos) {
      return getPlaceDirectionsNCP(eyePos.field_1352, eyePos.field_1351, eyePos.field_1350, blockPos.field_1352, blockPos.field_1351, blockPos.field_1350);
   }

   public static Set<Direction> getPlaceDirectionsNCP(double x, double y, double z, double dx, double dy, double dz) {
      double xdiff = x - dx;
      double ydiff = y - dy;
      double zdiff = z - dz;
      Set<Direction> dirs = new HashSet<>(6);
      if (ydiff > StashFinder$ChunkScreen$ConstantPool.const_cxvCdIeEMrpY9No) {
         dirs.add(Direction.field_11036);
      } else if (ydiff < BetterChat$ConstantPool.const_YjbvvMDFWrqOxKs) {
         dirs.add(Direction.field_11033);
      } else {
         dirs.add(Direction.field_11036);
         dirs.add(Direction.field_11033);
      }

      if (xdiff > WMeteorPlus$ConstantPool.const_AmkJGNWj2MVGL4H) {
         dirs.add(Direction.field_11034);
      } else if (xdiff < WindowConfig$ConstantPool.const_gBqjx3PljQsWyjJ) {
         dirs.add(Direction.field_11039);
      } else {
         dirs.add(Direction.field_11034);
         dirs.add(Direction.field_11039);
      }

      if (zdiff > RangeCheck$ConstantPool.const_t0Ca1TyhobkWg5t) {
         dirs.add(Direction.field_11035);
      } else if (zdiff < TypeData$TypeVar$ConstantPool.const_wr29CELQ5jxeFIZ) {
         dirs.add(Direction.field_11043);
      } else {
         dirs.add(Direction.field_11035);
         dirs.add(Direction.field_11043);
      }

      return dirs;
   }

   public static void clickBlock(BlockPos pos, Direction side, boolean rotate, Hand hand, SwingSide swingSide) {
      Vec3d directionVec = new Vec3d(
         pos.method_10263()
            + OverridingMethodsMustInvokeSuper$ConstantPool.const_kHyleDNJG5B4uJ6
            + side.method_10163().method_10263() * ElytraFly$AutoPilotMode$ConstantPool.const_Slmo4OM8l7Ygd6t,
         pos.method_10264() + Accounts$1$ConstantPool.const_1blw9ty2mGSF7bQ + side.method_10163().method_10264() * Ambience$ConstantPool.const_kAoMdsM3a9a9KxQ,
         pos.method_10260()
            + DefineClassHelper$Helper$ConstantPool.const_KPtv9GBxn56Yc4I
            + side.method_10163().method_10260() * LambdaListener$ConstantPool.const_Pq3IHaA6Bbqcd2V
      );
      PlayerUtils.swingHand(hand, swingSide);
      BlockHitResult result = new BlockHitResult(directionVec, side, pos, false);
      if (rotate) {
         boolean rot = MeteorClient.ROTATIONGRIM.register(new Rotation(directionVec).setPriority(10));
         if (!rot) {
            return;
         }
      }

      Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
      MeteorClient.ROTATIONGRIM.sync();
   }

   public static boolean intersectsEntity(BlockPos pos) {
      if (pos == null) {
         return true;
      } else {
         for (Entity entity : mc.field_1687.method_18112()) {
            if (!(entity instanceof EndCrystalEntity)
               && (
                  entity.method_5829().method_994(new Box(pos)) && entity.method_24828()
                     || entity instanceof ItemEntity && entity.method_5829().method_994(new Box(pos.method_10084()))
               )) {
               return true;
            }
         }

         return false;
      }
   }

   public static boolean intersectsAnyEntity(BlockPos pos) {
      if (pos == null) {
         return true;
      } else {
         for (Entity entity : mc.field_1687.method_18112()) {
            if (entity.method_5829().method_994(new Box(pos))) {
               return true;
            }
         }

         return false;
      }
   }
}
