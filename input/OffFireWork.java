package meteordevelopment.meteorclient.systems.modules.ggboy;

import io.netty.handler.codec.socks.SocksInitResponse$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.BlockDataSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$Position$ConstantPool;
import meteordevelopment.meteorclient.systems.proxies.ProxyType$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.ByteCountDataOutput$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralStatusType$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.item.ArmorItem;
import net.minecraft.item.ElytraItem;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.CloseHandledScreenC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.text.Text;
import net.minecraft.util.Hand;

public class OffFireWork extends Module {
   private int delay;
   private int old_slot;
   private final SettingGroup sgGeneral;
   private final Setting<Integer> closeDelay;

   public OffFireWork() {
      super(
         Categories.Ggboy,
         cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(AstralStatusType$ConstantPool.const_qnJA1ZyGg7MK1dB))),
         new StringBuilder(cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(Nametags$Position$ConstantPool.const_aINIJ2Pj7q3LcET)))),
         cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(ProxyType$ConstantPool.const_98Oi1a8DXVhvIUN)))
      );
      super.canBindMouse = true;
      this.delay = 0;
      this.old_slot = mc.field_1724 == null ? 0 : mc.field_1724.method_31548().field_7545;
      this.sgGeneral = this.settings.getDefaultGroup();
      this.closeDelay = this.sgGeneral
         .add(
            new IntSetting.Builder()
               .name(cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(ByteCountDataOutput$ConstantPool.const_ZErknAb7VAiLazD))))
               .description(cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(SocksInitResponse$ConstantPool.const_1yl5Ky4QXS5yJ8V))))
               .defaultValue(4)
               .sliderRange(1, 40)
               .build()
         );
   }

   @Override
   public void onActivate() {
      if (!CheckUtils.check()) {
         for (int i = 0; i < 3; i++) {
            this.info(Text.method_30163(cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(BlockDataSetting$ConstantPool.const_LjLEeCq4zVBemwo)))));
         }

         if (this.isActive()) {
            this.toggle();
         }
      }

      this.off();
      this.delay = this.closeDelay.get();
      this.old_slot = mc.field_1724 == null ? 0 : mc.field_1724.method_31548().field_7545;
   }

   @Override
   public void init() {
      if (this.isActive() && !CheckUtils.check()) {
         this.toggle();
      }
   }

   @EventHandler
   private void onTick(TickEvent.Post event) {
      if (this.delay == 0) {
         InvUtils.swap(this.old_slot, false);
         this.toggle();
      } else {
         this.delay--;
      }
   }

   public void off() {
      if (!mc.field_1724.method_24828()) {
         if (mc.field_1724.method_31548().method_5438(38).method_7909() instanceof ElytraItem) {
            if (!(mc.field_1724.method_6047().method_7909() instanceof ArmorItem)) {
               if (mc.field_1724.method_6047().method_7909() == Items.field_8639) {
                  sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.field_5808, id, mc.field_1724.method_36454(), mc.field_1724.method_36455()));
               } else {
                  int firework;
                  if ((firework = InvUtils.findInHotbar(Items.field_8639).slot()) != -1) {
                     int old = mc.field_1724.method_31548().field_7545;
                     InvUtils.switchToSlot(firework);
                     sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.field_5808, id, mc.field_1724.method_36454(), mc.field_1724.method_36455()));
                     InvUtils.switchToSlot(old);
                     mc.method_1562().method_52787(new CloseHandledScreenC2SPacket(mc.field_1724.field_7512.field_7763));
                  } else if ((firework = InvUtils.findItemInventorySlotGrim(Items.field_8639)) != -1) {
                     InvUtils.inventorySwap(firework, mc.field_1724.method_31548().field_7545);
                     sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.field_5808, id, mc.field_1724.method_36454(), mc.field_1724.method_36455()));
                     InvUtils.inventorySwap(firework, mc.field_1724.method_31548().field_7545);
                     InvUtils.sync();
                  }
               }
            }
         }
      }
   }
}
