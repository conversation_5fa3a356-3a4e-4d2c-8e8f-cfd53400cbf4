package meteordevelopment.meteorclient.systems.modules.ggboy;

import io.netty.handler.codec.socks.SocksInitResponse$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.BlockDataSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$Position$ConstantPool;
import meteordevelopment.meteorclient.systems.proxies.ProxyType$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.ByteCountDataOutput$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralStatusType$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.item.ArmorItem;
import net.minecraft.item.ElytraItem;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.CloseHandledScreenC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.text.Text;
import net.minecraft.util.Hand;

public class OffFireWork extends Module {
   private int delay;
   private int old_slot;
   private final SettingGroup sgGeneral;
   private final Setting<Integer> closeDelay;

   public OffFireWork() {
      super(
         Categories.Ggboy,
         cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(AstralStatusType$ConstantPool.const_qnJA1ZyGg7MK1dB))),
         new StringBuilder(cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(Nametags$Position$ConstantPool.const_aINIJ2Pj7q3LcET)))),
         cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(ProxyType$ConstantPool.const_98Oi1a8DXVhvIUN)))
      );
      super.canBindMouse = true;
      this.delay = 0;
      this.old_slot = mc.player == null ? 0 : mc.player.getInventory().selectedSlot;
      this.sgGeneral = this.settings.getDefaultGroup();
      this.closeDelay = this.sgGeneral
         .add(
            new IntSetting.Builder()
               .name(cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(ByteCountDataOutput$ConstantPool.const_ZErknAb7VAiLazD))))
               .description(cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(SocksInitResponse$ConstantPool.const_1yl5Ky4QXS5yJ8V))))
               .defaultValue(4)
               .sliderRange(1, 40)
               .build()
         );
   }

   @Override
   public void onActivate() {
      if (!CheckUtils.check()) {
         for (int i = 0; i < 3; i++) {
            this.info(Text.of(cf5YrWFLwk(sUtbrvw9bb(9awJ2HJSMx(BlockDataSetting$ConstantPool.const_LjLEeCq4zVBemwo)))));
         }

         if (this.isActive()) {
            this.toggle();
         }
      }

      this.off();
      this.delay = this.closeDelay.get();
      this.old_slot = mc.player == null ? 0 : mc.player.getInventory().selectedSlot;
   }

   @Override
   public void init() {
      if (this.isActive() && !CheckUtils.check()) {
         this.toggle();
      }
   }

   @EventHandler
   private void onTick(TickEvent.Post event) {
      if (this.delay == 0) {
         InvUtils.swap(this.old_slot, false);
         this.toggle();
      } else {
         this.delay--;
      }
   }

   public void off() {
      if (!mc.player.isOnGround()) {
         if (mc.player.getInventory().getStack(38).getItem() instanceof ElytraItem) {
            if (!(mc.player.getMainHandStack().getItem() instanceof ArmorItem)) {
               if (mc.player.getMainHandStack().getItem() == Items.FIREWORK_ROCKET) {
                  sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id, mc.player.getYaw(), mc.player.getPitch()));
               } else {
                  int firework;
                  if ((firework = InvUtils.findInHotbar(Items.FIREWORK_ROCKET).slot()) != -1) {
                     int old = mc.player.getInventory().selectedSlot;
                     InvUtils.switchToSlot(firework);
                     sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id, mc.player.getYaw(), mc.player.getPitch()));
                     InvUtils.switchToSlot(old);
                     mc.getNetworkHandler().sendPacket(new CloseHandledScreenC2SPacket(mc.player.currentScreenHandler.syncId));
                  } else if ((firework = InvUtils.findItemInventorySlotGrim(Items.FIREWORK_ROCKET)) != -1) {
                     InvUtils.inventorySwap(firework, mc.player.getInventory().selectedSlot);
                     sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id, mc.player.getYaw(), mc.player.getPitch()));
                     InvUtils.inventorySwap(firework, mc.player.getInventory().selectedSlot);
                     InvUtils.sync();
                  }
               }
            }
         }
      }
   }
}
