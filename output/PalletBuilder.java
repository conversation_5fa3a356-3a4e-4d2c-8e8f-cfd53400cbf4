package meteordevelopment.meteorclient.systems.modules.ggboy;

import io.netty.handler.codec.socks.SocksAuthStatus$ConstantPool;
import io.netty.handler.codec.socks.SocksInitResponseDecoder$ConstantPool;
import io.netty.handler.proxy.HttpProxyHandler$HttpProxyConnectException$ConstantPool;
import java.util.ArrayList;
import java.util.List;
import javassist.JarClassPath$ConstantPool;
import javassist.bytecode.CodeIterator$AlignmentException$ConstantPool;
import javassist.bytecode.annotation.NoSuchClassError$ConstantPool;
import javassist.compiler.CodeGen$ConstantPool;
import javax.annotation.MatchesPattern$Checker$ConstantPool;
import javax.annotation.Signed$ConstantPool;
import javax.annotation.WillClose$ConstantPool;
import meteordevelopment.meteorclient.events.entity.EntityDestroyEvent$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PlayerTickMovementEvent$ConstantPool;
import meteordevelopment.meteorclient.events.meteor.ModuleBindChangedEvent$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.screens.ModulesScreen$WCategoryController$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.ProxiesImportScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.BlockListSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.WMeteorSection$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorButton$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.macros.Macros$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.ArrowDodge$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.EntitySpeed$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.ChestSwap$Chestplate$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.LightOverlay$Spawn$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.Nametags$DistanceColorMode$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.InventorySorter$InvPart$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
import meteordevelopment.meteorclient.utils.world.BlockUtilGrim;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.Timer;
import meteordevelopment.orbit.EventBus$LambdaFactoryInfo$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.SlabBlock;
import net.minecraft.block.enums.SlabType;
import net.minecraft.item.Items;
import net.minecraft.state.property.Properties;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import org.reflections.Reflections$ConstantPool;

public class PalletBuilder extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup renderGeneral = this.settings.createGroup(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(SocksAuthStatus$ConstantPool.const_EX0AuawTrgeRtTN))));
   private final Setting<Boolean> moveStop = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(WMeteorButton$ConstantPool.const_LhQz2avertVAvXo))))
            .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(Macros$ConstantPool.const_1RjaDEjDDr9Rxj3))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> onlyBottom = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(ProxiesImportScreen$ConstantPool.const_1Qnjdkm6A7DlyQV))))
            .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(CodeGen$ConstantPool.const_BjQBvJv5beqYPgL))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Double> range = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(NoSuchClassError$ConstantPool.const_O3vNUMLoDC2gvqO))))
            .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(WMeteorSection$ConstantPool.const_Dqcf2ejTvJvnIhY))))
            .defaultValue(Reflections$ConstantPool.const_aiL9ryNANenzaX1)
            .range(0.0, ChestSwap$Chestplate$ConstantPool.const_7mgmS8onBlms3FO)
            .build()
      );
   public final Setting<BlockType> blockType = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(ModulesScreen$WCategoryController$ConstantPool.const_Blwmh0ZyS0NiK7H)))))
                  .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(Reflections$ConstantPool.const_309kaDydqUw1yGv)))))
               .defaultValue(BlockType.STONE))
            .build()
      );
   private final Setting<Integer> blocksPer = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(ArrowDodge$ConstantPool.const_a2Qqxq7iRmXStyG))))
            .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(ModuleBindChangedEvent$ConstantPool.const_7qWQwSatW60FPbV))))
            .defaultValue(1)
            .sliderRange(1, 6)
            .build()
      );
   private final Setting<Integer> delay = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(PlayerTickMovementEvent$ConstantPool.const_j2k6AYGZwxL6DOj))))
            .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(Signed$ConstantPool.const_8AAHYsSAWht1dVg))))
            .defaultValue(0)
            .sliderRange(0, 10)
            .build()
      );
   private final Setting<Boolean> render = this.renderGeneral
      .add(
         new BoolSetting.Builder()
            .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(EntityDestroyEvent$ConstantPool.const_Y6jFjyzM4POdgqi))))
            .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(SocksInitResponseDecoder$ConstantPool.const_3rQnIVbmS9zJJFR))))
            .defaultValue(true)
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.renderGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(InventorySorter$InvPart$ConstantPool.const_LtdW4EVHWsXWBVK)))))
                  .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(JarClassPath$ConstantPool.const_gbenoB8h2yg7jP0)))))
               .defaultValue(ShapeMode.Both))
            .build()
      );
   private final Setting<SettingColor> readySideColor = this.renderGeneral
      .add(
         new ColorSetting.Builder()
            .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(CodeIterator$AlignmentException$ConstantPool.const_SCln2bN0xsc1RYI))))
            .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(LightOverlay$Spawn$ConstantPool.const_OXwOsBG6DVlUQYI))))
            .defaultValue(new SettingColor(255, 255, 255, 50))
            .build()
      );
   private final Setting<SettingColor> readyLineColor = this.renderGeneral
      .add(
         new ColorSetting.Builder()
            .name(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(WillClose$ConstantPool.const_DxoV6FJWJMb1970))))
            .description(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(EntitySpeed$ConstantPool.const_CUeuEAbDvqwfDiQ))))
            .defaultValue(new SettingColor(255, 255, 255, 128))
            .build()
      );
   private List<BlockPos> placeList = new ArrayList<>();
   private final Timer timer = new Timer();
   int progress = 0;

   public PalletBuilder() {
      super(
         Categories.Ggboy,
         vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(Nametags$DistanceColorMode$ConstantPool.const_gLApPNbJxrDWqKB))),
         new StringBuilder(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(EventBus$LambdaFactoryInfo$ConstantPool.const_DWBMrrylr9vgtnB)))),
         vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(BlockListSettingScreen$ConstantPool.const_fhwFesfYwSbyaqF)))
      );
   }

   @Override
   public void onActivate() {
      if (!CheckUtils.check()) {
         for (int i = 0; i < 3; i++) {
            this.info(Text.of(vn5zFWeTjo(1jgFi1WkJJ(7YvJ2W844e(MatchesPattern$Checker$ConstantPool.const_QYitGGqitqWsFVB)))));
         }

         if (this.isActive()) {
            this.toggle();
         }
      }
   }

   @Override
   public void init() {
      if (this.isActive()) {
         this.toggle();
      }
   }

   @EventHandler
   public void onTick(TickEvent.Pre event) {
      if (!this.moveStop.get()
         || !mc.options.backKey.isPressed()
            && !mc.options.forwardKey.isPressed()
            && !mc.options.leftKey.isPressed()
            && !mc.options.rightKey.isPressed()) {
         this.placeList.clear();
         this.progress = 0;
         if (this.timer.passedMs(this.delay.get().intValue() * HttpProxyHandler$HttpProxyConnectException$ConstantPool.const_p1AmbB4BDNElJ1j)) {
            this.timer.reset();

            for (BlockPos pos : BlockUtils.getSphere(Float.parseFloat(this.range.get().toString()))) {
               if (this.blockType.get() == BlockType.DOWNSLAP) {
                  Direction direction = BlockUtilGrim.getInteractDirection(pos, true);
                  if (BlockUtils.canSee_alien(pos, direction)
                     && mc.world.getBlockState(pos).getBlock().asItem() == Items.AIR
                     && this.neighbor(pos)
                     && this.onlyBottom.get()
                     && pos.getY() == mc.player.getBlockPos().getY()) {
                     this.tryPlace(pos);
                  }
               } else if (BlockUtils.canPlace_alien(pos, this.range.get(), true)
                  && this.neighbor(pos)
                  && this.onlyBottom.get()
                  && pos.getY() == mc.player.getBlockPos().down().getY()
                  && this.blockType.get() != BlockType.DOWNSLAP
                  && mc.world.getBlockState(pos).getBlock().asItem() == Items.AIR) {
                  this.tryPlace(pos);
               }
            }
         }
      }
   }

   @EventHandler
   private void onRender(Render3DEvent event) {
      if (this.render.get()
         && this.placeList.size() > 0
         && (
            InvUtils.find(this.getBlockBytype(this.blockType.get()).asItem()).found()
               || (this.blockType.get() == BlockType.DOWNSLAP || this.blockType.get() == BlockType.UPSLAP)
                  && InvUtils.findClassInventorySlotGrim(SlabBlock.class) != -1
         )) {
         for (int i = 0; i < this.placeList.size(); i++) {
            double x1 = this.placeList.get(i).getX();
            double y1 = this.placeList.get(i).getY();
            double z1 = this.placeList.get(i).getZ();
            double x2 = this.placeList.get(i).getX() + 1;
            double y2 = this.placeList.get(i).getY() + 1;
            double z2 = this.placeList.get(i).getZ() + 1;
            event.renderer.box(x1, y1, z1, x2, y2, z2, this.readySideColor.get(), this.readyLineColor.get(), this.shapeMode.get(), 0);
         }
      }
   }

   private void tryPlace(BlockPos pos) {
      if (pos != null) {
         if (!this.placeList.contains(pos)) {
            if (this.progress < this.blocksPer.get()) {
               if (!(MathHelper.sqrt((float)BlockUtils.getEyesPos().squaredDistanceTo(pos.toCenterPos())) > this.range.get())) {
                  int itemResult = -1;
                  if (this.blockType.get() != BlockType.DOWNSLAP && this.blockType.get() != BlockType.UPSLAP) {
                     itemResult = InvUtils.findItemInventorySlotGrim(this.getBlockBytype(this.blockType.get()).asItem());
                  } else {
                     itemResult = InvUtils.findClassInventorySlotGrim(SlabBlock.class);
                  }

                  if (itemResult != -1) {
                     this.placeList.add(pos);
                     InvUtils.doSwap(itemResult);
                     if (this.blockType.get() == BlockType.UPSLAP) {
                        BlockUtilGrim.placeUpBlock(pos, true, true, true);
                     } else if (this.blockType.get() == BlockType.DOWNSLAP) {
                        BlockUtilGrim.placeDownBlock(pos, true, true, true);
                     } else {
                        BlockUtilGrim.placeBlock(pos, true, true, true);
                     }

                     InvUtils.doSwap(itemResult);
                     InvUtils.sync();
                     this.timer.reset();
                     this.progress++;
                  }
               }
            }
         }
      }
   }

   private boolean neighbor(BlockPos pos) {
      for (Direction direction : Direction.values()) {
         if (direction != Direction.DOWN && direction != Direction.UP) {
            if (this.blockType.get() == BlockType.UPSLAP
               && mc.world.getBlockState(pos.offset(direction)).getBlock() instanceof SlabBlock block
               && mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.TOP) {
               return true;
            }

            if (this.blockType.get() == BlockType.DOWNSLAP
               && mc.world.getBlockState(pos.offset(direction)).getBlock() instanceof SlabBlock block
               && (
                  mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.BOTTOM
                     || mc.world.getBlockState(pos.offset(direction)).get(Properties.SLAB_TYPE) == SlabType.DOUBLE
               )) {
               return true;
            }

            if (mc.world.getBlockState(pos.offset(direction)).getBlock() == this.getBlockBytype(this.blockType.get())
               && this.blockType.get() != BlockType.DOWNSLAP
               && this.blockType.get() != BlockType.UPSLAP) {
               return true;
            }
         }
      }

      return false;
   }

   public Block getBlockBytype(BlockType blockType) {
      switch (blockType) {
         case STONE:
            return Blocks.STONE;
         case DIRT:
            return Blocks.DIRT;
         case COBBLESTONE:
            return Blocks.COBBLESTONE;
         case OBSIDIAN:
            return Blocks.OBSIDIAN;
         case STONE_BRICKS:
            return Blocks.STONE_BRICKS;
         case SMOOTHSTON:
            return Blocks.SMOOTH_STONE;
         default:
            return Blocks.STONE;
      }
   }
}
