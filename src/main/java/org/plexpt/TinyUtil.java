package org.plexpt;

import net.fabricmc.stitch.commands.tinyv2.TinyFile;
import net.fabricmc.stitch.commands.tinyv2.TinyV2Reader;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

public class TinyUtil {


    public static void main(String[] args) throws IOException {


        Path tinyFile = Paths.get("mappings.tiny");
        Path inputJava = Paths.get("BlockUtilGrim.java");
        Path outputJava = Paths.get("BlockUtilGrim_remapped.java");


        TinyFile file = TinyV2Reader.read(tinyFile);
        file.getClassEntries().stream()
                .limit(10)
                .forEach(classEntry -> {

                    classEntry.getClassNames().stream()
                            .limit(10)
                            .forEach(className -> {
                                System.out.println("className=" + className);

                            });
                    classEntry.getComments()
                            .stream()
                            .limit(10)
                            .forEach(s -> {
                                System.out.println("Comments=" + s);
                            });
                    classEntry.getFields()
                            .stream()
                            .limit(10)
                            .forEach(s -> {
                                System.out.println("getFields=" + s);
                            });
                    classEntry.getMethods()
                            .stream()
                            .limit(10)
                            .forEach(s -> {
                                System.out.println("getMethods=" + s);
                            });

                });


    }
}
