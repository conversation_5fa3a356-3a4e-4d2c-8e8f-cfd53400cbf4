package org.plexpt;

import com.google.gson.*;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.*;

public class ConstantReplacer {

    public static void replaceConstants(String jsonPath, String inputDir, String outputDir) throws IOException {
        // 1. 读取 JSON 中的映射表
        Map<String, String> constantMap = new HashMap<>();

        Gson gson = new GsonBuilder()
                .setLenient()
                .serializeSpecialFloatingPointValues()
                .create();

        JsonObject json = gson.fromJson(new FileReader(jsonPath), JsonObject.class);

        for (Map.Entry<String, JsonElement> entry : json.entrySet()) {
            constantMap.put(entry.getKey(), toJavaLiteral(entry.getValue()));
        }

        // 2. 遍历 inputDir 中的所有 .java 文件
        Files.walk(Paths.get(inputDir))
                .filter(path -> Files.isRegularFile(path) && path.toString().endsWith(".java"))
                .forEach(path -> {
                    try {
                        String content = new String(Files.readAllBytes(path));

                        // 3. 替换每个常量引用
                        for (Map.Entry<String, String> entry : constantMap.entrySet()) {
                            // 匹配完整限定名，如 Module$ConstantPool.const_xxx
                            String keyRegex = Pattern.quote(entry.getKey());
                            content = content.replaceAll(keyRegex, entry.getValue());
                        }

                        // 4. 输出到 output 目录中，保持目录结构
                        Path relative = Paths.get(inputDir).relativize(path);
                        Path outputPath = Paths.get(outputDir).resolve(relative);
                        Files.createDirectories(outputPath.getParent());
                        Files.write(outputPath, content.getBytes());
                        System.out.println("Replaced: " + path);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });

        System.out.println("All replacements done.");
    }

    private static String toJavaLiteral(JsonElement value) {
        if (value.isJsonNull()) return "null";
        if (value.isJsonPrimitive()) {
            JsonPrimitive p = value.getAsJsonPrimitive();
            if (p.isString()) {
                return "\"" + p.getAsString().replace("\\", "\\\\").replace("\"", "\\\"") + "\"";
            } else if (p.isBoolean()) {
                return p.getAsBoolean() ? "true" : "false";
            } else if (p.isNumber()) {
                String s = p.getAsString();
                if ("NaN".equals(s)) return "Double.NaN";
                if ("Infinity".equals(s)) return "Double.POSITIVE_INFINITY";
                if ("-Infinity".equals(s)) return "Double.NEGATIVE_INFINITY";
                return s;
            }
        }
        return value.toString(); // fallback
    }

    public static void main(String[] args) throws IOException {
        replaceConstants(
                "map.json", // 输入 JSON 文件
                "input",           // 输入 Java 源码目录
                "output"           // 输出修改后的目录
        );
    }
}
