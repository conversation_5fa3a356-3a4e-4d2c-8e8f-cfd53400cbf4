//package org.plexpt;
//import net.fabricmc.stitch.commands.tinyv2.TinyV2Reader;
//import net.fabricmc.stitch.commands.tinyv2.TinyWriter;
//
//import java.io.*;
//import java.nio.file.*;
//import java.util.*;
//import com.google.gson.*;
//
//public class TinyToJson {
//    public static void main(String[] args) throws Exception {
//        String tinyPath = "mappings.tiny";
//        String jsonOutputPath = "mappings.json";
//
//        // 读取 tiny 文件
//        Map<String, String> classMap = new HashMap<>();
//        Map<String, String> fieldMap = new HashMap<>();
//        Map<String, String> methodMap = new HashMap<>();
//        try (BufferedReader reader = Files.newBufferedReader(Paths.get(tinyPath))) {
//            TinyV2Reader tinyReader = new TinyV2Reader(reader);
//            tinyReader.accept((type, owner, name, desc, namespaceFrom, namespaceTo, mappedName) -> {
//                switch (type) {
//                    case CLASS:
//                        classMap.put(name, mappedName);
//                        break;
//                    case FIELD:
//                        fieldMap.put(name, mappedName);
//                        break;
//                    case METHOD:
//                        methodMap.put(name, mappedName);
//                        break;
//                }
//            });
//        }
//
//        // 构建 JSON
//        JsonObject root = new JsonObject();
//
//        root.add("classes", mapToJson(classMap));
//        root.add("fields", mapToJson(fieldMap));
//        root.add("methods", mapToJson(methodMap));
//
//        // 输出到 mappings.json
//        try (Writer writer = new FileWriter(jsonOutputPath)) {
//            Gson gson = new GsonBuilder().setPrettyPrinting().create();
//            gson.toJson(root, writer);
//        }
//
//        System.out.println("✅ Mappings exported to mappings.json");
//    }
//
//    private static JsonObject mapToJson(Map<String, String> map) {
//        JsonObject obj = new JsonObject();
//        for (Map.Entry<String, String> entry : map.entrySet()) {
//            obj.addProperty(entry.getKey(), entry.getValue());
//        }
//        return obj;
//    }
//}
