//package org.plexpt;
//
//import net.fabricmc.mapping.reader.v2.TinyV2Factory;
//import net.fabricmc.mappingio.MappingVisitor;
//import net.fabricmc.stitch.commands.tinyv2.TinyClass;
//import net.fabricmc.stitch.commands.tinyv2.TinyFile;
//import net.fabricmc.stitch.commands.tinyv2.TinyV2Reader;
//
//import net.fabricmc.mappingio.tree.MappingTree;
//import net.fabricmc.mappingio.tree.MemoryMappingTree;
//
//import java.io.*;
//import java.nio.file.*;
//import java.util.*;
//import java.util.regex.Pattern;
//
//public class TinyMappingsRemapper {
//    public static void main(String[] args) throws IOException {
//        Path tinyFile = Paths.get("mappings.tiny");
//        Path inputJava = Paths.get("BlockUtilGrim.java");
//        Path outputJava = Paths.get("BlockUtilGrim_remapped.java");
//
//        // Load mappings using TinyMappingsParser
//        MemoryMappingTree mappingTree = new MemoryMappingTree();
//        try (InputStream is = Files.newInputStream(tinyFile)) {
//            TinyFile file = TinyV2Reader.read(tinyFile);
//            for (TinyClass classEntry : file.getClassEntries()) {
//                classEntry.getClassNames()
//            }
//        }
//
//        String fromNamespace = "intermediary";
//        String toNamespace = "named";
//
//        int fromId = mappingTree.getNamespaceId(fromNamespace);
//        int toId = mappingTree.getNamespaceId(toNamespace);
//
//        Map<String, String> classMap = new HashMap<>();
//        Map<String, String> fieldMap = new HashMap<>();
//        Map<String, String> methodMap = new HashMap<>();
//
//        // Process classes
//        mappingTree.accept(new MappingVisitor() {
//            @Override
//            public boolean visitClass(String srcName) {
//                String dstName = mappingTree.mapClassName(srcName, fromId, toId);
//                if (dstName != null) {
//                    classMap.put(getSimple(srcName), getSimple(dstName));
//                }
//                return true;
//            }
//
//            @Override
//            public boolean visitField(String className, String fieldName, String desc) {
//                String dstName = mappingTree.mapFieldName(className, fieldName, desc, fromId, toId);
//                if (dstName != null) {
//                    fieldMap.put(fieldName, dstName);
//                }
//                return true;
//            }
//
//            @Override
//            public boolean visitMethod(String className, String methodName, String desc) {
//                String dstName = mappingTree.mapMethodName(className, methodName, desc, fromId, toId);
//                if (dstName != null) {
//                    methodMap.put(methodName, dstName);
//                }
//                return true;
//            }
//        });
//
//        System.out.println("Loaded mappings: " + classMap.size() + " classes, " + fieldMap.size() + " fields, " + methodMap.size() + " methods.");
//
//        // Read Java source
//        String code = Files.readString(inputJava);
//
//        // Replace classes
//        for (Map.Entry<String, String> entry : classMap.entrySet()) {
//            code = code.replaceAll("\\b" + Pattern.quote(entry.getKey()) + "\\b", entry.getValue());
//        }
//
//        // Replace fields
//        for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
//            code = code.replaceAll("\\b" + Pattern.quote(entry.getKey()) + "\\b", entry.getValue());
//        }
//
//        // Replace methods
//        for (Map.Entry<String, String> entry : methodMap.entrySet()) {
//            code = code.replaceAll("\\b" + Pattern.quote(entry.getKey()) + "\\b", entry.getValue());
//        }
//
//        // Write output
//        Files.writeString(outputJava, code);
//        System.out.println("✅ Remap complete: " + outputJava);
//    }
//
//    private static String getSimple(String internalName) {
//        return internalName.contains("/") ? internalName.substring(internalName.lastIndexOf("/") + 1) : internalName;
//    }
//}
