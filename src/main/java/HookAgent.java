import java.lang.instrument.*;
import java.lang.reflect.*;
import java.io.*;

public class HookAgent {
    // 配置绝对输出路径（注意转义）
    private static final String DUMP_DIR = "D:\\dump\\";

    public static void premain(String args, Instrumentation inst) {
        // 确保输出目录存在
        new File(DUMP_DIR).mkdirs();

        try {
            // 获取 ClassLoader 的 defineClass 方法
            Method defineClass = ClassLoader.class.getDeclaredMethod(
                    "defineClass",
                    String.class, byte[].class, int.class, int.class
            );
            defineClass.setAccessible(true);

            // 创建动态代理
            Method proxy = (Method) Proxy.newProxyInstance(
                    HookAgent.class.getClassLoader(),
                    new Class[] { Method.class },
                    (proxyObj, method, params) -> {
                        // 当调用 invoke() 方法时
                        Object target = params[0]; // ClassLoader 实例
                        if ("invoke".equals(method.getName())) {
                            Object[] argsArr = (Object[]) params[2]; // 方法参数

                            String className = (String) argsArr[0];
                            byte[] classData = (byte[]) argsArr[1];

                            // 只 dump 目标包下的类
                            if (className != null  ) {
                                dumpClass(className, classData);
                            }
                        }

                        // 调用原始方法
                        return defineClass.invoke(target, params);
                    }
            );

            // 替换系统方法
            Field methodAccessorField = Method.class.getDeclaredField("methodAccessor");
            methodAccessorField.setAccessible(true);
            methodAccessorField.set(defineClass, proxy);

            System.out.println("[SUCCESS] Hook installed for defineClass");
        } catch (Exception e) {
            System.err.println("[ERROR] Failed to install hook: " + e);
            e.printStackTrace();
        }
    }

    private static void dumpClass(String name, byte[] data) {

        try {
            // 转换类名路径（com/example/Test -> com_example_Test.class）
            String fileName = name.replace('/', '_') + ".class";
            File output = new File(DUMP_DIR + fileName);

            try (FileOutputStream fos = new FileOutputStream(output)) {
                fos.write(data);
                System.out.println("[DUMP] Saved decrypted class: " + output.getAbsolutePath());
            }
        } catch (IOException e) {
            System.err.println("[ERROR] Failed to dump class: " + name);
        }
    }
}
