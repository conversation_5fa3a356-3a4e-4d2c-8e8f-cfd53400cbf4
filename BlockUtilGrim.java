package meteordevelopment.meteorclient.utils.world;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
	import meteordevelopment.meteorclient.MeteorClient;
	import meteordevelopment.meteorclient.gui.tabs.builtin.GuiTab.GuiScreen.ConstantPool;
	import meteordevelopment.meteorclient.systems.modules.Module;
	import meteordevelopment.meteorclient.utils.Base;
	import meteordevelopment.meteorclient.utils.player.PlayerUtils;
	import meteordevelopment.meteorclient.utils.player.Rotation;
	import net.minecraft.class_1268;
	import net.minecraft.class_1269;
	import net.minecraft.class_1297;
	import net.minecraft.class_1511;
	import net.minecraft.class_1542;
	import net.minecraft.class_2246;
	import net.minecraft.class_2248;
	import net.minecraft.class_2338;
	import net.minecraft.class_2350;
	import net.minecraft.class_238;
	import net.minecraft.class_243;
	import net.minecraft.class_2508;
	import net.minecraft.class_2551;
	import net.minecraft.class_2680;
	import net.minecraft.class_2848;
	import net.minecraft.class_2879;
	import net.minecraft.class_2885;
	import net.minecraft.class_3965;
	import net.minecraft.class_7713;
	import net.minecraft.class_2848.class_2849;

	public class BlockUtilGrim implements Base {
		public static final List<class_2248> SneakBlocks = Arrays.asList(
			class_2246.field_10443,
			class_2246.field_10034,
			class_2246.field_10380,
			class_2246.field_9980,
			class_2246.field_10486,
			class_2246.field_40285,
			class_2246.field_10246,
			class_2246.field_42740,
			class_2246.field_10535,
			class_2246.field_10333,
			class_2246.field_10312,
			class_2246.field_10228,
			class_2246.field_10200,
			class_2246.field_10608,
			class_2246.field_10485,
			class_2246.field_10199,
			class_2246.field_10407,
			class_2246.field_10063,
			class_2246.field_10203,
			class_2246.field_10600,
			class_2246.field_10275,
			class_2246.field_10051,
			class_2246.field_10140,
			class_2246.field_10532,
			class_2246.field_10268,
			class_2246.field_10605,
			class_2246.field_10373,
			class_2246.field_10055,
			class_2246.field_10068,
			class_2246.field_10371,
			class_2246.field_16492
		);
		public static final List<Class> SneakBlockClass = Arrays.asList(class_2508.class, class_7713.class, class_2551.class);

		public static boolean canPlace(class_2338 pos, boolean strictDirection) {
			return getInteractDirection(pos, strictDirection) != null;
		}

		public static boolean isSneakBlockClass(class_2248 block) {
			if (block == null) {
				return false;
			} else {
				for (Class clazz : SneakBlockClass) {
					if (clazz.isInstance(block)) {
						return true;
					}
				}

				return false;
			}
		}

		public static boolean canPlaceIf(class_2338 pos, boolean strictDirection, class_2350 direction) {
			return getInteractDirectionIf(pos, strictDirection, direction) != null;
		}

		public static boolean placeBlock(class_2338 pos) {
			return placeBlock(pos, true, true, true);
		}

		public static boolean placeBlock(class_2338 pos, boolean strictDirection, boolean clientSwing, boolean rotate) {
			class_2350 direction = getInteractDirection(pos, strictDirection);
			if (direction == null) {
				return false;
			} else {
				class_2338 neighbor = pos.method_10093(direction.method_10153());
				return placeBlock(neighbor, direction, clientSwing, rotate);
			}
		}

		public static boolean placeUpBlock(class_2338 pos, boolean strictDirection, boolean clientSwing, boolean rotate) {
			class_2350 direction = getInteractDirectionSlabBlock(pos, strictDirection);
			if (direction == null) {
				return false;
			} else {
				class_2338 neighbor = pos.method_10093(direction.method_10153());
				return placeUpBlock(neighbor, direction, clientSwing, rotate);
			}
		}

		public static boolean placeDownBlock(class_2338 pos, boolean strictDirection, boolean clientSwing, boolean rotate) {
			class_2350 direction = getInteractDirectionSlabBlock(pos, strictDirection);
			if (direction == null) {
				return false;
			} else if (!BlockUtils.canSee_alien(pos, direction)) {
				return false;
			} else {
				class_2338 neighbor = pos.method_10093(direction.method_10153());
				return placeDownBlock(neighbor, direction, clientSwing, rotate);
			}
		}

		public static boolean placeBlockByFaceDirection(class_2338 pos, boolean strictDirection, boolean clientSwing, boolean rotate, class_2350 faceDirection) {
			class_2350 direction = getInteractDirection(pos, strictDirection);
			if (direction == null) {
				return false;
			} else {
				class_2338 neighbor = pos.method_10093(direction.method_10153());
				return placeBlockByFaceDirection(pos, neighbor, direction, clientSwing, rotate, faceDirection);
			}
		}

		public static boolean placeBlockByFaceDirection(
			class_2338 initPos, class_2338 pos, class_2350 direction, boolean clientSwing, boolean rotate, class_2350 faceDirection
		) {
			class_243 hitVec = pos.method_46558().method_1019(new class_243(direction.method_23955()).method_1021(ConstantPool.const_9ns4yAlKrc6ld9e));
			if (rotate) {
				Rotation rotation = new Rotation(hitVec).setPriority(10);
				MeteorClient.ROTATIONGRIM.register(rotation);
				rotation.setYaw(getDirectionYaw(faceDirection));
				rotation.setPitch(meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin.GameOwnershipResponse.Item.ConstantPool.const_VR0w4fSXzuE7FB1);
				boolean rot = MeteorClient.ROTATIONGRIM.register(rotation);
				if (!rot) {
					return false;
				}
			}

			boolean placed = placeBlock(new class_3965(hitVec, direction, pos, false), clientSwing);
			MeteorClient.ROTATIONGRIM.sync();
			return placed;
		}

		public static float getDirectionYaw(class_2350 direction) {
			if (direction == null) {
				return 0.0F;
			} else {
				switch (direction) {
					case field_11043:
						return javassist.CtNewClass.ConstantPool.const_cyP1ut2d3kobr5A;
					case field_11035:
						return 0.0F;
					case field_11039:
						return meteordevelopment.meteorclient.systems.modules.ggboy.OffFireWork.ConstantPool.const_odXVnbAldIlBZyD;
					case field_11034:
						return javassist.compiler.ast.Stmnt.ConstantPool.const_EoGBUm9ZtTZdRM0;
					default:
						return 0.0F;
				}
			}
		}

		public static boolean placeBlock(class_2338 pos, class_2350 direction, boolean clientSwing, boolean rotate) {
			class_243 hitVec = pos.method_46558()
				.method_1019(
					new class_243(direction.method_23955()).method_1021(meteordevelopment.meteorclient.events.render.HeldItemRendererEvent.ConstantPool.const_aT3wGe2eyCDSynL)
				);
			if (rotate) {
				boolean rot = MeteorClient.ROTATIONGRIM.register(new Rotation(hitVec).setPriority(10));
				if (!rot) {
					return false;
				}
			}

			boolean placed = placeBlock(new class_3965(hitVec, direction, pos, false), clientSwing);
			MeteorClient.ROTATIONGRIM.sync();
			return placed;
		}

		public static boolean placeUpBlock(class_2338 pos, class_2350 direction, boolean clientSwing, boolean rotate) {
			class_243 hitVec = pos.method_46558()
				.method_1031(0.0, meteordevelopment.meteorclient.commands.arguments.SettingArgumentType.ConstantPool.const_9zESTdm2W0rZQMH, 0.0);
			if (rotate) {
				boolean rot = MeteorClient.ROTATIONGRIM.register(new Rotation(hitVec).setPriority(10));
				if (!rot) {
					return false;
				}
			}

			boolean placed = placeBlock(new class_3965(hitVec, direction, pos, false), clientSwing);
			MeteorClient.ROTATIONGRIM.sync();
			return placed;
		}

		public static boolean placeDownBlock(class_2338 pos, class_2350 direction, boolean clientSwing, boolean rotate) {
			class_243 hitVec = pos.method_46558().method_1031(0.0, javassist.tools.reflect.Metalevel.ConstantPool.const_9NlVNacaaQNx66I, 0.0);
			if (rotate) {
				boolean rot = MeteorClient.ROTATIONGRIM.register(new Rotation(hitVec).setPriority(10));
				if (!rot) {
					return false;
				}
			}

			boolean placed = placeBlock(new class_3965(hitVec, direction, pos, false), clientSwing);
			MeteorClient.ROTATIONGRIM.sync();
			return placed;
		}

		public static boolean placeBlock(class_3965 hitResult, boolean clientSwing) {
			return placeBlockImmediately(hitResult, clientSwing);
		}

		public static boolean placeBlockImmediately(class_3965 result, boolean clientSwing) {
			class_2680 state = mc.field_1687.method_8320(result.method_17777());
			boolean shouldSneak = (SneakBlocks.contains(state.method_26204()) || isSneakBlockClass(mc.field_1687.method_8320(result.method_17777()).method_26204()))
				&& !mc.field_1724.method_5715();
			if (shouldSneak) {
				mc.field_1724.field_3944.method_52787(new class_2848(mc.field_1724, class_2849.field_12979));
			}

			class_1269 actionResult = placeBlockInternally(result);
			if (actionResult.method_23665() && actionResult.method_23666()) {
				if (clientSwing) {
					mc.field_1724.method_6104(class_1268.field_5808);
				} else {
					mc.method_1562().method_52787(new class_2879(class_1268.field_5808));
				}
			}

			if (shouldSneak) {
				mc.field_1724.field_3944.method_52787(new class_2848(mc.field_1724, class_2849.field_12984));
			}

			return actionResult.method_23665();
		}

		private static class_1269 placeBlockInternally(class_3965 hitResult) {
			return mc.field_1761.method_2896(mc.field_1724, class_1268.field_5808, hitResult);
		}

		public static class_2350 getInteractDirection(class_2338 blockPos, boolean strictDirection) {
			Set<class_2350> ncpDirections = getPlaceDirectionsNCP(mc.field_1724.method_33571(), blockPos.method_46558());
			class_2350 interactDirection = null;

			for (class_2350 direction : class_2350.values()) {
				class_2680 state = mc.field_1687.method_8320(blockPos.method_10093(direction));
				if (!state.method_26215() && state.method_26227().method_15769() && (!strictDirection || ncpDirections.contains(direction.method_10153()))) {
					interactDirection = direction;
					break;
				}
			}

			return interactDirection == null ? null : interactDirection.method_10153();
		}

		public static class_2350 getInteractDirectionExitUpDown(class_2338 blockPos, boolean strictDirection) {
			Set<class_2350> ncpDirections = getPlaceDirectionsNCP(mc.field_1724.method_33571(), blockPos.method_46558());
			class_2350 interactDirection = null;

			for (class_2350 direction : class_2350.values()) {
				class_2680 state = mc.field_1687.method_8320(blockPos.method_10093(direction));
				if (!state.method_26215()
					&& state.method_26227().method_15769()
					&& (!strictDirection || ncpDirections.contains(direction.method_10153()))
					&& direction != class_2350.field_11036
					&& direction != class_2350.field_11033) {
					interactDirection = direction;
					break;
				}
			}

			return interactDirection == null ? null : interactDirection.method_10153();
		}

		public static class_2350 getInteractDirectionIf(class_2338 blockPos, boolean strictDirection, class_2350 direction_) {
			Set<class_2350> ncpDirections = getPlaceDirectionsNCP(mc.field_1724.method_33571(), blockPos.method_46558());
			class_2350 interactDirection = null;

			for (class_2350 direction : class_2350.values()) {
				class_2680 state = mc.field_1687.method_8320(blockPos.method_10093(direction));
				if ((!state.method_26215() && state.method_26227().method_15769() || direction == direction_)
					&& (!strictDirection || ncpDirections.contains(direction.method_10153()))) {
					interactDirection = direction;
					break;
				}
			}

			return interactDirection == null ? null : interactDirection.method_10153();
		}

		public static class_2350 getInteractDirectionSlabBlock(class_2338 blockPos, boolean strictDirection) {
			Set<class_2350> ncpDirections = getPlaceDirectionsNCP(mc.field_1724.method_33571(), blockPos.method_46558());
			class_2350 interactDirection = null;

			for (class_2350 direction : class_2350.values()) {
				if (direction != class_2350.field_11036 && direction != class_2350.field_11033) {
					class_2680 state = mc.field_1687.method_8320(blockPos.method_10093(direction));
					if (!state.method_26215() && state.method_26227().method_15769() && (!strictDirection || ncpDirections.contains(direction.method_10153()))) {
						interactDirection = direction;
						break;
					}
				}
			}

			return interactDirection == null ? null : interactDirection.method_10153();
		}

		public static Set<class_2350> getPlaceDirectionsNCP(class_243 eyePos, class_243 blockPos) {
			return getPlaceDirectionsNCP(eyePos.field_1352, eyePos.field_1351, eyePos.field_1350, blockPos.field_1352, blockPos.field_1351, blockPos.field_1350);
		}

		public static Set<class_2350> getPlaceDirectionsNCP(double x, double y, double z, double dx, double dy, double dz) {
			double xdiff = x - dx;
			double ydiff = y - dy;
			double zdiff = z - dz;
			Set<class_2350> dirs = new HashSet(6);
			if (ydiff > meteordevelopment.meteorclient.systems.modules.world.StashFinder.ChunkScreen.ConstantPool.const_cxvCdIeEMrpY9No) {
				dirs.add(class_2350.field_11036);
			} else if (ydiff < meteordevelopment.meteorclient.systems.modules.misc.BetterChat.ConstantPool.const_YjbvvMDFWrqOxKs) {
				dirs.add(class_2350.field_11033);
			} else {
				dirs.add(class_2350.field_11036);
				dirs.add(class_2350.field_11033);
			}

			if (xdiff > meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorPlus.ConstantPool.const_AmkJGNWj2MVGL4H) {
				dirs.add(class_2350.field_11034);
			} else if (xdiff < meteordevelopment.meteorclient.gui.utils.WindowConfig.ConstantPool.const_gBqjx3PljQsWyjJ) {
				dirs.add(class_2350.field_11039);
			} else {
				dirs.add(class_2350.field_11034);
				dirs.add(class_2350.field_11039);
			}

			if (zdiff > meteordevelopment.meteorclient.systems.modules.ggboy.RangeCheck.ConstantPool.const_t0Ca1TyhobkWg5t) {
				dirs.add(class_2350.field_11035);
			} else if (zdiff < javassist.bytecode.stackmap.TypeData.TypeVar.ConstantPool.const_wr29CELQ5jxeFIZ) {
				dirs.add(class_2350.field_11043);
			} else {
				dirs.add(class_2350.field_11035);
				dirs.add(class_2350.field_11043);
			}

			return dirs;
		}

		public static void clickBlock(class_2338 pos, class_2350 side, boolean rotate, class_1268 hand, SwingSide swingSide) {
			class_243 directionVec = new class_243(
				(double)pos.method_10263()
					+ javax.annotation.OverridingMethodsMustInvokeSuper.ConstantPool.const_kHyleDNJG5B4uJ6
					+ (double)side.method_10163().method_10263()
						* meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly.AutoPilotMode.ConstantPool.const_Slmo4OM8l7Ygd6t,
				(double)pos.method_10264()
					+ meteordevelopment.meteorclient.systems.accounts.Accounts.1.ConstantPool.const_1blw9ty2mGSF7bQ
					+ (double)side.method_10163().method_10264() * meteordevelopment.meteorclient.systems.modules.world.Ambience.ConstantPool.const_kAoMdsM3a9a9KxQ,
				(double)pos.method_10260()
					+ javassist.util.proxy.DefineClassHelper.Helper.ConstantPool.const_KPtv9GBxn56Yc4I
					+ (double)side.method_10163().method_10260() * meteordevelopment.orbit.listeners.LambdaListener.ConstantPool.const_Pq3IHaA6Bbqcd2V
			);
			PlayerUtils.swingHand(hand, swingSide);
			class_3965 result = new class_3965(directionVec, side, pos, false);
			if (rotate) {
				boolean rot = MeteorClient.ROTATIONGRIM.register(new Rotation(directionVec).setPriority(10));
				if (!rot) {
					return;
				}
			}

			Module.sendSequencedPacket(id -> new class_2885(hand, result, id));
			MeteorClient.ROTATIONGRIM.sync();
		}

		public static boolean intersectsEntity(class_2338 pos) {
			if (pos == null) {
				return true;
			} else {
				for (class_1297 entity : mc.field_1687.method_18112()) {
					if (!(entity instanceof class_1511)
						&& (
							entity.method_5829().method_994(new class_238(pos)) && entity.method_24828()
								|| entity instanceof class_1542 && entity.method_5829().method_994(new class_238(pos.method_10084()))
						)) {
						return true;
					}
				}

				return false;
			}
		}

		public static boolean intersectsAnyEntity(class_2338 pos) {
			if (pos == null) {
				return true;
			} else {
				for (class_1297 entity : mc.field_1687.method_18112()) {
					if (entity.method_5829().method_994(new class_238(pos))) {
						return true;
					}
				}

				return false;
			}
		}
	}
