package meteordevelopment.meteorclient.systems.modules.ggboy;

import baritone.api.BaritoneAPI;
import io.netty.handler.codec.socks.SocksAuthRequestDecoder$1$ConstantPool;
import io.netty.handler.codec.socks.SocksAuthResponseDecoder$State$ConstantPool;
import io.netty.handler.codec.socks.SocksInitRequestDecoder$1$ConstantPool;
import io.netty.handler.codec.socksx.v4.DefaultSocks4CommandRequest$ConstantPool;
import java.util.ArrayList;
import java.util.List;
import javassist.bytecode.AccessFlag$ConstantPool;
import javassist.bytecode.AnnotationsAttribute$ConstantPool;
import javassist.bytecode.BootstrapMethodsAttribute$BootstrapMethod$ConstantPool;
import javassist.bytecode.SignatureAttribute$1$ConstantPool;
import javassist.bytecode.SignatureAttribute$TypeArgument$ConstantPool;
import javassist.bytecode.SourceFileAttribute$ConstantPool;
import javassist.bytecode.analysis.Subroutine$ConstantPool;
import javassist.bytecode.stackmap.TypeData$ArrayType$ConstantPool;
import javassist.compiler.ast.CallExpr$ConstantPool;
import javassist.runtime.Cflow$ConstantPool;
import javassist.tools.reflect.CannotInvokeException$ConstantPool;
import javassist.tools.rmi.StubGenerator$ConstantPool;
import javassist.util.proxy.ProxyFactory$Find2MethodsArgs$ConstantPool;
import meteordevelopment.meteorclient.commands.arguments.CompoundNbtTagArgumentType$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.DisconnectCommand$ConstantPool;
import meteordevelopment.meteorclient.commands.commands.InputCommand$ConstantPool;
import meteordevelopment.meteorclient.events.entity.player.PushFluidsEvent$ConstantPool;
import meteordevelopment.meteorclient.events.meteor.KeyEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent$Receive$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.screens.ModuleScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.ColorSettingScreen$WBrightnessQuad$ConstantPool;
import meteordevelopment.meteorclient.gui.screens.settings.PacketBoolSettingScreen$ConstantPool;
import meteordevelopment.meteorclient.gui.themes.meteor.widgets.pressable.WMeteorTriangle$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.WTopBar$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.input.WSlider$ConstantPool;
import meteordevelopment.meteorclient.gui.widgets.pressable.WTriangle$ConstantPool;
import meteordevelopment.meteorclient.pathing.IPathManager$ISettings$ConstantPool;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.renderer.Mesh$Attrib$ConstantPool;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.settings.ColorSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.PacketListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.SoundEventListSetting$ConstantPool;
import meteordevelopment.meteorclient.settings.StringSetting;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$Handler$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.ArmorHud$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.CrystalAura$PauseMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$StaticGroundListener$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.SpeedMine$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ESP$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.marker.Sphere2dMarker$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.InfinityMiner$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.Nuker;
import meteordevelopment.meteorclient.systems.modules.world.Nuker$ListMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.PacketMine$MyBlock$ConstantPool;
import meteordevelopment.meteorclient.systems.waypoints.Waypoint$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.MeteorStarscript$ConstantPool;
import meteordevelopment.meteorclient.utils.network.packet.AstralPacket$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.player.Safety$ConstantPool;
import meteordevelopment.meteorclient.utils.player.SlotUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.render.ByteTexture$Format$ConstantPool;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.meteorclient.utils.usercheck.CheckUtils;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.compiler.Expr$Group$ConstantPool;
import meteordevelopment.starscript.compiler.Expr$Unary$ConstantPool;
import net.minecraft.block.Blocks;
import net.minecraft.client.gui.screen.ingame.ShulkerBoxScreen;
import net.minecraft.component.DataComponentTypes;
import net.minecraft.item.ArmorItem;
import net.minecraft.item.ArmorMaterials;
import net.minecraft.item.Item;
import net.minecraft.item.Items;
import net.minecraft.item.PickaxeItem;
import net.minecraft.item.ShovelItem;
import net.minecraft.item.SwordItem;
import net.minecraft.network.packet.s2c.common.DisconnectS2CPacket;
import net.minecraft.screen.slot.Slot;
import net.minecraft.screen.slot.SlotActionType;
import net.minecraft.text.Text;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import org.reflections.ReflectionUtils$1$ConstantPool;
import org.reflections.scanners.SubTypesScanner$ConstantPool;
import org.reflections.serializers.XmlSerializer$ConstantPool;
import org.reflections.util.FilterBuilder$Matcher$ConstantPool;

public class AutoMineSand extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final SettingGroup sgRender = this.settings.createGroup(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(WTriangle$ConstantPool.const_KmSQrajFgteYNbe))));
   private AutoMineSand.Status status = AutoMineSand.Status.FindSupplyBox;
   private boolean startMine = false;
   private boolean moving = false;
   private boolean checked = false;
   private ArrayList<BlockPos> shulckerPos = new ArrayList<>();
   private ArrayList<BlockPos> shulckerPos_temp = new ArrayList<>();
   private ArrayList<BlockPos> supply_box_pos = new ArrayList<>();
   private ArrayList<BlockPos> supply_box_pos_temp = new ArrayList<>();
   private BlockPos finishPos = null;
   private final Setting<Boolean> dropGarbage = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(InfinityMiner$ConstantPool.const_daAAVQ1DgCY34C2))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(AnnotationsAttribute$ConstantPool.const_6oyDfhB8ZFIvJU5))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Boolean> render = this.sgRender
      .add(
         new BoolSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(PacketListSetting$ConstantPool.const_9LT6PM15oqZXe79))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(CallExpr$ConstantPool.const_Wft5QFxSgA42viZ))))
            .defaultValue(true)
            .build()
      );
   private final Setting<Integer> Shulcker_range = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(PacketEvent$Receive$ConstantPool.const_aNOEajxYi6q57E7))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(CannotInvokeException$ConstantPool.const_F4Ftnkjg3roxWyL))))
            .defaultValue(3)
            .sliderRange(3, 30)
            .build()
      );
   private final Setting<ShapeMode> shapeMode = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(Expr$Unary$ConstantPool.const_bA6blq2k3O7O9At)))))
                  .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(SoundEventListSetting$ConstantPool.const_avBnJhdIjOuMi4m)))))
               .defaultValue(ShapeMode.Both))
            .build()
      );
   private final Setting<SettingColor> readySideColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(SocksAuthResponseDecoder$State$ConstantPool.const_PIYM6AQ6AGWFvvG))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(WTopBar$ConstantPool.const_mPGnVuvWsTB2li6))))
            .defaultValue(new SettingColor(0, 204, 0, 10))
            .build()
      );
   private final Setting<SettingColor> readyLineColor = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(SubTypesScanner$ConstantPool.const_op7IsAYeT98YQLo))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(SourceFileAttribute$ConstantPool.const_Zg2HSgtuBEvgrcD))))
            .defaultValue(new SettingColor(0, 204, 0, 255))
            .build()
      );
   private final Setting<Boolean> autosupply = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(ProxyFactory$Find2MethodsArgs$ConstantPool.const_SsrzII6626v67E7))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(Sphere2dMarker$ConstantPool.const_qcCc5DKeSQldDPC))))
            .defaultValue(false)
            .build()
      );
   private final Setting<String> supply_box_name = this.sgGeneral
      .add(
         new StringSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(SpeedMine$ConstantPool.const_uDkWwH1gldpkJAt))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(CrystalAura$PauseMode$ConstantPool.const_8GXJySg3YF2m9Jg))))
            .defaultValue(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(BootstrapMethodsAttribute$BootstrapMethod$ConstantPool.const_AQ46667DjKfo6Dt))))
            .visible(this.autosupply::get)
            .build()
      );
   private final Setting<Integer> totem_nums = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(IPathManager$ISettings$ConstantPool.const_rL7LBTZsd9bV4Uh))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(ESP$Mode$ConstantPool.const_ggg5stWnlTLSnBt))))
            .defaultValue(2)
            .sliderRange(1, 10)
            .visible(this.autosupply::get)
            .build()
      );
   private final Setting<Integer> shovel_demage = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(ColorSettingScreen$WBrightnessQuad$ConstantPool.const_KXIyXYlkmyTPo2v))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(ByteTexture$Format$ConstantPool.const_Q4SfEDxCUWO3drn))))
            .defaultValue(20)
            .sliderRange(20, 100)
            .visible(this.autosupply::get)
            .build()
      );
   private final Setting<Integer> foot_counts = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(Safety$ConstantPool.const_NewteVsMIb4OqAS))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(StubGenerator$ConstantPool.const_j14pQjoI0l29YXj))))
            .defaultValue(10)
            .sliderRange(0, 64)
            .visible(this.autosupply::get)
            .build()
      );
   private final Setting<ShapeMode> shapeMode_supply = this.sgRender
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(MicrosoftLogin$Handler$ConstantPool.const_yRbCBGqL24jMo07)))))
                  .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(SignatureAttribute$TypeArgument$ConstantPool.const_8VtUDJ5NGOz6Xj4)))))
               .defaultValue(ShapeMode.Both))
            .build()
      );
   private final Setting<SettingColor> readySideColor_supply = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(DefaultSocks4CommandRequest$ConstantPool.const_oAMTfteQPobnbKq))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(TypeData$ArrayType$ConstantPool.const_qFBhn4cdjdFsruN))))
            .defaultValue(new SettingColor(0, 204, 0, 10))
            .visible(this.autosupply::get)
            .build()
      );
   private final Setting<SettingColor> readyLineColor_supply = this.sgRender
      .add(
         new ColorSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(SocksAuthRequestDecoder$1$ConstantPool.const_vvs9kQqI3GLmhCl))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(Waypoint$ConstantPool.const_AU2BaRcE6l41z6k))))
            .defaultValue(new SettingColor(0, 204, 0, 255))
            .visible(this.autosupply::get)
            .build()
      );
   private final Setting<Boolean> AutoLog = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(WSlider$ConstantPool.const_1ClSeFyNrJyPLYP))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(PacketBoolSettingScreen$ConstantPool.const_NX7rurm6cgYrXhM))))
            .defaultValue(false)
            .build()
      );
   private final Setting<Boolean> WithNuker = this.sgGeneral
      .add(
         new BoolSetting.Builder()
            .name(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(KeyEvent$ConstantPool.const_Dv2hGj517UrG80A))))
            .description(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(ElytraFly$StaticGroundListener$ConstantPool.const_ERfNAfmNhwDfPqI))))
            .defaultValue(false)
            .build()
      );
   private boolean supplying = false;
   private AutoMineSand.SupplyType supplyingType = AutoMineSand.SupplyType.Shovel;
   private static boolean supplyed = false;
   private int step = 1;
   private int supply_i = -1;
   private int supply_j = -1;

   public AutoMineSand() {
      super(
         Categories.Ggboy,
         OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(PushFluidsEvent$ConstantPool.const_obRsY4PznwlJwQr))),
         new StringBuilder(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(WMeteorTriangle$ConstantPool.const_MY1vXtqrfgNb6xC)))),
         OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(SlotUtils$ConstantPool.const_nbGsgagygwTB4o9)))
      );
   }

   @Override
   public void onActivate() {
      if (!CheckUtils.check()) {
         for (int i = 0; i < 3; i++) {
            this.info(Text.of(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(ModuleScreen$ConstantPool.const_BLQx8l4doZmt5rl)))));
         }

         if (this.isActive()) {
            this.toggle();
         }
      }

      ((List)BaritoneAPI.getSettings().blocksToDisallowBreaking.get()).add(Blocks.SHULKER_BOX);
      PathManagers.get().stop();
      this.status = AutoMineSand.Status.FindSupplyBox;
      this.startMine = false;
      this.moving = false;
      this.checked = false;
      this.shulckerPos = new ArrayList<>();
      this.shulckerPos_temp = new ArrayList<>();
      this.supply_box_pos = new ArrayList<>();
      this.supply_box_pos_temp = new ArrayList<>();
      this.finishPos = null;
      boolean supplying = false;
      boolean supplyed = false;
      int step = 1;
      int supply_i = -1;
      int supply_j = -1;
   }

   @Override
   public void init() {
      if (this.isActive()) {
         this.toggle();
      }
   }

   @Override
   public void beforeDeactivate() {
      PathManagers.get().stop();
      BaritoneAPI.getSettings().blocksToDisallowBreaking.reset();
      this.status = AutoMineSand.Status.FindSupplyBox;
      this.startMine = false;
      this.moving = false;
      this.checked = false;
      this.shulckerPos = new ArrayList<>();
      this.shulckerPos_temp = new ArrayList<>();
      this.supply_box_pos = new ArrayList<>();
      this.supply_box_pos_temp = new ArrayList<>();
      this.finishPos = null;
      boolean supplying = false;
      boolean supplyed = false;
      int step = 1;
      int supply_i = -1;
      int supply_j = -1;
   }

   @Override
   public void onDeactivate() {
      PathManagers.get().stop();
      BaritoneAPI.getSettings().blocksToDisallowBreaking.reset();
      if (Modules.get().get(Nuker.class).isActive()) {
         Modules.get().get(Nuker.class).toggle();
      }

      this.status = AutoMineSand.Status.FindSupplyBox;
      this.startMine = false;
      this.moving = false;
      this.checked = false;
      this.shulckerPos = new ArrayList<>();
      this.shulckerPos_temp = new ArrayList<>();
      this.supply_box_pos = new ArrayList<>();
      this.supply_box_pos_temp = new ArrayList<>();
      this.finishPos = null;
      boolean supplying = false;
      boolean supplyed = false;
      int step = 1;
      int supply_i = -1;
      int supply_j = -1;
   }

   @EventHandler
   private void onRender(Render3DEvent event) {
      if (this.render.get()) {
         if (this.shulckerPos.size() > 0) {
            for (int i = 0; i < this.shulckerPos.size(); i++) {
               double x1 = this.shulckerPos.get(i).getX();
               double y1 = this.shulckerPos.get(i).getY();
               double z1 = this.shulckerPos.get(i).getZ();
               double x2 = this.shulckerPos.get(i).getX() + 1;
               double y2 = this.shulckerPos.get(i).getY() + 1;
               double z2 = this.shulckerPos.get(i).getZ() + 1;
               event.renderer.box(x1, y1, z1, x2, y2, z2, this.readySideColor.get(), this.readyLineColor.get(), this.shapeMode.get(), 0);
            }
         }

         if (this.supply_box_pos.size() > 0) {
            for (int i = 0; i < this.supply_box_pos.size(); i++) {
               double x1 = this.supply_box_pos.get(i).getX();
               double y1 = this.supply_box_pos.get(i).getY();
               double z1 = this.supply_box_pos.get(i).getZ();
               double x2 = this.supply_box_pos.get(i).getX() + 1;
               double y2 = this.supply_box_pos.get(i).getY() + 1;
               double z2 = this.supply_box_pos.get(i).getZ() + 1;
               event.renderer.box(x1, y1, z1, x2, y2, z2, this.readySideColor_supply.get(), this.readyLineColor_supply.get(), this.shapeMode_supply.get(), 0);
            }
         }
      }
   }

   @EventHandler
   public void onTick(TickEvent.Pre event) {
      if (this.supplying) {
         if (this.supplyingType == AutoMineSand.SupplyType.Food) {
            mc.interactionManager.clickSlot(mc.player.currentScreenHandler.syncId, this.supply_i, 0, SlotActionType.QUICK_MOVE, mc.player);
            this.supplying = false;
            supplyed = true;
            return;
         }

         if (this.supplyingType == AutoMineSand.SupplyType.Totem) {
            if (this.step == 1) {
               mc.interactionManager.clickSlot(mc.player.currentScreenHandler.syncId, this.supply_i, 0, SlotActionType.PICKUP, mc.player);
               this.step = 2;
               return;
            }

            if (this.step == 2) {
               mc.interactionManager.clickSlot(mc.player.currentScreenHandler.syncId, this.supply_j, 0, SlotActionType.PICKUP, mc.player);
               this.step = 1;
               this.supplying = false;
               supplyed = true;
               return;
            }
         }

         if (this.supplyingType == AutoMineSand.SupplyType.Shovel) {
            if (this.step == 1) {
               mc.interactionManager.clickSlot(mc.player.currentScreenHandler.syncId, this.supply_i, 0, SlotActionType.PICKUP, mc.player);
               this.step = 2;
               return;
            }

            if (this.step == 2) {
               mc.interactionManager.clickSlot(mc.player.currentScreenHandler.syncId, this.supply_j, 0, SlotActionType.PICKUP, mc.player);
               this.step = 3;
               return;
            }

            if (this.step == 3) {
               mc.interactionManager.clickSlot(mc.player.currentScreenHandler.syncId, this.supply_i, 0, SlotActionType.PICKUP, mc.player);
               this.step = 1;
               this.supplying = false;
               supplyed = true;
               return;
            }
         }
      }

      BlockPos playerPos = mc.player.getBlockPos();
      if (!this.checked) {
         this.shulckerPos = BlockUtils.findBlockPos(Blocks.SHULKER_BOX, this.Shulcker_range.get().intValue());
         this.shulckerPos_temp = BlockUtils.findBlockPos(Blocks.SHULKER_BOX, this.Shulcker_range.get().intValue());
         if (this.shulckerPos.size() == 0) {
            this.info(Text.of(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(DisconnectCommand$ConstantPool.const_zMwS1zlTFJt5NkM)))));
            this.toggle();
            return;
         }

         this.finishPos = this.shulckerPos.get(0);
         this.checked = true;
      }

      if (!this.WithNuker.get() && Modules.get().get(Nuker.class).isActive()) {
         Modules.get().get(Nuker.class).toggle();
      }

      if (this.dropGarbage.get()) {
         for (int i = 0; i < 36; i++) {
            Item _item = mc.player.getInventory().getStack(i).getItem();
            if (_item == Items.STICK
               || _item == Items.SANDSTONE
               || _item == Items.ARROW
               || _item == Items.BOW
               || _item == Items.ROTTEN_FLESH
               || _item == Items.CACTUS
               || _item == Items.STRING
               || _item == Items.SPIDER_EYE
               || _item == Items.FEATHER
               || _item == Items.CHICKEN
               || _item == Items.COOKED_CHICKEN
               || _item == Items.REDSTONE
               || _item == Items.GLOWSTONE_DUST
               || _item == Items.GUNPOWDER
               || _item == Items.BONE
               || _item == Items.DIRT
               || _item == Items.COBBLESTONE
               || _item == Items.STONE
               || _item == Items.BAKED_POTATO
               || _item == Items.CARROT
               || _item == Items.POTATO
               || _item == Items.POISONOUS_POTATO
               || _item == Items.SUGAR
               || _item instanceof ArmorItem && ((ArmorItem)_item).getMaterial() == ArmorMaterials.LEATHER
               || _item instanceof ArmorItem && ((ArmorItem)_item).getMaterial() == ArmorMaterials.CHAIN
               || _item instanceof ArmorItem && ((ArmorItem)_item).getMaterial() == ArmorMaterials.GOLD
               || _item == Items.COOKED_RABBIT
               || _item == Items.RABBIT
               || _item == Items.RABBIT_FOOT
               || _item == Items.GOLDEN_LEGGINGS
               || _item == Items.GOLDEN_HELMET
               || _item == Items.GOLDEN_CHESTPLATE
               || _item == Items.GOLDEN_BOOTS
               || _item == Items.RABBIT_HIDE) {
               InvUtils.drop().slot(i);
            }
         }
      }

      switch (this.status) {
         case FindSupplyBox:
            if (mc.currentScreen instanceof ShulkerBoxScreen) {
               mc.currentScreen.close();
            }

            if (this.shulckerPos_temp.size() != 0 && this.autosupply.get()) {
               if (!this.moving) {
                  PathManagers.get().moveTo(this.shulckerPos_temp.get(0));
                  this.moving = true;
               }

               BlockPos pos_ = mc.player.getBlockPos();
               if (Math.abs(this.shulckerPos_temp.get(0).getX() - pos_.getX()) == 1
                     && this.shulckerPos_temp.get(0).getZ() - pos_.getZ() == 0
                  || this.shulckerPos_temp.get(0).getX() - pos_.getX() == 0
                     && Math.abs(this.shulckerPos_temp.get(0).getZ() - pos_.getZ()) == 1
                  || Math.abs(this.shulckerPos_temp.get(0).getX() - pos_.getX()) == 0
                     && Math.abs(this.shulckerPos_temp.get(0).getZ() - pos_.getZ()) == 0) {
                  this.moving = false;
                  this.status = AutoMineSand.Status.SetSupplyPos;
               }
            } else {
               this.status = AutoMineSand.Status.mineSand;
            }
            break;
         case mineSand:
            supplyed = false;
            this.supplying = false;
            if (mc.currentScreen instanceof ShulkerBoxScreen) {
               mc.currentScreen.close();
            }

            if (this.WithNuker.get() && !Modules.get().get(Nuker.class).isActive()) {
               Modules.get().get(Nuker.class).toggle();
            }

            if (this.isShouldSupply() != null && this.autosupply.get()) {
               PathManagers.get().stop();
               this.status = AutoMineSand.Status.SupplyMove;
               this.startMine = false;
               this.moving = false;
               this.supply_box_pos_temp = new ArrayList<>(this.supply_box_pos);
               if (Modules.get().get(Nuker.class).isActive()) {
                  Modules.get().get(Nuker.class).toggle();
               }
            } else if (this.isHaveEmptyStack() && !this.startMine) {
               this.startMine = true;
               this.getShovel();
               PathManagers.get().mine(Blocks.SAND);
            } else if (!this.isHaveEmptyStack() && this.shulckerPos.size() != 0) {
               this.startMine = false;
               PathManagers.get().stop();
               this.status = AutoMineSand.Status.closeShulcker;
               if (Modules.get().get(Nuker.class).isActive()) {
                  Modules.get().get(Nuker.class).toggle();
               }
            } else if (!this.isHaveEmptyStack()) {
               PathManagers.get().stop();
               this.startMine = false;
               this.status = AutoMineSand.Status.Finish;
               if (Modules.get().get(Nuker.class).isActive()) {
                  Modules.get().get(Nuker.class).toggle();
               }
            }
            break;
         case closeShulcker:
            if (mc.currentScreen instanceof ShulkerBoxScreen) {
               mc.currentScreen.close();
            }

            if (this.shulckerPos.size() == 0) {
               this.status = AutoMineSand.Status.mineSand;
            } else {
               if (!this.moving) {
                  PathManagers.get().moveTo(this.shulckerPos.get(0));
                  this.moving = true;
               }

               BlockPos pos = mc.player.getBlockPos();
               if (Math.abs(this.shulckerPos.get(0).getX() - pos.getX()) == 1
                     && this.shulckerPos.get(0).getZ() - pos.getZ() == 0
                  || this.shulckerPos.get(0).getX() - pos.getX() == 0
                     && Math.abs(this.shulckerPos.get(0).getZ() - pos.getZ()) == 1
                  || Math.abs(this.shulckerPos.get(0).getX() - pos.getX()) == 0
                     && Math.abs(this.shulckerPos.get(0).getZ() - pos.getZ()) == 0) {
                  this.moving = false;
                  this.status = AutoMineSand.Status.collectSandToShulcker;
               }
            }
            break;
         case collectSandToShulcker:
            if (mc.currentScreen instanceof ShulkerBoxScreen shulkerBoxScreen) {
               if (!this.isCanRemove()) {
                  if (InvUtils.find(Items.SAND).found()) {
                     this.shulckerPos.remove(0);
                     shulkerBoxScreen.close();
                     this.status = AutoMineSand.Status.closeShulcker;
                  } else {
                     shulkerBoxScreen.close();
                     this.status = AutoMineSand.Status.mineSand;
                  }
               } else {
                  for (int ixxxx = 27; ixxxx < 63; ixxxx++) {
                     if (((Slot)mc.player.currentScreenHandler.slots.get(ixxxx)).getStack().getItem() == Items.SAND) {
                        InvUtils.shiftClick().slotId(ixxxx);
                     }
                  }
               }
            } else if (this.shulckerPos.size() > 0) {
               if (mc.world.getBlockState(this.shulckerPos.get(0)).getBlock() == Blocks.SHULKER_BOX) {
                  Rotations.rotate(
                     Rotations.getYaw(this.shulckerPos.get(0)),
                     Rotations.getPitch(this.shulckerPos.get(0)),
                     50,
                     () -> mc.interactionManager
                        .interactBlock(
                           mc.player,
                           Hand.MAIN_HAND,
                           new BlockHitResult(
                              new Vec3d(
                                 this.shulckerPos.get(0).getX() + XmlSerializer$ConstantPool.const_4SeBVrFMR1sLY0j,
                                 this.shulckerPos.get(0).getY() + Expr$Group$ConstantPool.const_HLJQEiTlN7w8JTD,
                                 this.shulckerPos.get(0).getZ() + ArmorHud$ConstantPool.const_39TBbElYeV7Y11c
                              ),
                              Direction.UP,
                              this.shulckerPos.get(0),
                              true
                           )
                        )
                  );
               } else {
                  this.shulckerPos.remove(0);
               }
            } else if (this.shulckerPos.size() == 0) {
               this.info(Text.of(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(MeteorStarscript$ConstantPool.const_rF0Ceaqbn1hmZyo)))));
               this.toggle();
            }
            break;
         case SupplyMove:
            if (this.isShouldSupply() == null) {
               this.status = AutoMineSand.Status.mineSand;
            } else if (this.supply_box_pos_temp.size() == 0) {
               mc.currentScreen.close();
               this.info(Text.of(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(Cflow$ConstantPool.const_N3xOXTQLGrr8Xe2)))));
               if (this.isActive()) {
                  this.toggle();
               }
            } else {
               if (!this.moving) {
                  PathManagers.get().moveTo(this.supply_box_pos_temp.get(0));
                  this.moving = true;
               }

               BlockPos pos_1 = mc.player.getBlockPos();
               if (Math.abs(this.supply_box_pos_temp.get(0).getX() - pos_1.getX()) == 1
                     && this.supply_box_pos_temp.get(0).getZ() - pos_1.getZ() == 0
                  || this.supply_box_pos_temp.get(0).getX() - pos_1.getX() == 0
                     && Math.abs(this.supply_box_pos_temp.get(0).getZ() - pos_1.getZ()) == 1
                  || Math.abs(this.supply_box_pos_temp.get(0).getX() - pos_1.getX()) == 0
                     && Math.abs(this.supply_box_pos_temp.get(0).getZ() - pos_1.getZ()) == 0) {
                  this.moving = false;
                  this.status = AutoMineSand.Status.Supply;
               }
            }
            break;
         case Supply:
            if (mc.currentScreen instanceof ShulkerBoxScreen shulkerBoxScreen) {
               boolean mineAgin = false;
               if (this.isShouldSupply() == AutoMineSand.SupplyType.Food) {
                  for (int ix = 0; ix < 27; ix++) {
                     if (((Slot)mc.player.currentScreenHandler.slots.get(ix)).getStack().getItem() == Items.GOLDEN_CARROT
                        || ((Slot)mc.player.currentScreenHandler.slots.get(ix)).getStack().getItem() == Items.BREAD) {
                        if (!supplyed) {
                           this.supplying = true;
                           this.supplyingType = AutoMineSand.SupplyType.Food;
                           this.supply_i = ix;
                           return;
                        }

                        supplyed = false;
                        if (this.isShouldSupply() != AutoMineSand.SupplyType.Food) {
                           shulkerBoxScreen.close();
                           InvUtils.switchToSlot(mc.player.getInventory().selectedSlot);
                           this.status = AutoMineSand.Status.mineSand;
                           mineAgin = true;
                           break;
                        }
                     }
                  }

                  if (mineAgin) {
                     break;
                  }
               }

               if (this.isShouldSupply() == AutoMineSand.SupplyType.Totem) {
                  for (int ixx = 0; ixx < 27; ixx++) {
                     if (((Slot)mc.player.currentScreenHandler.slots.get(ixx)).getStack().getItem() == Items.TOTEM_OF_UNDYING) {
                        for (int j = 27; j < 63; j++) {
                           if (((Slot)mc.player.currentScreenHandler.slots.get(j)).getStack().getItem() == Items.AIR) {
                              if (!supplyed) {
                                 this.supplying = true;
                                 this.supplyingType = AutoMineSand.SupplyType.Totem;
                                 this.supply_i = ixx;
                                 this.supply_j = j;
                                 this.step = 1;
                                 return;
                              }

                              supplyed = false;
                              if (this.isShouldSupply() != AutoMineSand.SupplyType.Totem) {
                                 InvUtils.switchToSlot(mc.player.getInventory().selectedSlot);
                                 this.status = AutoMineSand.Status.mineSand;
                                 mineAgin = true;
                                 break;
                              }
                           } else if (((Slot)mc.player.currentScreenHandler.slots.get(j)).getStack().getItem() == Items.SAND) {
                              InvUtils.drop().slot(j);
                              if (!supplyed) {
                                 this.supplying = true;
                                 this.supplyingType = AutoMineSand.SupplyType.Totem;
                                 this.supply_i = ixx;
                                 this.supply_j = j;
                                 this.step = 1;
                                 return;
                              }

                              supplyed = false;
                              if (this.isShouldSupply() != AutoMineSand.SupplyType.Totem) {
                                 InvUtils.switchToSlot(mc.player.getInventory().selectedSlot);
                                 this.status = AutoMineSand.Status.mineSand;
                                 mineAgin = true;
                                 break;
                              }
                           }
                        }
                     }
                  }

                  if (mineAgin) {
                     break;
                  }
               }

               if (this.isShouldSupply() == AutoMineSand.SupplyType.Shovel) {
                  for (int ixxx = 0; ixxx < 27; ixxx++) {
                     if (((Slot)mc.player.currentScreenHandler.slots.get(ixxx)).getStack().getItem() instanceof ShovelItem
                        && ((Slot)mc.player.currentScreenHandler.slots.get(ixxx)).getStack().getMaxDamage()
                              - ((Slot)mc.player.currentScreenHandler.slots.get(ixxx)).getStack().getDamage()
                           > this.shovel_demage.get()) {
                        if (!this.findShovel()) {
                           for (int jx = 27; jx < 63; jx++) {
                              if (((Slot)mc.player.currentScreenHandler.slots.get(jx)).getStack().getItem() == Items.SAND
                                 || ((Slot)mc.player.currentScreenHandler.slots.get(jx)).getStack().getItem() == Items.AIR) {
                                 if (((Slot)mc.player.currentScreenHandler.slots.get(jx)).getStack().getItem() == Items.SAND) {
                                    InvUtils.drop().slotId(jx);
                                 }

                                 if (!supplyed) {
                                    this.supplying = true;
                                    this.supplyingType = AutoMineSand.SupplyType.Shovel;
                                    this.supply_i = ixxx;
                                    this.supply_j = jx;
                                    this.step = 1;
                                    return;
                                 }

                                 supplyed = false;
                                 if (this.isShouldSupply() != AutoMineSand.SupplyType.Shovel) {
                                    this.status = AutoMineSand.Status.mineSand;
                                    mineAgin = true;
                                    break;
                                 }
                              }
                           }
                        } else {
                           for (int jxx = 27; jxx < 63; jxx++) {
                              if (((Slot)mc.player.currentScreenHandler.slots.get(jxx)).getStack().getItem() instanceof ShovelItem
                                 && ((Slot)mc.player.currentScreenHandler.slots.get(jxx)).getStack().getMaxDamage()
                                       - ((Slot)mc.player.currentScreenHandler.slots.get(jxx)).getStack().getDamage()
                                    <= this.shovel_demage.get()) {
                                 if (!supplyed) {
                                    this.supplying = true;
                                    this.supplyingType = AutoMineSand.SupplyType.Shovel;
                                    this.supply_i = ixxx;
                                    this.supply_j = jxx;
                                    this.step = 1;
                                    return;
                                 }

                                 supplyed = false;
                                 if (this.isShouldSupply() != AutoMineSand.SupplyType.Shovel) {
                                    this.status = AutoMineSand.Status.mineSand;
                                    mineAgin = true;
                                    break;
                                 }
                              }
                           }
                        }

                        if (mineAgin) {
                           break;
                        }
                     }
                  }

                  if (mineAgin) {
                     break;
                  }
               }

               this.supply_box_pos_temp.remove(0);
               shulkerBoxScreen.close();
               this.status = AutoMineSand.Status.SupplyMove;
            } else if (this.supply_box_pos_temp.size() > 0) {
               if (mc.world.getBlockState(this.supply_box_pos_temp.get(0)).getBlock() == Blocks.SHULKER_BOX) {
                  Rotations.rotate(
                     Rotations.getYaw(this.supply_box_pos_temp.get(0)),
                     Rotations.getPitch(this.supply_box_pos_temp.get(0)),
                     50,
                     () -> mc.interactionManager
                        .interactBlock(
                           mc.player,
                           Hand.MAIN_HAND,
                           new BlockHitResult(
                              new Vec3d(
                                 this.supply_box_pos_temp.get(0).getX() + SocksInitRequestDecoder$1$ConstantPool.const_wbDFDH22I64TdPa,
                                 this.supply_box_pos_temp.get(0).getY() + Nuker$ListMode$ConstantPool.const_y2DtmFiRq7EJ2M8,
                                 this.supply_box_pos_temp.get(0).getZ() + FilterBuilder$Matcher$ConstantPool.const_eyLGG91rhf06luf
                              ),
                              Direction.UP,
                              this.supply_box_pos_temp.get(0),
                              true
                           )
                        )
                  );
               } else {
                  this.supply_box_pos_temp.remove(0);
               }
            } else if (this.supply_box_pos_temp.size() == 0) {
               this.info(Text.of(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(InputCommand$ConstantPool.const_RnvYv0YBFbY2BzH)))));
               this.toggle();
            }
            break;
         case SetSupplyPos:
            if (mc.currentScreen instanceof ShulkerBoxScreen shulkerBoxScreenx) {
               if (shulkerBoxScreenx.getTitle().getString().equals(this.supply_box_name.get())) {
                  this.supply_box_pos.add(this.shulckerPos_temp.get(0));

                  for (int ixxxxx = 0; ixxxxx < this.shulckerPos.size(); ixxxxx++) {
                     if (this.shulckerPos.get(ixxxxx).getX() == this.shulckerPos_temp.get(0).getX()
                        && this.shulckerPos.get(ixxxxx).getY() == this.shulckerPos_temp.get(0).getY()
                        && this.shulckerPos.get(ixxxxx).getZ() == this.shulckerPos_temp.get(0).getZ()) {
                        this.shulckerPos.remove(ixxxxx);
                     }
                  }
               }

               shulkerBoxScreenx.close();
               this.shulckerPos_temp.remove(0);
               this.status = AutoMineSand.Status.FindSupplyBox;
            } else if (this.shulckerPos_temp.size() > 0) {
               if (mc.world.getBlockState(this.shulckerPos_temp.get(0)).getBlock() == Blocks.SHULKER_BOX) {
                  Rotations.rotate(
                     Rotations.getYaw(this.shulckerPos_temp.get(0)),
                     Rotations.getPitch(this.shulckerPos_temp.get(0)),
                     50,
                     () -> mc.interactionManager
                        .interactBlock(
                           mc.player,
                           Hand.MAIN_HAND,
                           new BlockHitResult(
                              new Vec3d(
                                 this.shulckerPos_temp.get(0).getX() + Subroutine$ConstantPool.const_VWFYOEm6jMBvCCB,
                                 this.shulckerPos_temp.get(0).getY() + AstralPacket$ConstantPool.const_AndzFGNqTlNgXtx,
                                 this.shulckerPos_temp.get(0).getZ() + SignatureAttribute$1$ConstantPool.const_J1j1AJXQeUWe9e2
                              ),
                              Direction.UP,
                              this.shulckerPos_temp.get(0),
                              true
                           )
                        )
                  );
               } else {
                  this.shulckerPos_temp.remove(0);
                  this.status = AutoMineSand.Status.FindSupplyBox;
               }
            }
            break;
         case Finish:
            if (this.finishPos != null) {
               if (!this.moving) {
                  this.info(Text.of(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(PacketMine$MyBlock$ConstantPool.const_oTWq5fTPOoo6bkO)))));
                  PathManagers.get().moveTo(this.finishPos);
                  this.moving = true;
               }

               BlockPos pos_finish = mc.player.getBlockPos();
               if ((Math.abs(this.finishPos.getX() - pos_finish.getX()) != 1 || this.finishPos.getZ() - pos_finish.getZ() != 0)
                  && (
                     this.finishPos.getX() - pos_finish.getX() != 0 || Math.abs(this.finishPos.getZ() - pos_finish.getZ()) != 1
                  )
                  && (
                     Math.abs(this.finishPos.getX() - pos_finish.getX()) != 0
                        || Math.abs(this.finishPos.getZ() - pos_finish.getZ()) != 0
                  )) {
                  return;
               }

               PathManagers.get().stop();
               this.moving = false;
               if (this.AutoLog.get()) {
                  this.toggle();
                  mc.player
                     .networkHandler
                     .onDisconnect(
                        new DisconnectS2CPacket(Text.literal(OU6NGVveGw(8Yimryw8JU(8doOIdnoIM(ReflectionUtils$1$ConstantPool.const_aoNOlNNGVzn4Olv)))))
                     );
               }
            }

            this.toggle();
      }
   }

   public void getShovel() {
      for (int i = 0; i < 36; i++) {
         if (mc.player.getInventory().getStack(i).getItem() instanceof ShovelItem
            && mc.player.getInventory().getStack(i).getMaxDamage() - mc.player.getInventory().getStack(i).getDamage() > this.shovel_demage.get()
            )
          {
            if (i < 9) {
               InvUtils.switchToSlot(i);
               return;
            }

            for (int j = 0; j < 9; j++) {
               if (mc.player.getInventory().getStack(j).isEmpty()
                  || !(mc.player.getInventory().getStack(j).getItem() instanceof PickaxeItem)
                     && !(mc.player.getInventory().getStack(j).getItem() instanceof SwordItem)
                     && !mc.player.getInventory().getStack(j).getItem().getComponents().contains(DataComponentTypes.FOOD)) {
                  InvUtils.move().from(i).to(j);
                  InvUtils.switchToSlot(j);
                  return;
               }
            }

            InvUtils.move().from(i).to(mc.player.getInventory().selectedSlot);
            return;
         }
      }
   }

   public boolean isSupplyFull(AutoMineSand.SupplyType supplyType, int num) {
      int count = 0;
      if (supplyType != AutoMineSand.SupplyType.Food) {
         return supplyType == AutoMineSand.SupplyType.Totem ? InvUtils.find(Items.TOTEM_OF_UNDYING).count() >= num : false;
      } else {
         for (int i = 0; i < 36; i++) {
            if (mc.player.getInventory().getStack(i).getItem() == Items.BREAD
               || mc.player.getInventory().getStack(i).getItem() == Items.GOLDEN_CARROT) {
               count += mc.player.getInventory().getStack(i).getCount();
            }
         }

         return count >= num;
      }
   }

   public AutoMineSand.SupplyType isShouldSupply() {
      int foot_count = 0;

      for (int i = 0; i < 36; i++) {
         if (mc.player.getInventory().getStack(i).getItem() == Items.BREAD
            || mc.player.getInventory().getStack(i).getItem() == Items.GOLDEN_CARROT) {
            foot_count += mc.player.getInventory().getStack(i).getCount();
         }
      }

      if (foot_count < this.foot_counts.get()) {
         return AutoMineSand.SupplyType.Food;
      } else if (!this.findShovel()) {
         return AutoMineSand.SupplyType.Shovel;
      } else {
         boolean should_supply_shovel = true;

         for (int ix = 0; ix < 36; ix++) {
            if (mc.player.getInventory().getStack(ix).getItem() instanceof ShovelItem
               && mc.player.getInventory().getStack(ix).getMaxDamage() - mc.player.getInventory().getStack(ix).getDamage()
                  > this.shovel_demage.get()) {
               should_supply_shovel = false;
            }
         }

         if (should_supply_shovel) {
            return AutoMineSand.SupplyType.Shovel;
         } else {
            return InvUtils.find(Items.TOTEM_OF_UNDYING).count() < this.totem_nums.get() ? AutoMineSand.SupplyType.Totem : null;
         }
      }
   }

   public int getPickaxe() {
      for (int i = 0; i < 36; i++) {
         if (mc.player.getInventory().getStack(i).getItem() instanceof PickaxeItem) {
            return i;
         }
      }

      return -1;
   }

   public boolean findShovel() {
      for (int i = 0; i < 36; i++) {
         if (mc.player.getInventory().getStack(i).getItem() instanceof ShovelItem) {
            return true;
         }
      }

      return false;
   }

   public boolean isCanRemove() {
      for (int i = 27; i < 63; i++) {
         if (((Slot)mc.player.currentScreenHandler.slots.get(i)).getStack().getItem() == Items.SAND) {
            for (int j = 0; j < 27; j++) {
               Item item = ((Slot)mc.player.currentScreenHandler.slots.get(j)).getStack().getItem();
               if (item == Items.AIR || item == Items.SAND && ((Slot)mc.player.currentScreenHandler.slots.get(j)).getStack().getCount() < 64) {
                  return true;
               }
            }
         }
      }

      return false;
   }

   private boolean isHaveEmptyStack() {
      for (int i = 0; i < 36; i++) {
         if (mc.player.getInventory().getStack(i).isEmpty()
            || mc.player.getInventory().getStack(i).getItem() == Items.SAND && mc.player.getInventory().getStack(i).getCount() < 64) {
            return true;
         }
      }

      return false;
   }

   private boolean isShulckerBoxFull() {
      for (int i = 0; i < 27; i++) {
         Item item = ((Slot)mc.player.currentScreenHandler.slots.get(i)).getStack().getItem();
         if (item == Items.AIR || item == Items.SAND && ((Slot)mc.player.currentScreenHandler.slots.get(i)).getStack().getCount() < 64) {
            return false;
         }
      }

      return true;
   }

   public BlockPos getPlacePose() {
      BlockPos playerPose = mc.player.getBlockPos();
      Vec3d eyePos = mc.player.getEyePos();
      double maxDistance = 0.0;
      Direction maxDir = Direction.NORTH;

      for (Direction direction : Direction.values()) {
         if (direction != Direction.UP && direction != Direction.DOWN) {
            BlockPos blockPos = playerPose.offset(direction);
            double getDistance = eyePos.distanceTo(
               new Vec3d(
                  blockPos.getX() + CompoundNbtTagArgumentType$ConstantPool.const_1NtW6hDwaQ5aUuM,
                  blockPos.getY() + AccessFlag$ConstantPool.const_arCBFeZGGKDLvxb,
                  blockPos.getZ() + Mesh$Attrib$ConstantPool.const_AmTeOJf9oIFAufA
               )
            );
            if (getDistance > maxDistance) {
               maxDir = direction;
            }
         }
      }

      return playerPose.offset(maxDir);
   }

   static enum Status {
      FindSupplyBox,
      mineSand,
      closeShulcker,
      collectSandToShulcker,
      SupplyMove,
      Supply,
      SetSupplyPos,
      Finish;
   }

   static enum SupplyType {
      Food,
      Totem,
      Helmet,
      Chestplate,
      Leggings,
      Boots,
      Shovel;
   }
}
