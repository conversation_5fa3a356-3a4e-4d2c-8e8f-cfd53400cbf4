plugins {
    id 'java'
    id("io.freefair.lombok") version "8.14"

}

group = 'org.plexpt'
version = '1.0'

repositories {
    mavenCentral()
    maven {
        name = "fb-maven"
        url = uri("https://maven.fabricmc.net/")
    }
}

dependencies {
//    implementation 'net.fabricmc:tiny-mappings-parser:0.3.0+build.17'
//    implementation 'net.fabricmc:tiny-remapper:0.9.0'
    implementation 'com.google.code.gson:gson:2.13.1'

    implementation 'net.fabricmc:stitch:0.6.2'
    implementation "net.fabricmc:mapping-io:0.7.1"
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'

    implementation 'com.google.code.gson:gson:2.10.1'

}

test {
    useJUnitPlatform()
}
